'use client'

import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Send, 
  Image as ImageIcon, 
  Link as LinkIcon,
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Code,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Upload,
  Search,
  Tag,
  Calendar,
  Globe,
  BarChart3,
  Target,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  XCircle,
  Zap,
  Clock,
  FileText,
  Settings
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import Link from 'next/link';

const categories = [
  { id: 1, name: 'AI & Machine Learning', color: '#8B5CF6' },
  { id: 2, name: 'Web Development', color: '#3B82F6' },
  { id: 3, name: 'Backend', color: '#10B981' },
  { id: 4, name: 'DevOps', color: '#F59E0B' },
  { id: 5, name: 'Database', color: '#EF4444' },
  { id: 6, name: 'Mobile Development', color: '#8B5CF6' }
];

const editorTools = [
  { icon: Bold, name: 'Bold', shortcut: 'Ctrl+B' },
  { icon: Italic, name: 'Italic', shortcut: 'Ctrl+I' },
  { icon: Underline, name: 'Underline', shortcut: 'Ctrl+U' },
  { icon: Heading1, name: 'Heading 1', shortcut: 'Ctrl+1' },
  { icon: Heading2, name: 'Heading 2', shortcut: 'Ctrl+2' },
  { icon: Heading3, name: 'Heading 3', shortcut: 'Ctrl+3' },
  { icon: List, name: 'Bullet List', shortcut: 'Ctrl+Shift+8' },
  { icon: ListOrdered, name: 'Numbered List', shortcut: 'Ctrl+Shift+7' },
  { icon: Quote, name: 'Quote', shortcut: 'Ctrl+Shift+9' },
  { icon: Code, name: 'Code Block', shortcut: 'Ctrl+`' },
  { icon: LinkIcon, name: 'Link', shortcut: 'Ctrl+K' },
  { icon: ImageIcon, name: 'Image', shortcut: 'Ctrl+Shift+I' }
];

interface SEOAnalysis {
  score: number;
  title: { status: 'good' | 'warning' | 'error'; message: string };
  metaDescription: { status: 'good' | 'warning' | 'error'; message: string };
  readability: { status: 'good' | 'warning' | 'error'; message: string };
  keywords: { status: 'good' | 'warning' | 'error'; message: string };
  headings: { status: 'good' | 'warning' | 'error'; message: string };
  images: { status: 'good' | 'warning' | 'error'; message: string };
}

export default function BlogEditor() {
  const router = useRouter();
  const contentRef = useRef<HTMLTextAreaElement>(null);
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    featuredImage: '',
    status: 'draft',
    category: '',
    tags: [] as string[],
    
    // SEO
    metaTitle: '',
    metaDescription: '',
    keywords: '',
    canonicalUrl: '',
    
    // Publishing
    publishDate: '',
    author: 'Khiwniti',
    readingTime: 0,
    
    // Settings
    allowComments: true,
    featured: false,
    newsletter: false
  });

  const [activeTab, setActiveTab] = useState('content');
  const [wordCount, setWordCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);

  const [seoAnalysis, setSeoAnalysis] = useState<SEOAnalysis>({
    score: 0,
    title: { status: 'error', message: 'Title is required' },
    metaDescription: { status: 'error', message: 'Meta description is required' },
    readability: { status: 'warning', message: 'Add more content for better readability' },
    keywords: { status: 'error', message: 'Add focus keywords' },
    headings: { status: 'warning', message: 'Add headings to improve structure' },
    images: { status: 'warning', message: 'Add alt text to images' }
  });

  const updateContent = (content: string) => {
    setFormData(prev => ({ ...prev, content }));
    
    // Calculate word count and reading time
    const words = content.trim().split(/\s+/).length;
    setWordCount(words);
    setReadingTime(Math.ceil(words / 200)); // Average reading speed: 200 wpm
    
    // Update reading time in form data
    setFormData(prev => ({ ...prev, readingTime: Math.ceil(words / 200) }));
    
    // Analyze SEO
    analyzeSEO();
  };

  const updateTitle = (title: string) => {
    setFormData(prev => ({ 
      ...prev, 
      title,
      slug: title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
      metaTitle: prev.metaTitle || title
    }));
    analyzeSEO();
  };

  const analyzeSEO = () => {
    let score = 0;
    const newAnalysis = { ...seoAnalysis };

    // Title analysis
    if (formData.title.length === 0) {
      newAnalysis.title = { status: 'error', message: 'Title is required' };
    } else if (formData.title.length < 30) {
      newAnalysis.title = { status: 'warning', message: 'Title could be longer (30+ chars recommended)' };
      score += 10;
    } else if (formData.title.length > 60) {
      newAnalysis.title = { status: 'warning', message: 'Title might be too long (60+ chars)' };
      score += 10;
    } else {
      newAnalysis.title = { status: 'good', message: 'Title length is optimal' };
      score += 20;
    }

    // Meta description
    if (formData.metaDescription.length === 0) {
      newAnalysis.metaDescription = { status: 'error', message: 'Meta description is required' };
    } else if (formData.metaDescription.length < 120) {
      newAnalysis.metaDescription = { status: 'warning', message: 'Meta description could be longer' };
      score += 10;
    } else if (formData.metaDescription.length > 160) {
      newAnalysis.metaDescription = { status: 'warning', message: 'Meta description might be too long' };
      score += 10;
    } else {
      newAnalysis.metaDescription = { status: 'good', message: 'Meta description length is optimal' };
      score += 20;
    }

    // Content readability
    if (wordCount < 300) {
      newAnalysis.readability = { status: 'error', message: 'Content too short (300+ words recommended)' };
    } else if (wordCount < 500) {
      newAnalysis.readability = { status: 'warning', message: 'Consider adding more content' };
      score += 10;
    } else {
      newAnalysis.readability = { status: 'good', message: 'Content length is good' };
      score += 20;
    }

    // Keywords
    if (!formData.keywords) {
      newAnalysis.keywords = { status: 'error', message: 'Add focus keywords' };
    } else {
      newAnalysis.keywords = { status: 'good', message: 'Keywords added' };
      score += 20;
    }

    // Headings
    const headingCount = (formData.content.match(/^#{1,6}\s/gm) || []).length;
    if (headingCount === 0) {
      newAnalysis.headings = { status: 'warning', message: 'Add headings to improve structure' };
      score += 5;
    } else {
      newAnalysis.headings = { status: 'good', message: 'Good heading structure' };
      score += 20;
    }

    // Images
    const imageCount = (formData.content.match(/!\[.*?\]\(.*?\)/g) || []).length;
    if (imageCount === 0 && !formData.featuredImage) {
      newAnalysis.images = { status: 'warning', message: 'Consider adding images' };
      score += 5;
    } else {
      newAnalysis.images = { status: 'good', message: 'Images included' };
      score += 20;
    }

    newAnalysis.score = Math.min(score, 100);
    setSeoAnalysis(newAnalysis);
  };

  const addTag = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const insertFormatting = (format: string) => {
    if (!contentRef.current) return;
    
    const textarea = contentRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    
    let replacement = '';
    
    switch (format) {
      case 'bold':
        replacement = `**${selectedText || 'bold text'}**`;
        break;
      case 'italic':
        replacement = `*${selectedText || 'italic text'}*`;
        break;
      case 'heading1':
        replacement = `# ${selectedText || 'Heading 1'}`;
        break;
      case 'heading2':
        replacement = `## ${selectedText || 'Heading 2'}`;
        break;
      case 'heading3':
        replacement = `### ${selectedText || 'Heading 3'}`;
        break;
      case 'link':
        replacement = `[${selectedText || 'link text'}](url)`;
        break;
      case 'image':
        replacement = `![${selectedText || 'alt text'}](image-url)`;
        break;
      case 'code':
        replacement = `\`\`\`\n${selectedText || 'code here'}\n\`\`\``;
        break;
      default:
        replacement = selectedText;
    }
    
    const newContent = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
    updateContent(newContent);
    
    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + replacement.length, start + replacement.length);
    }, 0);
  };

  const handleSave = async (status: 'draft' | 'published') => {
    try {
      const postData = {
        ...formData,
        status,
        publishDate: status === 'published' ? new Date().toISOString() : formData.publishDate
      };
      
      console.log('Saving post:', postData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      router.push('/admin/blog');
    } catch (error) {
      console.error('Error saving post:', error);
    }
  };

  const getStatusIcon = (status: 'good' | 'warning' | 'error') => {
    switch (status) {
      case 'good': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getSEOScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/admin/blog">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Posts
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Create Blog Post</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Write and optimize your content for maximum impact
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => handleSave('draft')}>
              <Save className="w-4 h-4 mr-2" />
              Save Draft
            </Button>
            <Button variant="outline">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button 
              onClick={() => handleSave('published')}
              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
            >
              <Send className="w-4 h-4 mr-2" />
              Publish
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            <Card>
              <CardContent className="pt-6">
                {/* Title */}
                <div className="space-y-2 mb-6">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => updateTitle(e.target.value)}
                    placeholder="Enter your blog post title..."
                    className="text-2xl font-bold border-none p-0 shadow-none focus-visible:ring-0"
                  />
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>Slug: /{formData.slug}</span>
                    <span>{formData.title.length}/60 characters</span>
                  </div>
                </div>

                {/* Content Editor */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Content</Label>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <span>{wordCount} words</span>
                      <span>•</span>
                      <span>{readingTime} min read</span>
                    </div>
                  </div>
                  
                  {/* Editor Toolbar */}
                  <TooltipProvider>
                    <div className="flex items-center gap-1 p-2 border rounded-lg bg-gray-50 dark:bg-gray-800">
                      {editorTools.map((tool, index) => {
                        const Icon = tool.icon;
                        return (
                          <Tooltip key={index}>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => insertFormatting(tool.name.toLowerCase().replace(' ', ''))}
                              >
                                <Icon className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{tool.name} ({tool.shortcut})</p>
                            </TooltipContent>
                          </Tooltip>
                        );
                      })}
                    </div>
                  </TooltipProvider>

                  <Textarea
                    ref={contentRef}
                    value={formData.content}
                    onChange={(e) => updateContent(e.target.value)}
                    placeholder="Write your blog post content here..."
                    className="min-h-[500px] font-mono"
                  />
                </div>
              </CardContent>
            </Card>

            {/* SEO & Meta Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="w-5 h-5" />
                  SEO & Meta Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    value={formData.metaTitle}
                    onChange={(e) => setFormData(prev => ({ ...prev, metaTitle: e.target.value }))}
                    placeholder="SEO optimized title..."
                  />
                  <div className="text-sm text-gray-500">{formData.metaTitle.length}/60 characters</div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    value={formData.metaDescription}
                    onChange={(e) => setFormData(prev => ({ ...prev, metaDescription: e.target.value }))}
                    placeholder="Brief description for search engines..."
                    rows={3}
                  />
                  <div className="text-sm text-gray-500">{formData.metaDescription.length}/160 characters</div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="keywords">Focus Keywords</Label>
                  <Input
                    id="keywords"
                    value={formData.keywords}
                    onChange={(e) => setFormData(prev => ({ ...prev, keywords: e.target.value }))}
                    placeholder="keyword1, keyword2, keyword3"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="canonicalUrl">Canonical URL (optional)</Label>
                  <Input
                    id="canonicalUrl"
                    value={formData.canonicalUrl}
                    onChange={(e) => setFormData(prev => ({ ...prev, canonicalUrl: e.target.value }))}
                    placeholder="https://example.com/original-post"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* SEO Score */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  SEO Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className={`text-3xl font-bold ${getSEOScoreColor(seoAnalysis.score)}`}>
                    {seoAnalysis.score}/100
                  </div>
                  <Progress value={seoAnalysis.score} className="mt-2" />
                </div>
                
                <div className="space-y-2">
                  {Object.entries(seoAnalysis).map(([key, analysis]) => {
                    if (key === 'score') return null;
                    return (
                      <div key={key} className="flex items-center gap-2 text-sm">
                        {getStatusIcon(analysis.status)}
                        <span className="flex-1">{analysis.message}</span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Publishing Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Publishing
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Category</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {formData.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                        {tag} ×
                      </Badge>
                    ))}
                  </div>
                  <Input
                    placeholder="Add tags (press Enter)"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addTag(e.currentTarget.value);
                        e.currentTarget.value = '';
                      }
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="featuredImage">Featured Image URL</Label>
                  <Input
                    id="featuredImage"
                    value={formData.featuredImage}
                    onChange={(e) => setFormData(prev => ({ ...prev, featuredImage: e.target.value }))}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="featured">Featured Post</Label>
                    <Switch
                      id="featured"
                      checked={formData.featured}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="comments">Allow Comments</Label>
                    <Switch
                      id="comments"
                      checked={formData.allowComments}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowComments: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="newsletter">Include in Newsletter</Label>
                    <Switch
                      id="newsletter"
                      checked={formData.newsletter}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, newsletter: checked }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Quick Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Word Count</span>
                  <span className="font-medium">{wordCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Reading Time</span>
                  <span className="font-medium">{readingTime} min</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Character Count</span>
                  <span className="font-medium">{formData.content.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">SEO Score</span>
                  <span className={`font-medium ${getSEOScoreColor(seoAnalysis.score)}`}>
                    {seoAnalysis.score}/100
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}