# Testing Guide

Comprehensive testing guide for the getintheq.space platform, covering unit testing, integration testing, end-to-end testing, performance testing, and testing best practices for enterprise-level quality assurance.

## Table of Contents

- [Testing Overview](#testing-overview)
- [Testing Strategy](#testing-strategy)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [End-to-End Testing](#end-to-end-testing)
- [Performance Testing](#performance-testing)
- [Security Testing](#security-testing)
- [API Testing](#api-testing)
- [Database Testing](#database-testing)
- [Frontend Testing](#frontend-testing)
- [Test Automation](#test-automation)
- [Testing Best Practices](#testing-best-practices)

## Testing Overview

The getintheq.space platform employs a comprehensive testing strategy to ensure code quality, functionality, performance, and security across all layers of the application.

### Testing Pyramid

```mermaid
graph TB
    subgraph "Testing Pyramid"
        E2E[End-to-End Tests<br/>~5%<br/>High Cost, High Confidence]
        INT[Integration Tests<br/>~15%<br/>Medium Cost, Medium Confidence]
        UNIT[Unit Tests<br/>~80%<br/>Low Cost, Low-Medium Confidence]
    end
    
    subgraph "Test Types"
        API[API Tests]
        UI[UI Tests]
        PERF[Performance Tests]
        SEC[Security Tests]
    end
    
    UNIT --> INT
    INT --> E2E
    
    API --> INT
    UI --> E2E
    PERF --> E2E
    SEC --> INT
```

### Testing Principles

| Principle | Description | Implementation |
|-----------|-------------|----------------|
| **Fast Feedback** | Tests provide quick feedback on code changes | Unit tests run in < 5s, Integration tests < 30s |
| **Reliability** | Tests are deterministic and repeatable | Proper test isolation, no flaky tests |
| **Maintainability** | Tests are easy to understand and modify | Clear naming, proper organization |
| **Coverage** | Adequate test coverage across all layers | >80% unit test coverage, critical path coverage |
| **Automation** | Tests run automatically in CI/CD pipeline | All tests automated, no manual testing in CI |

## Testing Strategy

### Test Classification

```typescript
// Testing strategy configuration
export const TESTING_STRATEGY = {
  unit: {
    target: '80%',
    tools: ['Jest', 'React Testing Library', 'MSW'],
    scope: 'Individual functions, components, modules',
    frequency: 'Every commit'
  },
  
  integration: {
    target: '70%',
    tools: ['Jest', 'Supertest', 'Test Database'],
    scope: 'API endpoints, service interactions',
    frequency: 'Every PR'
  },
  
  e2e: {
    target: 'Critical user journeys',
    tools: ['Playwright', 'Cypress'],
    scope: 'Complete user workflows',
    frequency: 'Pre-deployment'
  },
  
  performance: {
    target: 'SLA compliance',
    tools: ['Lighthouse', 'K6', 'Artillery'],
    scope: 'Load, stress, performance',
    frequency: 'Weekly + releases'
  },
  
  security: {
    target: 'Zero vulnerabilities',
    tools: ['OWASP ZAP', 'Snyk', 'SonarQube'],
    scope: 'Security vulnerabilities',
    frequency: 'Every PR + weekly'
  }
} as const;
```

### Test Environment Matrix

| Environment | Purpose | Data | Deployment |
|-------------|---------|------|------------|
| **Local** | Development testing | Mock/fixture data | Developer machine |
| **CI** | Automated testing | Test database | GitHub Actions |
| **Staging** | Pre-production testing | Sanitized production data | Staging server |
| **Production** | Monitoring tests | Live data | Production environment |

## Unit Testing

### Jest Configuration

```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'app/**/*.{ts,tsx}',
    'shared/**/*.{ts,tsx}',
    '!app/**/*.d.ts',
    '!app/**/*.stories.{ts,tsx}',
    '!app/**/*.test.{ts,tsx}',
    '!app/globals.css',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/app/$1',
    '^@shared/(.*)$': '<rootDir>/shared/$1',
  },
  testMatch: [
    '**/__tests__/**/*.(ts|tsx)',
    '**/*.(test|spec).(ts|tsx)',
  ],
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/e2e/',
  ],
  transformIgnorePatterns: [
    '/node_modules/(?!(.*\\.mjs$|.*\\.ts$|.*\\.tsx$))',
  ],
};

module.exports = createJestConfig(customJestConfig);
```

### Unit Test Examples

#### Testing Utility Functions

```typescript
// app/lib/utils.test.ts
import { cn, formatDate, generateSlug } from './utils';

describe('Utils', () => {
  describe('cn (className utility)', () => {
    it('should merge class names correctly', () => {
      expect(cn('base', 'additional')).toBe('base additional');
    });

    it('should handle conditional classes', () => {
      expect(cn('base', true && 'conditional', false && 'not-included'))
        .toBe('base conditional');
    });

    it('should handle Tailwind conflicts', () => {
      expect(cn('px-4', 'px-6')).toBe('px-6');
    });
  });

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      expect(formatDate(date)).toBe('January 15, 2024');
    });

    it('should handle different formats', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      expect(formatDate(date, 'short')).toBe('Jan 15, 2024');
    });

    it('should throw error for invalid date', () => {
      expect(() => formatDate(new Date('invalid'))).toThrow();
    });
  });

  describe('generateSlug', () => {
    it('should generate URL-friendly slug', () => {
      expect(generateSlug('Hello World!')).toBe('hello-world');
    });

    it('should handle special characters', () => {
      expect(generateSlug('Hello & World @ 2024')).toBe('hello-world-2024');
    });

    it('should handle empty string', () => {
      expect(generateSlug('')).toBe('');
    });
  });
});
```

#### Testing React Components

```typescript
// app/components/button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './button';

describe('Button', () => {
  it('should render with default props', () => {
    render(<Button>Click me</Button>);
    
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-primary');
  });

  it('should render different variants', () => {
    render(<Button variant="secondary">Secondary</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-secondary');
  });

  it('should handle click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when loading', () => {
    render(<Button loading>Loading</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('should forward ref correctly', () => {
    const ref = React.createRef<HTMLButtonElement>();
    render(<Button ref={ref}>Button</Button>);
    
    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
  });
});
```

#### Testing Custom Hooks

```typescript
// app/hooks/use-auth.test.ts
import { renderHook, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuth } from './use-auth';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useAuth', () => {
  it('should return initial auth state', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(result.current.user).toBeNull();
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should handle login successfully', async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.login('<EMAIL>', 'password');
    });

    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user).toEqual(
      expect.objectContaining({
        email: '<EMAIL>',
      })
    );
  });

  it('should handle login failure', async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      try {
        await result.current.login('<EMAIL>', 'wrong');
      } catch (error) {
        // Expected error
      }
    });

    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.user).toBeNull();
  });
});
```

### Mocking Strategies

```typescript
// app/lib/__mocks__/database.ts
export const mockDb = {
  query: {
    users: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
    },
    blogPosts: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
    },
  },
  insert: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

// app/lib/__mocks__/auth.ts
export const mockAuth = {
  authenticate: jest.fn(),
  generateToken: jest.fn(),
  verifyToken: jest.fn(),
};

// Test setup with MSW (Mock Service Worker)
// app/lib/test-utils/msw-handlers.ts
import { rest } from 'msw';

export const handlers = [
  rest.get('/api/user', (req, res, ctx) => {
    return res(
      ctx.json({
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
      })
    );
  }),

  rest.post('/api/auth/login', (req, res, ctx) => {
    const { email, password } = req.body as any;
    
    if (email === '<EMAIL>' && password === 'password') {
      return res(
        ctx.json({
          user: { id: '1', email, name: 'Test User' },
          token: 'mock-jwt-token',
        })
      );
    }
    
    return res(
      ctx.status(401),
      ctx.json({ error: 'Invalid credentials' })
    );
  }),

  rest.get('/api/blog/posts', (req, res, ctx) => {
    return res(
      ctx.json([
        {
          id: '1',
          title: 'Test Post',
          content: 'Test content',
          createdAt: '2024-01-01T00:00:00Z',
        },
      ])
    );
  }),
];
```

## Integration Testing

### API Integration Tests

```typescript
// app/api/blog/posts/route.test.ts
import { NextRequest } from 'next/server';
import { GET, POST } from './route';
import { db } from '@/lib/database';
import { createMockRequest } from '@/lib/test-utils';

// Mock database
jest.mock('@/lib/database');
const mockDb = db as jest.Mocked<typeof db>;

describe('/api/blog/posts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/blog/posts', () => {
    it('should return blog posts successfully', async () => {
      const mockPosts = [
        {
          id: '1',
          title: 'Test Post',
          content: 'Test content',
          authorId: 'author1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockDb.query.blogPosts.findMany.mockResolvedValue(mockPosts);

      const request = createMockRequest('GET', '/api/blog/posts');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.posts).toEqual(mockPosts);
      expect(mockDb.query.blogPosts.findMany).toHaveBeenCalledWith({
        with: { author: true },
        orderBy: expect.any(Function),
        limit: 20,
      });
    });

    it('should handle database errors', async () => {
      mockDb.query.blogPosts.findMany.mockRejectedValue(
        new Error('Database error')
      );

      const request = createMockRequest('GET', '/api/blog/posts');
      const response = await GET(request);

      expect(response.status).toBe(500);
    });

    it('should handle pagination', async () => {
      const request = createMockRequest('GET', '/api/blog/posts?page=2&limit=10');
      await GET(request);

      expect(mockDb.query.blogPosts.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          limit: 10,
          offset: 10,
        })
      );
    });
  });

  describe('POST /api/blog/posts', () => {
    it('should create blog post successfully', async () => {
      const mockUser = { id: 'user1', role: 'admin' };
      const newPost = {
        title: 'New Post',
        content: 'New content',
        authorId: 'user1',
      };

      mockDb.insert.mockResolvedValue([{ 
        id: '1', 
        ...newPost, 
        createdAt: new Date(),
        updatedAt: new Date() 
      }]);

      const request = createMockRequest('POST', '/api/blog/posts', {
        body: newPost,
        user: mockUser,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.post).toEqual(expect.objectContaining(newPost));
    });

    it('should validate required fields', async () => {
      const request = createMockRequest('POST', '/api/blog/posts', {
        body: { title: 'Missing content' },
      });

      const response = await POST(request);
      expect(response.status).toBe(400);
    });

    it('should require authentication', async () => {
      const request = createMockRequest('POST', '/api/blog/posts', {
        body: { title: 'Test', content: 'Test' },
      });

      const response = await POST(request);
      expect(response.status).toBe(401);
    });
  });
});
```

### Database Integration Tests

```typescript
// app/lib/repositories/user-repository.test.ts
import { UserRepository } from './user-repository';
import { setupTestDatabase, cleanupTestDatabase } from '@/lib/test-utils/database';

describe('UserRepository', () => {
  let userRepository: UserRepository;

  beforeAll(async () => {
    await setupTestDatabase();
    userRepository = new UserRepository();
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  beforeEach(async () => {
    await cleanupTestData();
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashedpassword',
      };

      const user = await userRepository.create(userData);

      expect(user).toEqual(
        expect.objectContaining({
          id: expect.any(String),
          name: userData.name,
          email: userData.email,
          createdAt: expect.any(Date),
        })
      );
    });

    it('should throw error for duplicate email', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashedpassword',
      };

      await userRepository.create(userData);

      await expect(userRepository.create(userData)).rejects.toThrow(
        'User with this email already exists'
      );
    });
  });

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashedpassword',
      };

      const createdUser = await userRepository.create(userData);
      const foundUser = await userRepository.findByEmail(userData.email);

      expect(foundUser).toEqual(createdUser);
    });

    it('should return null for non-existent email', async () => {
      const user = await userRepository.findByEmail('<EMAIL>');
      expect(user).toBeNull();
    });
  });

  describe('updateUser', () => {
    it('should update user successfully', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashedpassword',
      };

      const createdUser = await userRepository.create(userData);
      const updates = { name: 'Jane Doe' };
      
      const updatedUser = await userRepository.update(createdUser.id, updates);

      expect(updatedUser.name).toBe(updates.name);
      expect(updatedUser.updatedAt).not.toEqual(createdUser.updatedAt);
    });
  });
});
```

## End-to-End Testing

### Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/e2e-results.xml' }],
  ],
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  webServer: {
    command: 'npm run start',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Examples

```typescript
// e2e/user-authentication.spec.ts
import { test, expect } from '@playwright/test';

test.describe('User Authentication', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    // Navigate to login page
    await page.click('[data-testid="login-button"]');
    await expect(page).toHaveURL('/auth/login');

    // Fill login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    
    // Submit form
    await page.click('[data-testid="submit-button"]');

    // Verify successful login
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    await expect(page.locator('text=Welcome back')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.click('[data-testid="login-button"]');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="submit-button"]');

    await expect(page.locator('[data-testid="error-message"]')).toContainText(
      'Invalid email or password'
    );
    await expect(page).toHaveURL('/auth/login');
  });

  test('should logout successfully', async ({ page }) => {
    // Login first
    await page.goto('/auth/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="submit-button"]');

    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');

    // Verify logout
    await expect(page).toHaveURL('/');
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
  });

  test('should redirect to login when accessing protected route', async ({ page }) => {
    await page.goto('/admin');
    await expect(page).toHaveURL('/auth/login?redirect=/admin');
  });
});
```

```typescript
// e2e/blog-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Blog Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('/auth/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'admin123');
    await page.click('[data-testid="submit-button"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should create new blog post', async ({ page }) => {
    // Navigate to blog management
    await page.click('[data-testid="blog-menu"]');
    await page.click('[data-testid="new-post-button"]');

    // Fill post form
    await page.fill('[data-testid="title-input"]', 'Test Blog Post');
    await page.fill('[data-testid="content-editor"]', 'This is test content for the blog post.');
    
    // Select category
    await page.selectOption('[data-testid="category-select"]', 'technology');
    
    // Add tags
    await page.fill('[data-testid="tags-input"]', 'test, blog, automation');
    
    // Save as draft
    await page.click('[data-testid="save-draft-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toContainText(
      'Post saved as draft'
    );
    
    // Verify in posts list
    await page.goto('/admin/blog');
    await expect(page.locator('text=Test Blog Post')).toBeVisible();
    await expect(page.locator('[data-testid="post-status"]')).toContainText('Draft');
  });

  test('should publish blog post', async ({ page }) => {
    // Create a draft first
    await page.goto('/admin/blog/new');
    await page.fill('[data-testid="title-input"]', 'Post to Publish');
    await page.fill('[data-testid="content-editor"]', 'Content to publish.');
    await page.click('[data-testid="save-draft-button"]');

    // Navigate back to posts list
    await page.goto('/admin/blog');
    
    // Find and edit the post
    await page.click('[data-testid="edit-post-button"]');
    
    // Publish the post
    await page.click('[data-testid="publish-button"]');
    
    // Confirm publication
    await page.click('[data-testid="confirm-publish"]');

    // Verify publication
    await expect(page.locator('[data-testid="success-message"]')).toContainText(
      'Post published successfully'
    );
    
    // Verify on public blog page
    await page.goto('/blog');
    await expect(page.locator('text=Post to Publish')).toBeVisible();
  });

  test('should delete blog post', async ({ page }) => {
    // Go to blog management
    await page.goto('/admin/blog');
    
    // Click delete on first post
    await page.click('[data-testid="delete-post-button"]');
    
    // Confirm deletion
    await page.click('[data-testid="confirm-delete"]');

    // Verify deletion
    await expect(page.locator('[data-testid="success-message"]')).toContainText(
      'Post deleted successfully'
    );
  });
});
```

### Page Object Model

```typescript
// e2e/page-objects/login-page.ts
import { Page, Locator } from '@playwright/test';

export class LoginPage {
  readonly page: Page;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly submitButton: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailInput = page.locator('[data-testid="email-input"]');
    this.passwordInput = page.locator('[data-testid="password-input"]');
    this.submitButton = page.locator('[data-testid="submit-button"]');
    this.errorMessage = page.locator('[data-testid="error-message"]');
  }

  async goto() {
    await this.page.goto('/auth/login');
  }

  async login(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.submitButton.click();
  }

  async getErrorMessage() {
    return await this.errorMessage.textContent();
  }
}

// e2e/page-objects/dashboard-page.ts
export class DashboardPage {
  readonly page: Page;
  readonly userMenu: Locator;
  readonly logoutButton: Locator;
  readonly welcomeMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.userMenu = page.locator('[data-testid="user-menu"]');
    this.logoutButton = page.locator('[data-testid="logout-button"]');
    this.welcomeMessage = page.locator('text=Welcome back');
  }

  async logout() {
    await this.userMenu.click();
    await this.logoutButton.click();
  }

  async isLoggedIn() {
    return await this.userMenu.isVisible();
  }
}
```

## Performance Testing

### Load Testing with K6

```javascript
// scripts/performance/load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
    errors: ['rate<0.1'],
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

export default function() {
  // Test home page
  let response = http.get(`${BASE_URL}/`);
  check(response, {
    'home page status is 200': (r) => r.status === 200,
    'home page loads in <500ms': (r) => r.timings.duration < 500,
  }) || errorRate.add(1);

  sleep(1);

  // Test API endpoint
  response = http.get(`${BASE_URL}/api/blog/posts`);
  check(response, {
    'API status is 200': (r) => r.status === 200,
    'API response time <200ms': (r) => r.timings.duration < 200,
    'API returns valid JSON': (r) => {
      try {
        JSON.parse(r.body);
        return true;
      } catch {
        return false;
      }
    },
  }) || errorRate.add(1);

  sleep(1);

  // Test blog page
  response = http.get(`${BASE_URL}/blog`);
  check(response, {
    'blog page status is 200': (r) => r.status === 200,
    'blog page loads in <1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);

  sleep(2);
}

export function handleSummary(data) {
  return {
    'performance-report.json': JSON.stringify(data, null, 2),
    'performance-report.html': htmlReport(data),
  };
}

function htmlReport(data) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Performance Test Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .metric { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; }
        .success { border-color: #28a745; }
        .warning { border-color: #ffc107; }
        .error { border-color: #dc3545; }
      </style>
    </head>
    <body>
      <h1>Performance Test Report</h1>
      <div class="metric ${data.metrics.http_req_duration.values.p95 < 500 ? 'success' : 'error'}">
        <strong>Response Time (p95):</strong> ${data.metrics.http_req_duration.values.p95.toFixed(2)}ms
      </div>
      <div class="metric ${data.metrics.http_req_failed.values.rate < 0.1 ? 'success' : 'error'}">
        <strong>Error Rate:</strong> ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%
      </div>
      <div class="metric">
        <strong>Total Requests:</strong> ${data.metrics.http_reqs.values.count}
      </div>
    </body>
    </html>
  `;
}
```

### Lighthouse Performance Testing

```javascript
// scripts/performance/lighthouse-test.js
const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');

async function runLighthouse(url) {
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
  
  const options = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance'],
    port: chrome.port,
  };
  
  const runnerResult = await lighthouse(url, options);
  await chrome.kill();
  
  return runnerResult;
}

async function runPerformanceTests() {
  const urls = [
    'http://localhost:3000',
    'http://localhost:3000/blog',
    'http://localhost:3000/about',
    'http://localhost:3000/contact',
  ];
  
  const results = [];
  
  for (const url of urls) {
    console.log(`Testing ${url}...`);
    const result = await runLighthouse(url);
    
    const report = JSON.parse(result.report);
    const performance = report.categories.performance;
    
    results.push({
      url,
      score: performance.score * 100,
      metrics: {
        fcp: report.audits['first-contentful-paint'].numericValue,
        lcp: report.audits['largest-contentful-paint'].numericValue,
        fid: report.audits['max-potential-fid'].numericValue,
        cls: report.audits['cumulative-layout-shift'].numericValue,
        ttfb: report.audits['server-response-time'].numericValue,
      },
    });
  }
  
  // Save results
  fs.writeFileSync(
    'performance-results.json',
    JSON.stringify(results, null, 2)
  );
  
  // Check thresholds
  const failedTests = results.filter(result => result.score < 90);
  
  if (failedTests.length > 0) {
    console.error('Performance tests failed:');
    failedTests.forEach(test => {
      console.error(`${test.url}: Score ${test.score}/100`);
    });
    process.exit(1);
  }
  
  console.log('All performance tests passed!');
}

runPerformanceTests().catch(console.error);
```

## Security Testing

### OWASP ZAP Integration

```bash
#!/bin/bash
# scripts/security/zap-test.sh

echo "Starting security tests with OWASP ZAP..."

# Start ZAP in daemon mode
docker run -d --name zap-test \
  -p 8080:8080 \
  owasp/zap2docker-stable:latest \
  zap.sh -daemon -host 0.0.0.0 -port 8080 -config api.addrs.addr.name=.* -config api.addrs.addr.regex=true

# Wait for ZAP to start
sleep 30

# Run baseline scan
docker exec zap-test zap-baseline.py \
  -t http://host.docker.internal:3000 \
  -J zap-report.json \
  -r zap-report.html

# Copy reports
docker cp zap-test:/zap/wrk/zap-report.json ./security-reports/
docker cp zap-test:/zap/wrk/zap-report.html ./security-reports/

# Cleanup
docker rm -f zap-test

echo "Security tests completed. Reports saved to ./security-reports/"
```

### Security Test Examples

```typescript
// app/lib/security/security.test.ts
import { validateInput, sanitizeHtml, hashPassword, verifyPassword } from './security';

describe('Security Functions', () => {
  describe('validateInput', () => {
    it('should reject malicious input', () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        "'; DROP TABLE users; --",
        '../../../etc/passwd',
        '<img src=x onerror=alert(1)>',
      ];

      maliciousInputs.forEach(input => {
        expect(() => validateInput(input)).toThrow();
      });
    });

    it('should accept safe input', () => {
      const safeInputs = [
        'Hello world',
        '<EMAIL>',
        'Valid content with numbers 123',
      ];

      safeInputs.forEach(input => {
        expect(() => validateInput(input)).not.toThrow();
      });
    });
  });

  describe('sanitizeHtml', () => {
    it('should remove dangerous HTML', () => {
      const dangerous = '<script>alert("xss")</script><p>Safe content</p>';
      const sanitized = sanitizeHtml(dangerous);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('<p>Safe content</p>');
    });

    it('should preserve safe HTML', () => {
      const safe = '<p>Hello <strong>world</strong></p>';
      const sanitized = sanitizeHtml(safe);
      
      expect(sanitized).toBe(safe);
    });
  });

  describe('Password Security', () => {
    it('should hash passwords securely', async () => {
      const password = 'testpassword123';
      const hash = await hashPassword(password);
      
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50);
      expect(hash).toContain('$');
    });

    it('should verify passwords correctly', async () => {
      const password = 'testpassword123';
      const hash = await hashPassword(password);
      
      const isValid = await verifyPassword(password, hash);
      const isInvalid = await verifyPassword('wrongpassword', hash);
      
      expect(isValid).toBe(true);
      expect(isInvalid).toBe(false);
    });
  });
});
```

## Test Automation

### GitHub Actions Test Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run unit tests
        run: npm run test:coverage
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/test

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright
        run: npx playwright install --with-deps
        
      - name: Build application
        run: npm run build
        
      - name: Start application
        run: npm start &
        
      - name: Wait for application
        run: npx wait-on http://localhost:3000
        
      - name: Run E2E tests
        run: npx playwright test
        
      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        
      - name: Start application
        run: npm start &
        
      - name: Wait for application
        run: npx wait-on http://localhost:3000
        
      - name: Run Lighthouse tests
        run: node scripts/performance/lighthouse-test.js
        
      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-results.json

  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Snyk security scan
        run: npx snyk test
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
          
      - name: Run npm audit
        run: npm audit --audit-level moderate
```

## Testing Best Practices

### Testing Guidelines

```typescript
// Testing best practices and guidelines
export const TESTING_BEST_PRACTICES = {
  naming: {
    testFiles: 'Use .test.ts or .spec.ts suffix',
    testCases: 'Describe what the test does, not how',
    examples: [
      '✅ should return user data when valid ID provided',
      '❌ test getUserById function',
    ],
  },
  
  structure: {
    arrange: 'Set up test data and conditions',
    act: 'Execute the function or action being tested',
    assert: 'Verify the expected outcome',
  },
  
  isolation: {
    independent: 'Each test should be independent',
    cleanup: 'Clean up after each test',
    noSharedState: 'Avoid shared state between tests',
  },
  
  coverage: {
    unit: 'Aim for 80%+ line coverage',
    integration: 'Cover critical user journeys',
    e2e: 'Cover happy path and error scenarios',
  },
  
  maintainability: {
    dryPrinciple: 'Use helper functions for common setup',
    readability: 'Tests should be easy to read and understand',
    updateability: 'Tests should be easy to update when code changes',
  },
} as const;
```

### Test Data Management

```typescript
// app/lib/test-utils/fixtures.ts
export const TEST_FIXTURES = {
  users: {
    validUser: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'user' as const,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    adminUser: {
      id: 'admin1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin' as const,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
  },
  
  blogPosts: {
    published: {
      id: 'post1',
      title: 'Published Post',
      content: 'This is a published blog post.',
      authorId: 'user1',
      status: 'published' as const,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    draft: {
      id: 'post2',
      title: 'Draft Post',
      content: 'This is a draft blog post.',
      authorId: 'user1',
      status: 'draft' as const,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
  },
} as const;

// Factory functions for creating test data
export class TestDataFactory {
  static createUser(overrides: Partial<User> = {}): User {
    return {
      ...TEST_FIXTURES.users.validUser,
      id: crypto.randomUUID(),
      ...overrides,
    };
  }
  
  static createBlogPost(overrides: Partial<BlogPost> = {}): BlogPost {
    return {
      ...TEST_FIXTURES.blogPosts.published,
      id: crypto.randomUUID(),
      ...overrides,
    };
  }
  
  static createUserWithPosts(
    userOverrides: Partial<User> = {},
    postCount: number = 2
  ): UserWithPosts {
    const user = this.createUser(userOverrides);
    const posts = Array.from({ length: postCount }, (_, i) =>
      this.createBlogPost({
        authorId: user.id,
        title: `Post ${i + 1}`,
      })
    );
    
    return { ...user, posts };
  }
}
```

### Custom Test Utilities

```typescript
// app/lib/test-utils/test-utils.tsx
import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '@/components/theme-provider';

// Create a custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light">
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Mock request helper
export function createMockRequest(
  method: string,
  url: string,
  options: {
    body?: any;
    headers?: Record<string, string>;
    user?: any;
  } = {}
): NextRequest {
  const request = new NextRequest(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    body: options.body ? JSON.stringify(options.body) : undefined,
  });

  // Add user to request for authentication tests
  if (options.user) {
    (request as any).user = options.user;
  }

  return request;
}

// Database test utilities
export async function setupTestDatabase() {
  // Setup test database connection
  // Run migrations
  // Seed initial data
}

export async function cleanupTestDatabase() {
  // Clean up test database
  // Close connections
}

export async function cleanupTestData() {
  // Clear test data between tests
  // Reset sequences
  // Clear caches
}
```

---

**Testing Guide Version**: 1.0.0  
**Last Updated**: 2024-08-14  
**Coverage Target**: >80% unit, >70% integration  
**Next Review**: 2024-11-14  
**Testing Contact**: <EMAIL>