"""
Serverless Python Backend for Portfolio
Supports Vercel, Cloudflare Workers, AWS Lambda, Google Cloud Functions
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, Field
import httpx
import os
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncio

# Import playground router
from playground import playground_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI
app = FastAPI(
    title="Portfolio API",
    description="Serverless Python backend for Khiw Nitithachot's portfolio",
    version="1.0.0",
    docs_url="/api/docs" if os.getenv("ENVIRONMENT") == "development" else None
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "https://getintheq.space",
        "https://*.vercel.app",
        "https://*.pages.dev"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer(auto_error=False)

# Include playground router
app.include_router(playground_router)

# Models
class GitHubStats(BaseModel):
    repos: int
    followers: int
    stars: int
    commits: int

class Project(BaseModel):
    id: str
    title: str
    description: str
    technologies: List[str]
    github_url: Optional[str] = None
    demo_url: Optional[str] = None
    image_url: Optional[str] = None
    stars: Optional[int] = None
    forks: Optional[int] = None
    language: Optional[str] = None
    updated_at: Optional[str] = None

class BlogPost(BaseModel):
    id: str
    title: str
    excerpt: str
    category: str
    read_time: int
    published_at: str
    slug: str

class ContactForm(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    email: EmailStr
    subject: str = Field(..., min_length=1, max_length=100)
    message: str = Field(..., min_length=10, max_length=1000)

class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None

# Environment variables
GITHUB_TOKEN = os.getenv("GITHUB_TOKEN")
GITHUB_USERNAME = os.getenv("GITHUB_USERNAME", "khiwniti")
RESEND_API_KEY = os.getenv("RESEND_API_KEY")
CONTACT_EMAIL = os.getenv("CONTACT_EMAIL", "<EMAIL>")

# Rate limiting (simple in-memory for serverless)
request_counts = {}

async def rate_limit_check(client_ip: str, limit: int = 100, window: int = 3600):
    """Simple rate limiting"""
    now = datetime.now()
    if client_ip not in request_counts:
        request_counts[client_ip] = []
    
    # Clean old requests
    request_counts[client_ip] = [
        req_time for req_time in request_counts[client_ip] 
        if now - req_time < timedelta(seconds=window)
    ]
    
    if len(request_counts[client_ip]) >= limit:
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    request_counts[client_ip].append(now)

# GitHub API client
async def github_request(endpoint: str) -> Dict[Any, Any]:
    """Make authenticated GitHub API request"""
    if not GITHUB_TOKEN:
        raise HTTPException(status_code=500, detail="GitHub token not configured")
    
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json",
        "User-Agent": "Portfolio-API/1.0"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"https://api.github.com/{endpoint}", headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"GitHub API error: {e}")
            raise HTTPException(status_code=502, detail="GitHub API unavailable")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

# API Routes

@app.get("/", response_model=APIResponse)
async def root():
    """Health check endpoint"""
    return APIResponse(
        success=True,
        message="Portfolio API is running",
        data={"version": "1.0.0", "timestamp": datetime.now().isoformat()}
    )

@app.get("/api/github/stats", response_model=GitHubStats)
async def get_github_stats():
    """Get GitHub user statistics"""
    try:
        # Fetch user data
        user_data = await github_request(f"users/{GITHUB_USERNAME}")
        
        # Fetch repositories
        repos_data = await github_request(f"users/{GITHUB_USERNAME}/repos?per_page=100")
        
        # Calculate total stars
        total_stars = sum(repo.get("stargazers_count", 0) for repo in repos_data)
        
        # Estimate commits (simplified)
        total_commits = 0
        for repo in repos_data[:10]:  # Limit to avoid rate limits
            try:
                commits_data = await github_request(
                    f"repos/{GITHUB_USERNAME}/{repo['name']}/commits?per_page=1"
                )
                # This is a simplified estimation
                total_commits += 50  # Average commits per active repo
            except:
                continue
        
        return GitHubStats(
            repos=user_data.get("public_repos", 0),
            followers=user_data.get("followers", 0),
            stars=total_stars,
            commits=total_commits or 1200  # Fallback
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching GitHub stats: {e}")
        # Return fallback data
        return GitHubStats(
            repos=42,
            followers=156,
            stars=287,
            commits=1200
        )

@app.get("/api/github/repos", response_model=List[Project])
async def get_github_repos():
    """Get GitHub repositories for portfolio"""
    try:
        repos_data = await github_request(f"users/{GITHUB_USERNAME}/repos?sort=updated&per_page=20")
        
        # Filter and transform repos
        portfolio_repos = []
        for repo in repos_data:
            if repo.get("fork") or repo.get("stargazers_count", 0) < 0:
                continue
                
            portfolio_repos.append(Project(
                id=str(repo["id"]),
                title=repo["name"].replace("-", " ").title(),
                description=repo.get("description", "No description available"),
                technologies=[repo.get("language", "JavaScript")] if repo.get("language") else ["JavaScript"],
                github_url=repo["html_url"],
                demo_url=repo.get("homepage"),
                image_url=f"https://opengraph.githubassets.com/1/{GITHUB_USERNAME}/{repo['name']}",
                stars=repo.get("stargazers_count"),
                forks=repo.get("forks_count"),
                language=repo.get("language"),
                updated_at=repo.get("updated_at")
            ))
            
            if len(portfolio_repos) >= 6:
                break
        
        return portfolio_repos
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching repos: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch repositories")

@app.get("/api/blog", response_model=List[BlogPost])
async def get_blog_posts():
    """Get blog posts (mock data for now)"""
    # Mock data - replace with actual blog integration
    mock_posts = [
        BlogPost(
            id="1",
            title="Building Serverless APIs with Python",
            excerpt="Learn how to create scalable serverless APIs using Python and FastAPI.",
            category="Backend Development",
            read_time=8,
            published_at="2024-01-15T00:00:00Z",
            slug="building-serverless-apis-python"
        ),
        BlogPost(
            id="2",
            title="Modern AI/ML Pipeline Architecture",
            excerpt="Best practices for building robust machine learning pipelines in production.",
            category="AI/ML",
            read_time=12,
            published_at="2024-01-10T00:00:00Z",
            slug="modern-ai-ml-pipeline-architecture"
        ),
        BlogPost(
            id="3",
            title="Data Engineering at Scale",
            excerpt="Strategies for handling large-scale data processing and analytics.",
            category="Data Engineering",
            read_time=10,
            published_at="2024-01-05T00:00:00Z",
            slug="data-engineering-at-scale"
        )
    ]
    
    return mock_posts

async def send_email(contact_data: ContactForm):
    """Send email notification using Resend"""
    if not RESEND_API_KEY:
        logger.warning("No email service configured")
        return
    
    try:
        import resend
        resend.api_key = RESEND_API_KEY
        
        params = {
            "from": "Portfolio <<EMAIL>>",
            "to": [CONTACT_EMAIL],
            "subject": f"Portfolio Contact: {contact_data.subject}",
            "html": f"""
                <h2>New Contact Form Submission</h2>
                <p><strong>Name:</strong> {contact_data.first_name} {contact_data.last_name}</p>
                <p><strong>Email:</strong> {contact_data.email}</p>
                <p><strong>Subject:</strong> {contact_data.subject}</p>
                <p><strong>Message:</strong></p>
                <p>{contact_data.message.replace(chr(10), '<br>')}</p>
            """
        }
        
        await asyncio.to_thread(resend.Emails.send, params)
        logger.info(f"Email sent for contact from {contact_data.email}")
        
    except Exception as e:
        logger.error(f"Failed to send email: {e}")

@app.post("/api/contact", response_model=APIResponse)
async def submit_contact_form(
    contact_data: ContactForm,
    background_tasks: BackgroundTasks
):
    """Handle contact form submission"""
    try:
        # Add email sending to background tasks
        background_tasks.add_task(send_email, contact_data)
        
        # Log the contact (in production, save to database)
        logger.info(f"Contact form submitted by {contact_data.email}")
        
        return APIResponse(
            success=True,
            message="Thank you for your message! I'll get back to you soon.",
            data={"submitted_at": datetime.now().isoformat()}
        )
        
    except Exception as e:
        logger.error(f"Contact form error: {e}")
        raise HTTPException(status_code=500, detail="Failed to process contact form")

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "Endpoint not found", "status_code": 404}

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {exc}")
    return {"error": "Internal server error", "status_code": 500}

# For different serverless platforms
def create_app():
    """Factory function for creating the app"""
    return app

# Vercel handler
app_handler = create_app()

# AWS Lambda handler
from mangum import Mangum
lambda_handler = Mangum(app)

# Google Cloud Functions handler
import functions_framework
@functions_framework.http
def gcp_handler(request):
    from google.cloud.functions.context import Context
    return app(request.environ, lambda *args: None)