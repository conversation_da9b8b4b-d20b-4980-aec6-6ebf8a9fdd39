'use client'

import { motion, useMotionValue, useSpring, useTransform } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, GitFork, ExternalLink, Github } from "lucide-react";

interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  githubUrl?: string;
  demoUrl?: string;
  imageUrl?: string;
  stars?: number;
  forks?: number;
  language?: string;
  updated_at?: string;
}

interface ProjectCard3DProps {
  project: Project;
  index: number;
}

export function ProjectCard3D({ project, index }: ProjectCard3DProps) {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useSpring(useTransform(y, [-100, 100], [30, -30]));
  const rotateY = useSpring(useTransform(x, [-100, 100], [-30, 30]));

  const handleMouse = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;
    const xPct = mouseX / width - 0.5;
    const yPct = mouseY / height - 0.5;
    x.set(xPct * 100);
    y.set(yPct * 100);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  return (
    <motion.div
      style={{
        rotateX,
        rotateY,
        transformStyle: "preserve-3d",
      }}
      onMouseMove={handleMouse}
      onMouseLeave={handleMouseLeave}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        ease: "easeOut"
      }}
      viewport={{ once: true }}
      className="group cursor-pointer"
    >
      <Card className="glass-effect overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform-gpu border-0 bg-white/10 dark:bg-black/20 backdrop-blur-xl">
        <div className="relative overflow-hidden">
          <motion.img 
            src={project.imageUrl || "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400"} 
            alt={project.title} 
            className="w-full h-48 object-cover transition-transform duration-700"
            whileHover={{ scale: 1.1 }}
          />
          <motion.div 
            className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          />
          
          {/* GitHub stats overlay */}
          <motion.div
            className="absolute bottom-4 left-4 flex items-center space-x-3"
            initial={{ opacity: 0, y: 20 }}
            whileHover={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {project.stars !== undefined && (
              <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                <span className="text-xs text-white font-medium">{project.stars}</span>
              </div>
            )}
            {project.forks !== undefined && (
              <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
                <GitFork className="w-3 h-3 text-blue-400" />
                <span className="text-xs text-white font-medium">{project.forks}</span>
              </div>
            )}
          </motion.div>

          {/* Floating tech badges on hover */}
          <motion.div
            className="absolute top-4 left-4 flex flex-wrap gap-1"
            initial={{ opacity: 0, y: -20 }}
            whileHover={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, staggerChildren: 0.1 }}
          >
            {project.technologies.slice(0, 2).map((tech, techIndex) => (
              <motion.div
                key={tech}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: techIndex * 0.1 }}
              >
                <Badge
                  variant="secondary"
                  className="text-xs bg-white/90 text-gray-800 border-0"
                >
                  {tech}
                </Badge>
              </motion.div>
            ))}
          </motion.div>

          {/* Action buttons overlay */}
          <motion.div 
            className="absolute top-4 right-4 flex space-x-2"
            initial={{ opacity: 0, x: 20 }}
            whileHover={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {project.githubUrl && (
              <motion.a 
                href={project.githubUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center text-gray-800 hover:bg-white transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                data-testid={`github-link-${project.id}`}
              >
                <Github className="w-4 h-4" />
              </motion.a>
            )}
            {project.demoUrl && (
              <motion.a 
                href={project.demoUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center text-gray-800 hover:bg-white transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                data-testid={`demo-link-${project.id}`}
              >
                <ExternalLink className="w-4 h-4" />
              </motion.a>
            )}
          </motion.div>
        </div>

        <CardContent className="p-6 relative">
          <motion.div
            style={{ transform: "translateZ(50px)" }}
            className="transform-gpu"
          >
            <h3 className="text-xl font-bold mb-3 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              {project.title}
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
              {project.description}
            </p>
            
            <div className="flex flex-wrap gap-2 mb-6">
              {project.technologies.map((tech) => (
                <Badge 
                  key={tech} 
                  variant="outline" 
                  className="text-xs border-primary/20 text-primary/80 hover:bg-primary/10 transition-colors"
                  data-testid={`tech-${tech.toLowerCase()}`}
                >
                  {tech}
                </Badge>
              ))}
            </div>
            
            <div className="flex space-x-2">
              <Button
                className="flex-1 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white font-medium rounded-xl transition-all duration-300 border-0 shadow-lg hover:shadow-xl"
                onClick={() => project.githubUrl ? window.open(project.githubUrl, '_blank') : null}
                data-testid={`github-button-${project.id}`}
              >
                <span className="flex items-center justify-center space-x-2">
                  <Github className="w-4 h-4" />
                  <span>Code</span>
                </span>
              </Button>
              {project.demoUrl && (
                <Button
                  variant="outline"
                  className="flex-1 border-primary/20 text-primary hover:bg-primary/10 font-medium rounded-xl transition-all duration-300"
                  onClick={() => window.open(project.demoUrl, '_blank')}
                  data-testid={`demo-button-${project.id}`}
                >
                  <span className="flex items-center justify-center space-x-2">
                    <ExternalLink className="w-4 h-4" />
                    <span>Demo</span>
                  </span>
                </Button>
              )}
            </div>
          </motion.div>

          {/* Shine effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12"
            initial={{ x: "-100%" }}
            whileHover={{ x: "100%" }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            style={{ transformOrigin: "center" }}
          />
        </CardContent>
      </Card>
    </motion.div>
  );
}