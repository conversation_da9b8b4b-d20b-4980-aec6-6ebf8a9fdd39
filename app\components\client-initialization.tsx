'use client'

import React from 'react';
import { trackWebVitals } from '@/lib/performance';
import { setupGlobalErrorHandlers } from '@/components/error-boundary';

export function ClientInitialization() {
  React.useEffect(() => {
    // Setup performance monitoring
    trackWebVitals();

    // Setup global error handlers
    setupGlobalErrorHandlers();

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 Performance monitoring initialized');
      console.log('🛡️  Global error handlers initialized');
    }
  }, []);

  return null;
}