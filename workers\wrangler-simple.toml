name = "portfolio-api"
main = "src/index.ts"
compatibility_date = "2024-01-15"
compatibility_flags = ["nodejs_compat"]
account_id = "5adf62efd6cf179a8939c211b155e229"

# Custom domain configuration
[env.production]
name = "portfolio-api-production"
route = { pattern = "api.getintheq.space/*", zone_name = "getintheq.space" }

[env.staging]
name = "portfolio-api-staging"

# Environment variables will be set via CLI
# wrangler secret put GITHUB_TOKEN
# wrangler secret put GITHUB_USERNAME
# wrangler secret put RESEND_API_KEY
# wrangler secret put CONTACT_EMAIL