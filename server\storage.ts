import { 
  type User, 
  type InsertUser, 
  type BlogPost, 
  type InsertBlogPost,
  type Project,
  type InsertProject,
  type Contact,
  type InsertContact
} from "@shared/schema";
import { randomUUID } from "crypto";

export interface IStorage {
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getBlogPosts(): Promise<BlogPost[]>;
  getBlogPost(slug: string): Promise<BlogPost | undefined>;
  createBlogPost(post: InsertBlogPost): Promise<BlogPost>;
  
  getProjects(): Promise<Project[]>;
  getFeaturedProjects(): Promise<Project[]>;
  createProject(project: InsertProject): Promise<Project>;
  
  createContact(contact: InsertContact): Promise<Contact>;
}

export class MemStorage implements IStorage {
  private users: Map<string, User>;
  private blogPosts: Map<string, BlogPost>;
  private projects: Map<string, Project>;
  private contacts: Map<string, Contact>;

  constructor() {
    this.users = new Map();
    this.blogPosts = new Map();
    this.projects = new Map();
    this.contacts = new Map();
    
    // Initialize with sample data
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Sample blog posts
    const blogPost1: BlogPost = {
      id: randomUUID(),
      title: "Building Scalable ML Pipelines with MLOps",
      content: "Learn how to implement robust MLOps practices for production-ready machine learning systems...",
      excerpt: "Learn how to implement robust MLOps practices for production-ready machine learning systems...",
      category: "Machine Learning",
      readTime: 5,
      publishedAt: new Date("2023-12-15"),
      slug: "building-scalable-ml-pipelines-mlops"
    };

    const blogPost2: BlogPost = {
      id: randomUUID(),
      title: "Real-time Data Processing with Apache Kafka",
      content: "Exploring event-driven architectures and stream processing for modern data applications...",
      excerpt: "Exploring event-driven architectures and stream processing for modern data applications...",
      category: "Data Engineering",
      readTime: 7,
      publishedAt: new Date("2023-12-10"),
      slug: "real-time-data-processing-apache-kafka"
    };

    const blogPost3: BlogPost = {
      id: randomUUID(),
      title: "Responsible AI: Building Ethical ML Systems",
      content: "Understanding bias, fairness, and transparency in artificial intelligence applications...",
      excerpt: "Understanding bias, fairness, and transparency in artificial intelligence applications...",
      category: "AI & Ethics",
      readTime: 6,
      publishedAt: new Date("2023-12-05"),
      slug: "responsible-ai-building-ethical-ml-systems"
    };

    this.blogPosts.set(blogPost1.slug, blogPost1);
    this.blogPosts.set(blogPost2.slug, blogPost2);
    this.blogPosts.set(blogPost3.slug, blogPost3);

    // Sample projects
    const project1: Project = {
      id: randomUUID(),
      title: "AI-Powered Analytics Dashboard",
      description: "Real-time analytics platform with ML-powered insights and predictive modeling for business intelligence.",
      technologies: ["Python", "TensorFlow", "React", "FastAPI"],
      githubUrl: "https://github.com/example/ai-dashboard",
      demoUrl: "https://ai-dashboard-demo.com",
      imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
      featured: "true"
    };

    const project2: Project = {
      id: randomUUID(),
      title: "Retail Data Pipeline",
      description: "Scalable ETL pipeline processing millions of transactions daily with automated anomaly detection.",
      technologies: ["Apache Spark", "AWS", "Kafka", "Python"],
      githubUrl: "https://github.com/example/retail-pipeline",
      demoUrl: "",
      imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
      featured: "true"
    };

    const project3: Project = {
      id: randomUUID(),
      title: "Customer Behavior Predictor",
      description: "ML model predicting customer purchase behavior with 94% accuracy using ensemble methods.",
      technologies: ["Scikit-learn", "XGBoost", "Docker", "Flask"],
      githubUrl: "https://github.com/example/customer-predictor",
      demoUrl: "https://customer-predictor-demo.com",
      imageUrl: "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
      featured: "true"
    };

    this.projects.set(project1.id, project1);
    this.projects.set(project2.id, project2);
    this.projects.set(project3.id, project3);
  }

  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = randomUUID();
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getBlogPosts(): Promise<BlogPost[]> {
    return Array.from(this.blogPosts.values()).sort(
      (a, b) => b.publishedAt!.getTime() - a.publishedAt!.getTime()
    );
  }

  async getBlogPost(slug: string): Promise<BlogPost | undefined> {
    return this.blogPosts.get(slug);
  }

  async createBlogPost(insertPost: InsertBlogPost): Promise<BlogPost> {
    const id = randomUUID();
    const post: BlogPost = { 
      ...insertPost, 
      id,
      publishedAt: new Date()
    };
    this.blogPosts.set(post.slug, post);
    return post;
  }

  async getProjects(): Promise<Project[]> {
    return Array.from(this.projects.values());
  }

  async getFeaturedProjects(): Promise<Project[]> {
    return Array.from(this.projects.values()).filter(p => p.featured === "true");
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    const id = randomUUID();
    const project: Project = { 
      ...insertProject,
      id,
      githubUrl: insertProject.githubUrl || null,
      demoUrl: insertProject.demoUrl || null,
      imageUrl: insertProject.imageUrl || null,
      featured: insertProject.featured || "false"
    };
    this.projects.set(id, project);
    return project;
  }

  async createContact(insertContact: InsertContact): Promise<Contact> {
    const id = randomUUID();
    const contact: Contact = { 
      ...insertContact, 
      id,
      createdAt: new Date()
    };
    this.contacts.set(id, contact);
    return contact;
  }
}

export const storage = new MemStorage();
