# 🐍 Python Serverless Backend for Portfolio

A modern, secure, and scalable serverless backend built with **FastAPI** that replaces your Express.js server. Supports multiple deployment platforms including Vercel, AWS Lambda, Google Cloud Functions, and Cloudflare Workers.

## 🚀 Features

- **FastAPI** - Modern, fast, and type-safe Python API framework
- **Multi-platform** - Deploy to Vercel, AWS, GCP, or run with Docker
- **Security** - Rate limiting, input validation, security headers
- **GitHub Integration** - Fetch repositories and user stats
- **Email Support** - Contact form with Resend/SendGrid integration
- **Type Safety** - Full Pydantic models and validation
- **Auto Documentation** - OpenAPI/Swagger docs at `/api/docs`

## 📁 Project Structure

```
serverless/
├── main.py              # Main FastAPI application
├── security.py          # Security utilities and middleware
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
├── deploy.sh           # Multi-platform deployment script
├── vercel.json         # Vercel configuration
├── wrangler.toml       # Cloudflare Workers configuration
├── aws-lambda.yml      # AWS SAM template
├── Dockerfile          # Docker configuration
└── README.md           # This file
```

## 🛠️ Quick Start

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Configure your environment variables
# Edit .env with your actual values:
# - GITHUB_TOKEN (from https://github.com/settings/tokens)
# - RESEND_API_KEY (from https://resend.com)
# - CONTACT_EMAIL (your contact email)
```

### 2. Local Development

```bash
# Run locally
./deploy.sh local
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000

### 3. Deploy to Platform

Choose your preferred platform:

```bash
# Deploy to Vercel (Recommended)
./deploy.sh vercel

# Deploy to AWS Lambda
./deploy.sh aws

# Deploy to Google Cloud Functions  
./deploy.sh gcp

# Run with Docker
./deploy.sh docker
```

## 🔧 API Endpoints

### Core Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Health check and API info |
| `/api/github/stats` | GET | GitHub user statistics |
| `/api/github/repos` | GET | Portfolio repositories |
| `/api/blog` | GET | Blog posts (mock data) |
| `/api/contact` | POST | Contact form submission |

### Example Responses

**GitHub Stats** (`/api/github/stats`):
```json
{
  "repos": 42,
  "followers": 156,
  "stars": 287,
  "commits": 1200
}
```

**Repositories** (`/api/github/repos`):
```json
[
  {
    "id": "123456",
    "title": "Portfolio Website",
    "description": "Modern portfolio built with Next.js",
    "technologies": ["TypeScript"],
    "github_url": "https://github.com/khiwniti/portfolio",
    "demo_url": "https://getintheq.space",
    "image_url": "https://opengraph.githubassets.com/...",
    "stars": 15,
    "forks": 3,
    "language": "TypeScript",
    "updated_at": "2024-01-15T10:30:00Z"
  }
]
```

## 🔒 Security Features

- **Rate Limiting** - 100 requests per hour per IP
- **Input Validation** - Pydantic models with sanitization
- **Security Headers** - CSP, XSS protection, HSTS
- **CORS Protection** - Configured for your domains
- **Request Size Limits** - Prevent large payload attacks
- **Email Validation** - RFC-compliant email validation

## 🌐 Platform-Specific Setup

### Vercel (Recommended)

1. Install Vercel CLI: `npm install -g vercel`
2. Run: `./deploy.sh vercel`
3. Set environment variables in Vercel dashboard

### AWS Lambda

1. Install AWS SAM CLI
2. Configure AWS credentials
3. Run: `./deploy.sh aws`

### Google Cloud Functions

1. Install Google Cloud CLI
2. Authenticate: `gcloud auth login`
3. Run: `./deploy.sh gcp`

### Cloudflare Workers

1. Install Wrangler CLI: `npm install -g wrangler`
2. Set secrets: `wrangler secret put GITHUB_TOKEN`
3. Deploy: `wrangler publish`

## 📋 Environment Variables

Required environment variables:

```bash
# GitHub API
GITHUB_TOKEN=ghp_your_token_here
GITHUB_USERNAME=your_username

# Email Service (choose one)
RESEND_API_KEY=re_your_key_here
# OR
SENDGRID_API_KEY=SG.your_key_here

# Configuration
CONTACT_EMAIL=<EMAIL>
ENVIRONMENT=production
```

## 🔄 Frontend Integration

Update your frontend API endpoints to point to your serverless backend:

```typescript
// app/lib/constants.ts
export const API_ENDPOINTS = {
  GITHUB_REPOS: 'https://your-api.vercel.app/api/github/repos',
  GITHUB_STATS: 'https://your-api.vercel.app/api/github/stats',
  CONTACT: 'https://your-api.vercel.app/api/contact',
  BLOG: 'https://your-api.vercel.app/api/blog',
} as const;
```

## 🧪 Testing

```bash
# Install dependencies
pip install -r requirements.txt

# Run tests (if you add them)
pytest

# Test endpoints locally
curl http://localhost:8000/api/github/stats
curl -X POST http://localhost:8000/api/contact \
  -H "Content-Type: application/json" \
  -d '{"first_name":"John","last_name":"Doe","email":"<EMAIL>","subject":"Test","message":"Hello world!"}'
```

## 📊 Monitoring

- **Logs** - Check platform-specific logging (Vercel dashboard, AWS CloudWatch, etc.)
- **Health Check** - GET `/` endpoint returns API status
- **Rate Limiting** - 429 status code when limits exceeded

## 🔧 Customization

### Adding New Endpoints

```python
@app.get("/api/custom")
async def custom_endpoint():
    return {"message": "Custom endpoint"}
```

### Database Integration

```python
# Add to requirements.txt
sqlalchemy==2.0.23
psycopg2-binary==2.9.9

# Use in main.py
from sqlalchemy import create_engine
engine = create_engine(os.getenv("DATABASE_URL"))
```

### Caching

```python
# Redis caching (add redis to requirements.txt)
import redis
cache = redis.from_url(os.getenv("REDIS_URL"))
```

## 📝 Migration from Express.js

This serverless backend provides the same API endpoints as your Express.js server:

| Express.js Route | Python Serverless Route |
|------------------|-------------------------|
| `GET /api/github/stats` | `GET /api/github/stats` |
| `GET /api/github/repos` | `GET /api/github/repos` |
| `GET /api/blog` | `GET /api/blog` |
| `POST /api/contact` | `POST /api/contact` |

Simply update your frontend API endpoints to point to the new serverless backend URL.

## 🆘 Troubleshooting

### Common Issues

1. **Build Errors**: Check Python version (3.9+ required)
2. **API Timeouts**: Increase function timeout in platform settings
3. **CORS Errors**: Verify your domain is in `allow_origins`
4. **Rate Limiting**: Check rate limit settings in `security.py`

### Debug Mode

```bash
# Run with debug logging
export LOG_LEVEL=DEBUG
./deploy.sh local
```

## 📚 Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Vercel Python Functions](https://vercel.com/docs/functions/serverless-functions/runtimes/python)
- [AWS Lambda Python](https://docs.aws.amazon.com/lambda/latest/dg/python-programming-model.html)
- [Google Cloud Functions Python](https://cloud.google.com/functions/docs/writing/python)

---

**🎉 Your portfolio now has a modern, scalable, and secure Python serverless backend!**