/**
 * Portfolio API for Cloudflare Workers
 * Provides GitHub integration, contact form, blog, and AI playground endpoints
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { githubRouter } from './routes/github';
import { contactRouter } from './routes/contact';
import { blogRouter } from './routes/blog';
import { playgroundRouter } from './routes/playground';
import { errorHandler } from './middleware/error-handler';
import { rateLimiter } from './middleware/rate-limiter';

// Define environment interface
export interface Env {
  GITHUB_TOKEN: string;
  GITHUB_USERNAME: string;
  RESEND_API_KEY: string;
  CONTACT_EMAIL: string;
  ENVIRONMENT: string;
  // D1 Database
  DB: D1Database;
  // Optional KV storage
  CACHE?: KVNamespace;
  // Optional Durable Object for rate limiting
  RATE_LIMITER?: DurableObjectNamespace;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// CORS middleware
app.use('*', cors({
  origin: [
    'http://localhost:3000',
    'https://getintheq.space',
    'https://*.getintheq.space',
    'https://*.vercel.app',
    'https://*.pages.dev'
  ],
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  credentials: true,
}));

// Rate limiting middleware
app.use('*', rateLimiter);

// Health check endpoint
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'Portfolio API is running on Cloudflare Workers',
    data: {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      worker: true
    }
  });
});

// Route handlers
app.route('/api/github', githubRouter);
app.route('/api/contact', contactRouter);
app.route('/api/blog', blogRouter);
app.route('/api/playground', playgroundRouter);

// 404 handler
app.notFound((c) => {
  return c.json({
    error: 'Endpoint not found',
    status_code: 404
  }, 404);
});

// Error handler
app.onError(errorHandler);

// Export for Cloudflare Workers
export default app;