import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { AuthService } from '@/lib/auth';

export const GET = AuthService.requireAuth(async (request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const id = params.id;
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    
    // Get project details
    const projectQuery = 'SELECT * FROM playground_projects WHERE id = $1';
    const projectResult = await DatabaseService.query(projectQuery, [id]);
    
    if (projectResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Playground project not found' },
        { status: 404 }
      );
    }
    
    const project = projectResult.rows[0];
    
    // Get usage analytics
    const analyticsQuery = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as usage_count,
        COUNT(DISTINCT session_id) as unique_sessions,
        AVG(EXTRACT(EPOCH FROM duration)) as avg_duration
      FROM playground_usage 
      WHERE project_id = $1 
        AND created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    const analyticsResult = await DatabaseService.query(analyticsQuery, [id]);
    const dailyAnalytics = analyticsResult.rows;
    
    // Get total stats
    const statsQuery = `
      SELECT 
        COUNT(*) as total_usage,
        COUNT(DISTINCT session_id) as unique_users,
        AVG(EXTRACT(EPOCH FROM duration)) as avg_duration,
        MAX(created_at) as last_used
      FROM playground_usage 
      WHERE project_id = $1 
        AND created_at >= NOW() - INTERVAL '${days} days'
    `;
    
    const statsResult = await DatabaseService.query(statsQuery, [id]);
    const stats = statsResult.rows[0];
    
    // Get user feedback/ratings if available
    const feedbackQuery = `
      SELECT 
        AVG(rating) as avg_rating,
        COUNT(*) as total_ratings
      FROM playground_feedback 
      WHERE project_id = $1 
        AND created_at >= NOW() - INTERVAL '${days} days'
    `;
    
    const feedbackResult = await DatabaseService.query(feedbackQuery, [id]);
    const feedback = feedbackResult.rows[0];
    
    return NextResponse.json({
      project,
      stats: {
        total_usage: parseInt(stats.total_usage),
        unique_users: parseInt(stats.unique_users),
        avg_duration: parseFloat(stats.avg_duration) || 0,
        last_used: stats.last_used,
        avg_rating: parseFloat(feedback.avg_rating) || 0,
        total_ratings: parseInt(feedback.total_ratings)
      },
      daily_analytics: dailyAnalytics.map(day => ({
        ...day,
        usage_count: parseInt(day.usage_count),
        unique_sessions: parseInt(day.unique_sessions),
        avg_duration: parseFloat(day.avg_duration) || 0
      }))
    });
  } catch (error) {
    console.error('Error fetching playground analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch playground analytics' },
      { status: 500 }
    );
  }
});

export const POST = AuthService.requireAuth(async (request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const id = params.id;
    const body = await request.json();
    
    // Track usage
    const usageQuery = `
      INSERT INTO playground_usage (
        project_id, session_id, user_agent, ip_address, duration, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;
    
    const usageValues = [
      id,
      body.session_id,
      body.user_agent,
      body.ip_address,
      body.duration || null,
      JSON.stringify(body.metadata || {})
    ];
    
    const result = await DatabaseService.query(usageQuery, usageValues);
    
    // Update project usage count
    await DatabaseService.incrementPlaygroundUsage(parseInt(id));
    
    return NextResponse.json({ success: true, usage: result.rows[0] });
  } catch (error) {
    console.error('Error tracking playground usage:', error);
    return NextResponse.json(
      { error: 'Failed to track usage' },
      { status: 500 }
    );
  }
});
