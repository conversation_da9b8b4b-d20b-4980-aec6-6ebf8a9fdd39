import { NextRequest } from 'next/server';

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  // Core Web Vitals
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
  
  // Custom metrics
  pageLoadTime?: number;
  domContentLoaded?: number;
  renderTime?: number;
  
  // Request metrics
  requestDuration?: number;
  responseSize?: number;
  
  // User context
  userAgent?: string;
  url?: string;
  timestamp: number;
}

/**
 * Performance monitoring utility
 */
export class PerformanceMonitor {
  private static metrics: PerformanceMetrics[] = [];
  private static readonly MAX_METRICS = 1000; // Prevent memory leaks

  /**
   * Record performance metrics
   */
  static recordMetrics(metrics: Partial<PerformanceMetrics>): void {
    const fullMetrics: PerformanceMetrics = {
      timestamp: Date.now(),
      ...metrics,
    };

    this.metrics.push(fullMetrics);

    // Keep only recent metrics to prevent memory leaks
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance Metrics:', fullMetrics);
    }

    // Send to external monitoring service
    this.sendToMonitoringService(fullMetrics);
  }

  /**
   * Get performance statistics
   */
  static getStatistics(timeRange?: number): {
    totalRequests: number;
    averageResponseTime: number;
    p95ResponseTime: number;
    errorRate: number;
    coreWebVitals: {
      avgFCP: number;
      avgLCP: number;
      avgFID: number;
      avgCLS: number;
    };
  } {
    const cutoff = timeRange ? Date.now() - timeRange : 0;
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff);

    if (recentMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        p95ResponseTime: 0,
        errorRate: 0,
        coreWebVitals: {
          avgFCP: 0,
          avgLCP: 0,
          avgFID: 0,
          avgCLS: 0,
        },
      };
    }

    // Calculate response time statistics
    const responseTimes = recentMetrics
      .map(m => m.requestDuration)
      .filter(Boolean) as number[];

    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    const sortedTimes = responseTimes.sort((a, b) => a - b);
    const p95Index = Math.floor(sortedTimes.length * 0.95);
    const p95ResponseTime = sortedTimes[p95Index] || 0;

    // Calculate Core Web Vitals
    const fcpValues = recentMetrics.map(m => m.fcp).filter(Boolean) as number[];
    const lcpValues = recentMetrics.map(m => m.lcp).filter(Boolean) as number[];
    const fidValues = recentMetrics.map(m => m.fid).filter(Boolean) as number[];
    const clsValues = recentMetrics.map(m => m.cls).filter(Boolean) as number[];

    const avgFCP = fcpValues.length > 0 
      ? fcpValues.reduce((sum, val) => sum + val, 0) / fcpValues.length : 0;
    const avgLCP = lcpValues.length > 0 
      ? lcpValues.reduce((sum, val) => sum + val, 0) / lcpValues.length : 0;
    const avgFID = fidValues.length > 0 
      ? fidValues.reduce((sum, val) => sum + val, 0) / fidValues.length : 0;
    const avgCLS = clsValues.length > 0 
      ? clsValues.reduce((sum, val) => sum + val, 0) / clsValues.length : 0;

    return {
      totalRequests: recentMetrics.length,
      averageResponseTime: avgResponseTime,
      p95ResponseTime,
      errorRate: 0, // Calculate based on error tracking
      coreWebVitals: {
        avgFCP,
        avgLCP,
        avgFID,
        avgCLS,
      },
    };
  }

  /**
   * Clear all metrics
   */
  static clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Send metrics to external monitoring service
   */
  private static async sendToMonitoringService(metrics: PerformanceMetrics): Promise<void> {
    try {
      // Example: Send to your monitoring service
      if (process.env.MONITORING_ENDPOINT) {
        await fetch(process.env.MONITORING_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.MONITORING_API_KEY}`,
          },
          body: JSON.stringify(metrics),
        });
      }

      // Example: Send to Google Analytics 4
      if (process.env.NEXT_PUBLIC_GA_ID && typeof window !== 'undefined') {
        // This would run on the client side
        // gtag('event', 'web_vitals', metrics);
      }

      // Example: Send to Sentry
      if (process.env.SENTRY_DSN) {
        // Sentry.addBreadcrumb({
        //   category: 'performance',
        //   data: metrics,
        //   level: 'info',
        // });
      }
    } catch (error) {
      console.error('Failed to send metrics to monitoring service:', error);
    }
  }
}

/**
 * Request timing middleware helper
 */
export class RequestTimer {
  private startTime: number;
  private endTime?: number;

  constructor() {
    this.startTime = performance.now();
  }

  /**
   * End the timer and return duration
   */
  end(): number {
    this.endTime = performance.now();
    return this.endTime - this.startTime;
  }

  /**
   * Get current duration without ending
   */
  getCurrentDuration(): number {
    return performance.now() - this.startTime;
  }
}

/**
 * Performance monitoring middleware
 */
export function measureRequestPerformance() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const timer = new RequestTimer();
      const [request] = args;

      try {
        const result = await originalMethod.apply(this, args);
        const duration = timer.end();

        // Record performance metrics
        PerformanceMonitor.recordMetrics({
          requestDuration: duration,
          url: request?.url || 'unknown',
          userAgent: request?.headers?.get?.('user-agent') || 'unknown',
        });

        return result;
      } catch (error) {
        const duration = timer.end();

        // Record error metrics
        PerformanceMonitor.recordMetrics({
          requestDuration: duration,
          url: request?.url || 'unknown',
          userAgent: request?.headers?.get?.('user-agent') || 'unknown',
        });

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Core Web Vitals tracking (client-side)
 */
export const trackWebVitals = () => {
  if (typeof window === 'undefined') return;

  // Track FCP (First Contentful Paint)
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            PerformanceMonitor.recordMetrics({
              fcp: entry.startTime,
              url: window.location.href,
              userAgent: navigator.userAgent,
            });
          }
        });
      });
      observer.observe({ entryTypes: ['paint'] });
    } catch (error) {
      console.warn('Failed to observe paint metrics:', error);
    }

    // Track LCP (Largest Contentful Paint)
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          PerformanceMonitor.recordMetrics({
            lcp: lastEntry.startTime,
            url: window.location.href,
            userAgent: navigator.userAgent,
          });
        }
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (error) {
      console.warn('Failed to observe LCP metrics:', error);
    }

    // Track FID (First Input Delay)
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          PerformanceMonitor.recordMetrics({
            fid: entry.processingStart - entry.startTime,
            url: window.location.href,
            userAgent: navigator.userAgent,
          });
        });
      });
      observer.observe({ entryTypes: ['first-input'] });
    } catch (error) {
      console.warn('Failed to observe FID metrics:', error);
    }

    // Track CLS (Cumulative Layout Shift)
    try {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });

        PerformanceMonitor.recordMetrics({
          cls: clsValue,
          url: window.location.href,
          userAgent: navigator.userAgent,
        });
      });
      observer.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Failed to observe CLS metrics:', error);
    }
  }

  // Track page load metrics
  window.addEventListener('load', () => {
    setTimeout(() => {
      const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (perfData) {
        PerformanceMonitor.recordMetrics({
          pageLoadTime: perfData.loadEventEnd - perfData.fetchStart,
          domContentLoaded: perfData.domContentLoadedEventEnd - perfData.fetchStart,
          ttfb: perfData.responseStart - perfData.fetchStart,
          url: window.location.href,
          userAgent: navigator.userAgent,
        });
      }
    }, 0);
  });
};

/**
 * Memory usage monitoring
 */
export class MemoryMonitor {
  /**
   * Get current memory usage (Node.js only)
   */
  static getMemoryUsage(): {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  } | null {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return {
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
        external: Math.round(usage.external / 1024 / 1024), // MB
        rss: Math.round(usage.rss / 1024 / 1024), // MB
      };
    }
    return null;
  }

  /**
   * Check if memory usage is high
   */
  static isMemoryUsageHigh(threshold: number = 500): boolean {
    const usage = this.getMemoryUsage();
    return usage ? usage.heapUsed > threshold : false;
  }

  /**
   * Log memory usage
   */
  static logMemoryUsage(): void {
    const usage = this.getMemoryUsage();
    if (usage) {
      console.log('Memory Usage:', usage);
      
      if (this.isMemoryUsageHigh()) {
        console.warn('High memory usage detected!');
      }
    }
  }
}

/**
 * Database query performance tracking
 */
export class DatabaseMonitor {
  private static queryTimes: number[] = [];

  /**
   * Track database query performance
   */
  static async trackQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await queryFn();
      const duration = performance.now() - startTime;
      
      this.queryTimes.push(duration);
      
      // Keep only recent query times
      if (this.queryTimes.length > 1000) {
        this.queryTimes = this.queryTimes.slice(-1000);
      }

      // Log slow queries
      if (duration > 1000) { // 1 second
        console.warn(`Slow database query detected: ${queryName} took ${duration.toFixed(2)}ms`);
      }

      // Record metrics
      PerformanceMonitor.recordMetrics({
        requestDuration: duration,
        url: `database:${queryName}`,
      });

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      console.error(`Database query failed: ${queryName} after ${duration.toFixed(2)}ms`, error);
      
      throw error;
    }
  }

  /**
   * Get database performance statistics
   */
  static getStatistics(): {
    totalQueries: number;
    averageTime: number;
    slowQueries: number;
  } {
    const slowThreshold = 1000; // 1 second
    
    return {
      totalQueries: this.queryTimes.length,
      averageTime: this.queryTimes.length > 0 
        ? this.queryTimes.reduce((sum, time) => sum + time, 0) / this.queryTimes.length 
        : 0,
      slowQueries: this.queryTimes.filter(time => time > slowThreshold).length,
    };
  }
}

/**
 * API endpoint performance decorator
 */
export function measureApiPerformance(endpoint: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const timer = new RequestTimer();
      const [request] = args;

      try {
        const result = await originalMethod.apply(this, args);
        const duration = timer.end();

        PerformanceMonitor.recordMetrics({
          requestDuration: duration,
          url: endpoint,
          userAgent: request?.headers?.get?.('user-agent'),
        });

        return result;
      } catch (error) {
        const duration = timer.end();

        PerformanceMonitor.recordMetrics({
          requestDuration: duration,
          url: endpoint,
          userAgent: request?.headers?.get?.('user-agent'),
        });

        throw error;
      }
    };

    return descriptor;
  };
}