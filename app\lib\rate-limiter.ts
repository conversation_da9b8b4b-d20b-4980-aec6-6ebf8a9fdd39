import { NextRequest } from 'next/server';
import { IPUtils } from './security';

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: NextRequest) => string;
}

/**
 * Rate limit attempt record
 */
interface RateLimitAttempt {
  count: number;
  resetTime: number;
  blocked: boolean;
}

/**
 * In-memory rate limiter
 * For production, consider using Redis or a distributed cache
 */
class MemoryRateLimiter {
  private attempts = new Map<string, RateLimitAttempt>();
  private readonly defaultConfig: RateLimitConfig = {
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  };

  /**
   * Check if a request should be rate limited
   */
  async isLimited(key: string, config?: Partial<RateLimitConfig>): Promise<{
    limited: boolean;
    remaining: number;
    reset: Date;
    totalHits: number;
  }> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const now = Date.now();
    const windowStart = now - finalConfig.windowMs;

    // Clean up old entries
    this.cleanup(windowStart);

    // Get or create attempt record
    let attempt = this.attempts.get(key);
    if (!attempt || attempt.resetTime < windowStart) {
      attempt = {
        count: 0,
        resetTime: now + finalConfig.windowMs,
        blocked: false,
      };
    }

    // Increment count
    attempt.count++;
    
    // Check if limit exceeded
    const limited = attempt.count > finalConfig.maxRequests;
    if (limited) {
      attempt.blocked = true;
    }

    // Update stored attempt
    this.attempts.set(key, attempt);

    return {
      limited,
      remaining: Math.max(0, finalConfig.maxRequests - attempt.count),
      reset: new Date(attempt.resetTime),
      totalHits: attempt.count,
    };
  }

  /**
   * Reset rate limit for a specific key
   */
  reset(key: string): void {
    this.attempts.delete(key);
  }

  /**
   * Get current rate limit status without incrementing
   */
  getStatus(key: string, config?: Partial<RateLimitConfig>): {
    remaining: number;
    reset: Date;
    totalHits: number;
  } {
    const finalConfig = { ...this.defaultConfig, ...config };
    const attempt = this.attempts.get(key);
    
    if (!attempt) {
      return {
        remaining: finalConfig.maxRequests,
        reset: new Date(Date.now() + finalConfig.windowMs),
        totalHits: 0,
      };
    }

    return {
      remaining: Math.max(0, finalConfig.maxRequests - attempt.count),
      reset: new Date(attempt.resetTime),
      totalHits: attempt.count,
    };
  }

  /**
   * Clean up expired entries
   */
  private cleanup(cutoff: number): void {
    const entries = Array.from(this.attempts.entries());
    for (const [key, attempt] of entries) {
      if (attempt.resetTime < cutoff) {
        this.attempts.delete(key);
      }
    }
  }

  /**
   * Get all active rate limit entries (for debugging)
   */
  getActiveEntries(): Array<{ key: string; attempt: RateLimitAttempt }> {
    return Array.from(this.attempts.entries()).map(([key, attempt]) => ({
      key,
      attempt,
    }));
  }

  /**
   * Clear all rate limit entries
   */
  clear(): void {
    this.attempts.clear();
  }
}

// Global rate limiter instance
const rateLimiter = new MemoryRateLimiter();

/**
 * Rate limiter utility class
 */
export class RateLimiter {
  /**
   * Check if request is rate limited
   */
  static async checkLimit(
    request: NextRequest,
    config?: Partial<RateLimitConfig>
  ): Promise<{
    limited: boolean;
    remaining: number;
    reset: Date;
    totalHits: number;
  }> {
    const key = config?.keyGenerator 
      ? config.keyGenerator(request)
      : this.generateKey(request);

    return rateLimiter.isLimited(key, config);
  }

  /**
   * Generate rate limit key from request
   */
  static generateKey(request: NextRequest, prefix?: string): string {
    const ip = IPUtils.getRealIP(request.headers);
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const route = request.nextUrl.pathname;
    
    // Create a more specific key that includes route and a hash of user agent
    const userAgentHash = this.simpleHash(userAgent);
    const baseKey = `${ip}:${route}:${userAgentHash}`;
    
    return prefix ? `${prefix}:${baseKey}` : baseKey;
  }

  /**
   * Reset rate limit for specific key
   */
  static reset(key: string): void {
    rateLimiter.reset(key);
  }

  /**
   * Get rate limit status without incrementing
   */
  static getStatus(key: string, config?: Partial<RateLimitConfig>): {
    remaining: number;
    reset: Date;
    totalHits: number;
  } {
    return rateLimiter.getStatus(key, config);
  }

  /**
   * Clear all rate limits
   */
  static clear(): void {
    rateLimiter.clear();
  }

  /**
   * Simple hash function for creating shorter keys
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

/**
 * Predefined rate limit configurations for different endpoints
 */
export const RateLimitConfigs = {
  // Strict limits for authentication endpoints
  auth: {
    maxRequests: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
  },

  // Moderate limits for API endpoints
  api: {
    maxRequests: 100,
    windowMs: 15 * 60 * 1000, // 15 minutes
  },

  // Strict limits for contact form
  contact: {
    maxRequests: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
  },

  // Lenient limits for public content
  public: {
    maxRequests: 1000,
    windowMs: 15 * 60 * 1000, // 15 minutes
  },

  // Very strict limits for admin endpoints
  admin: {
    maxRequests: 50,
    windowMs: 15 * 60 * 1000, // 15 minutes
  },

  // Strict limits for file uploads
  upload: {
    maxRequests: 10,
    windowMs: 60 * 60 * 1000, // 1 hour
  },

  // Global rate limit (catch-all)
  global: {
    maxRequests: 500,
    windowMs: 15 * 60 * 1000, // 15 minutes
  },
} as const;

/**
 * Middleware helper for rate limiting
 */
export async function applyRateLimit(
  request: NextRequest,
  configName: keyof typeof RateLimitConfigs = 'api'
): Promise<{
  allowed: boolean;
  headers: Record<string, string>;
}> {
  const config = RateLimitConfigs[configName];
  const result = await RateLimiter.checkLimit(request, config);

  const headers: Record<string, string> = {
    'X-RateLimit-Limit': config.maxRequests.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': result.reset.toISOString(),
  };

  if (result.limited) {
    headers['Retry-After'] = Math.ceil((result.reset.getTime() - Date.now()) / 1000).toString();
  }

  return {
    allowed: !result.limited,
    headers,
  };
}

/**
 * Create a rate limit key for specific use cases
 */
export function createRateLimitKey(
  type: 'login' | 'register' | 'contact' | 'api' | 'upload',
  identifier: string,
  ip?: string
): string {
  const actualIp = ip || 'unknown';
  return `${type}:${identifier}:${actualIp}`;
}

/**
 * Rate limit decorator for API routes
 */
export function withRateLimit(
  configName: keyof typeof RateLimitConfigs = 'api'
) {
  return function <T extends (...args: any[]) => any>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (this: any, ...args: any[]) {
      const [request] = args;
      
      if (request && typeof request === 'object' && 'headers' in request) {
        const { allowed, headers } = await applyRateLimit(request, configName);
        
        if (!allowed) {
          const response = new Response(
            JSON.stringify({ error: 'Too many requests' }),
            {
              status: 429,
              headers: {
                'Content-Type': 'application/json',
                ...headers,
              },
            }
          );
          return response;
        }
      }

      return originalMethod?.apply(this, args);
    } as T;

    return descriptor;
  };
}

/**
 * Advanced rate limiter with sliding window
 */
export class SlidingWindowRateLimiter {
  private windows = new Map<string, number[]>();

  async isLimited(
    key: string,
    maxRequests: number,
    windowMs: number
  ): Promise<boolean> {
    const now = Date.now();
    const windowStart = now - windowMs;

    // Get or create window
    let window = this.windows.get(key) || [];
    
    // Remove old timestamps
    window = window.filter(timestamp => timestamp > windowStart);

    // Check if limit exceeded
    if (window.length >= maxRequests) {
      return true;
    }

    // Add current timestamp
    window.push(now);
    this.windows.set(key, window);

    return false;
  }

  reset(key: string): void {
    this.windows.delete(key);
  }

  clear(): void {
    this.windows.clear();
  }
}