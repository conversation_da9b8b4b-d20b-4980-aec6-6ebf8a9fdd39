# API Documentation

Complete API reference for the getintheq.space platform. This document provides detailed information about all available endpoints, request/response formats, authentication requirements, and practical examples.

## Table of Contents

- [Overview](#overview)
- [Authentication](#authentication)
- [Rate Limiting](#rate-limiting)
- [Error Handling](#error-handling)
- [API Endpoints](#api-endpoints)
  - [Authentication](#authentication-endpoints)
  - [Blog](#blog-endpoints)
  - [Contact](#contact-endpoints)
  - [GitHub Integration](#github-integration-endpoints)
  - [Admin](#admin-endpoints)
- [Response Formats](#response-formats)
- [Status Codes](#status-codes)
- [SDKs and Tools](#sdks-and-tools)

## Overview

The getintheq.space API is a RESTful API that provides programmatic access to:

- **Portfolio Management**: Projects, blog posts, and content
- **User Authentication**: JWT-based authentication system
- **Contact Management**: Form submissions and inquiries
- **Analytics**: Page views, user tracking, and performance metrics
- **GitHub Integration**: Repository data and statistics
- **Admin Functions**: Content management and system configuration

### Base URLs

| Environment | Base URL |
|-------------|----------|
| Production | `https://getintheq.space/api` |
| Development | `http://localhost:3000/api` |

### API Versioning

The API uses URL path versioning. The current version is `v1` (implicit - no version prefix required).

## Authentication

The API uses JWT (JSON Web Tokens) for authentication with two supported methods:

### 1. Bearer Token (Recommended for API clients)

Include the JWT token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. HTTP-Only Cookie (Web applications)

The API automatically reads authentication cookies set during login:

```http
Cookie: auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Database

    Client->>API: POST /auth/login (credentials)
    API->>Database: Validate user credentials
    Database-->>API: User data
    API->>API: Generate JWT token
    API-->>Client: JWT token + Set-Cookie
    
    Note over Client: Store token for future requests
    
    Client->>API: API Request + Authorization header
    API->>API: Validate JWT token
    API-->>Client: Protected resource
```

### Token Lifecycle

- **Expiration**: 7 days (configurable)
- **Refresh**: Not implemented (tokens must be renewed via login)
- **Revocation**: Logout clears cookies but doesn't blacklist tokens

## Rate Limiting

All endpoints implement rate limiting to prevent abuse and ensure fair usage:

| Endpoint Type | Limit | Window | Headers |
|---------------|-------|--------|---------|
| Authentication | 5 requests | 15 minutes | `X-RateLimit-*` |
| API Endpoints | 100 requests | 15 minutes | `X-RateLimit-*` |
| Contact Form | 3 requests | 1 hour | `X-RateLimit-*` |
| Admin Endpoints | 50 requests | 15 minutes | `X-RateLimit-*` |

### Rate Limit Headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 2025-08-14T19:30:00Z
```

### Rate Limit Exceeded Response

```http
HTTP/1.1 429 Too Many Requests
Retry-After: 900
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 2025-08-14T19:30:00Z

{
  "error": "Too many requests. Please try again later.",
  "code": "RATE_LIMITED"
}
```

## Error Handling

The API uses conventional HTTP status codes and returns consistent error responses:

### Error Response Format

```json
{
  "error": "Human-readable error message",
  "code": "MACHINE_READABLE_ERROR_CODE",
  "details": [
    {
      "field": "email",
      "message": "Invalid email address"
    }
  ]
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Request validation failed | 400 |
| `UNAUTHORIZED` | Authentication required | 401 |
| `FORBIDDEN` | Insufficient permissions | 403 |
| `NOT_FOUND` | Resource not found | 404 |
| `RATE_LIMITED` | Rate limit exceeded | 429 |
| `INTERNAL_ERROR` | Server error | 500 |

## API Endpoints

### Authentication Endpoints

#### POST `/auth/login`

Authenticate user with email and password.

**Request:**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123!",
  "rememberMe": true
}
```

**Response:**
```http
HTTP/1.1 200 OK
Set-Cookie: auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Max-Age=2592000
Content-Type: application/json

{
  "success": true,
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

**Error Example:**
```http
HTTP/1.1 401 Unauthorized
Content-Type: application/json

{
  "error": "Invalid email or password",
  "code": "UNAUTHORIZED"
}
```

#### GET `/auth/login`

Check current authentication status.

**Request:**
```http
GET /api/auth/login
Cookie: auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (Authenticated):**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "authenticated": true,
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

**Response (Not Authenticated):**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "authenticated": false
}
```

#### POST `/auth/logout`

Invalidate user session and clear authentication cookie.

**Request:**
```http
POST /api/auth/logout
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```http
HTTP/1.1 200 OK
Set-Cookie: auth-token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0
Content-Type: application/json

{
  "success": true,
  "message": "Logged out successfully"
}
```

#### POST `/auth/setup`

Create the first admin user (only available when no users exist).

**Request:**
```http
POST /api/auth/setup
Content-Type: application/json

{
  "username": "admin",
  "email": "<EMAIL>",
  "password": "securePassword123!"
}
```

**Response:**
```http
HTTP/1.1 201 Created
Set-Cookie: auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "success": true,
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

### Blog Endpoints

#### GET `/blog`

Retrieve paginated list of published blog posts.

**Parameters:**
- `page` (integer, optional): Page number (default: 1)
- `limit` (integer, optional): Posts per page (default: 10, max: 50)
- `category` (string, optional): Filter by category
- `search` (string, optional): Search in title and content

**Request:**
```http
GET /api/blog?page=1&limit=5&category=Web%20Development&search=next.js
```

**Response:**
```http
HTTP/1.1 200 OK
Cache-Control: public, max-age=300, stale-while-revalidate=60
Content-Type: application/json

{
  "posts": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440001",
      "title": "Getting Started with Next.js and TypeScript",
      "excerpt": "Learn how to build modern web applications with Next.js and TypeScript for better developer experience.",
      "category": "Web Development",
      "readTime": 8,
      "publishedAt": "2024-01-15T10:00:00Z",
      "slug": "getting-started-nextjs-typescript",
      "views": 1250
    }
  ],
  "pagination": {
    "current": 1,
    "total": 3,
    "hasNext": true,
    "hasPrev": false,
    "totalPosts": 15
  },
  "filters": {
    "category": "Web Development",
    "search": "next.js"
  }
}
```

### Contact Endpoints

#### POST `/contact`

Submit a contact form message.

**Request:**
```http
POST /api/contact
Content-Type: application/json
X-Forwarded-For: *************
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "subject": "Business Partnership Inquiry",
  "message": "We are interested in discussing potential collaboration opportunities. Our company specializes in enterprise software solutions and we believe there could be synergies with your work."
}
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "Thank you for your message! I'll get back to you soon.",
  "id": "550e8400-e29b-41d4-a716-446655440002"
}
```

**Validation Error Example:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Validation failed",
  "code": "VALIDATION_ERROR",
  "details": [
    {
      "field": "email",
      "message": "Invalid email address"
    },
    {
      "field": "message",
      "message": "Message must be at least 10 characters"
    }
  ]
}
```

### GitHub Integration Endpoints

#### GET `/github/repos`

Retrieve public repositories from GitHub.

**Parameters:**
- `featured` (boolean, optional): Filter for featured repositories only

**Request:**
```http
GET /api/github/repos?featured=true
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "repositories": [
    {
      "id": 123456789,
      "name": "portfolio-website",
      "full_name": "username/portfolio-website",
      "description": "Modern portfolio website built with Next.js and TypeScript",
      "html_url": "https://github.com/username/portfolio-website",
      "language": "TypeScript",
      "stargazers_count": 45,
      "forks_count": 12,
      "watchers_count": 45,
      "size": 2048,
      "default_branch": "main",
      "topics": ["nextjs", "typescript", "portfolio", "react"],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-08-14T15:30:00Z",
      "pushed_at": "2024-08-14T15:30:00Z"
    }
  ]
}
```

#### GET `/github/stats`

Retrieve GitHub profile statistics.

**Request:**
```http
GET /api/github/stats
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "public_repos": 25,
  "public_gists": 5,
  "followers": 150,
  "following": 75,
  "total_stars": 320,
  "total_forks": 89,
  "languages": {
    "TypeScript": 45,
    "JavaScript": 30,
    "Python": 15,
    "Go": 10
  }
}
```

### Admin Endpoints

All admin endpoints require authentication with admin role.

#### GET `/admin/dashboard`

Retrieve admin dashboard overview data.

**Request:**
```http
GET /api/admin/dashboard
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "stats": {
    "totalPosts": 15,
    "publishedPosts": 12,
    "totalProjects": 8,
    "contactSubmissions": 25,
    "pageViews": 15420
  },
  "recentActivity": [
    {
      "type": "post",
      "title": "New blog post published",
      "timestamp": "2024-08-14T15:30:00Z"
    },
    {
      "type": "contact",
      "title": "New contact form submission",
      "timestamp": "2024-08-14T14:20:00Z"
    }
  ],
  "performance": {
    "avgResponseTime": 185.5,
    "uptime": 99.98,
    "errorRate": 0.02
  }
}
```

#### POST `/admin/blog`

Create a new blog post.

**Request:**
```http
POST /api/admin/blog
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "title": "Building Scalable APIs with Node.js",
  "content": "# Building Scalable APIs with Node.js\n\nIn this comprehensive guide, we'll explore...",
  "excerpt": "Learn best practices for creating robust and scalable APIs using Node.js and Express.",
  "category": "Backend Development",
  "tags": ["nodejs", "api", "express", "backend"],
  "readTime": 12,
  "status": "published",
  "featuredImage": "https://example.com/images/nodejs-api.jpg",
  "metaTitle": "Building Scalable APIs with Node.js - Complete Guide",
  "metaDescription": "Comprehensive guide to building scalable APIs with Node.js, Express, and best practices.",
  "slug": "building-scalable-apis-nodejs"
}
```

**Response:**
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "id": "550e8400-e29b-41d4-a716-************",
  "title": "Building Scalable APIs with Node.js",
  "content": "# Building Scalable APIs with Node.js\n\nIn this comprehensive guide, we'll explore...",
  "excerpt": "Learn best practices for creating robust and scalable APIs using Node.js and Express.",
  "category": "Backend Development",
  "tags": ["nodejs", "api", "express", "backend"],
  "readTime": 12,
  "status": "published",
  "featuredImage": "https://example.com/images/nodejs-api.jpg",
  "metaTitle": "Building Scalable APIs with Node.js - Complete Guide",
  "metaDescription": "Comprehensive guide to building scalable APIs with Node.js, Express, and best practices.",
  "publishedAt": "2024-08-14T19:15:00Z",
  "slug": "building-scalable-apis-nodejs",
  "views": 0,
  "createdAt": "2024-08-14T19:15:00Z",
  "updatedAt": "2024-08-14T19:15:00Z"
}
```

#### GET `/admin/analytics`

Retrieve analytics dashboard data.

**Parameters:**
- `period` (string, optional): Time period (`7d`, `30d`, `90d`, `1y`) (default: `30d`)

**Request:**
```http
GET /api/admin/analytics?period=30d
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "pageViews": {
    "total": 15420,
    "unique": 8750,
    "change": 12.5
  },
  "topPages": [
    {
      "path": "/",
      "views": 3240,
      "title": "Home"
    },
    {
      "path": "/blog",
      "views": 2180,
      "title": "Blog"
    }
  ],
  "referrers": [
    {
      "source": "google.com",
      "visits": 4520
    },
    {
      "source": "github.com",
      "visits": 1240
    }
  ],
  "devices": {
    "desktop": 8750,
    "mobile": 5420,
    "tablet": 1250
  },
  "countries": [
    {
      "country": "United States",
      "visits": 5420
    },
    {
      "country": "United Kingdom",
      "visits": 2180
    }
  ]
}
```

## Response Formats

### Success Responses

All successful responses include appropriate HTTP status codes and JSON payloads:

```json
{
  "success": true,
  "data": { /* response data */ },
  "meta": { /* metadata like pagination */ }
}
```

### Error Responses

Error responses follow a consistent format:

```json
{
  "error": "Human-readable error message",
  "code": "MACHINE_READABLE_ERROR_CODE",
  "details": [ /* validation errors or additional info */ ]
}
```

## Status Codes

| Code | Meaning | Usage |
|------|---------|-------|
| 200 | OK | Successful GET, PUT, PATCH requests |
| 201 | Created | Successful POST requests that create resources |
| 204 | No Content | Successful DELETE requests |
| 400 | Bad Request | Invalid request data or parameters |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource conflict (e.g., duplicate creation) |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server-side error |

## SDKs and Tools

### Testing the API

You can test the API using various tools:

#### cURL Examples

```bash
# Login
curl -X POST https://getintheq.space/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"yourpassword"}'

# Get blog posts
curl https://getintheq.space/api/blog?page=1&limit=5

# Submit contact form
curl -X POST https://getintheq.space/api/contact \
  -H "Content-Type: application/json" \
  -d '{"firstName":"John","lastName":"Doe","email":"<EMAIL>","subject":"Test","message":"Test message"}'
```

#### Postman Collection

A complete Postman collection is available with pre-configured requests and environment variables.

#### OpenAPI/Swagger

The complete OpenAPI specification is available at [`openapi.yaml`](openapi.yaml) and can be imported into:
- Swagger UI
- Postman
- Insomnia
- Any OpenAPI-compatible tool

### Rate Limiting Best Practices

- Implement exponential backoff when rate limits are hit
- Cache responses when appropriate to reduce API calls
- Use webhooks for real-time updates instead of polling
- Monitor your usage patterns and optimize accordingly

### Security Considerations

- Always use HTTPS in production
- Store JWT tokens securely (not in localStorage for web apps)
- Implement proper CORS policies
- Validate and sanitize all input data
- Monitor for unusual API usage patterns

---

**Last Updated**: 2024-08-14  
**API Version**: 1.0.0  
**OpenAPI Specification**: [openapi.yaml](openapi.yaml)