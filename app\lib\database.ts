import { Pool } from 'pg';

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/portfolio_db',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

export { pool };

// Database Models and Types
export interface User {
  id: number;
  email: string;
  name: string;
  role: string;
  avatar_url?: string;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  is_active: boolean;
}

export interface Project {
  id: number;
  title: string;
  description?: string;
  long_description?: string;
  github_url?: string;
  demo_url?: string;
  image_url?: string;
  technologies: string[];
  category?: string;
  status: 'active' | 'archived' | 'draft';
  featured: boolean;
  sort_order: number;
  github_data?: any;
  created_at: Date;
  updated_at: Date;
  published_at?: Date;
}

export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featured_image?: string;
  status: 'draft' | 'published' | 'archived';
  author_id: number;
  category_id?: number;
  tags: string[];
  meta_title?: string;
  meta_description?: string;
  reading_time?: number;
  view_count: number;
  like_count: number;
  created_at: Date;
  updated_at: Date;
  published_at?: Date;
}

export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  created_at: Date;
}

export interface PlaygroundProject {
  id: number;
  title: string;
  description?: string;
  project_type: string;
  icon?: string;
  gradient?: string;
  tags: string[];
  configuration: any;
  status: 'active' | 'inactive';
  featured: boolean;
  sort_order: number;
  usage_count: number;
  created_at: Date;
  updated_at: Date;
}

export interface ContactSubmission {
  id: number;
  name: string;
  email: string;
  subject?: string;
  message: string;
  status: 'new' | 'read' | 'replied' | 'archived';
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

export interface MediaFile {
  id: number;
  filename: string;
  original_name: string;
  file_path: string;
  file_size?: number;
  mime_type?: string;
  alt_text?: string;
  caption?: string;
  uploaded_by?: number;
  created_at: Date;
}

export interface Setting {
  id: number;
  key: string;
  value?: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface PageView {
  id: number;
  page_path: string;
  user_agent?: string;
  ip_address?: string;
  referrer?: string;
  session_id?: string;
  created_at: Date;
}

// Database utility functions
export class DatabaseService {
  static async query(text: string, params?: any[]) {
    const client = await pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }

  static async getProjects(filters?: { status?: string; featured?: boolean; limit?: number }) {
    let query = 'SELECT * FROM projects WHERE 1=1';
    const params: any[] = [];
    let paramCount = 0;

    if (filters?.status) {
      paramCount++;
      query += ` AND status = $${paramCount}`;
      params.push(filters.status);
    }

    if (filters?.featured !== undefined) {
      paramCount++;
      query += ` AND featured = $${paramCount}`;
      params.push(filters.featured);
    }

    query += ' ORDER BY sort_order ASC, created_at DESC';

    if (filters?.limit) {
      paramCount++;
      query += ` LIMIT $${paramCount}`;
      params.push(filters.limit);
    }

    const result = await this.query(query, params);
    return result.rows as Project[];
  }

  static async getBlogPosts(filters?: { status?: string; category_id?: number; limit?: number }) {
    let query = `
      SELECT bp.*, bc.name as category_name, bc.color as category_color,
             u.name as author_name
      FROM blog_posts bp
      LEFT JOIN blog_categories bc ON bp.category_id = bc.id
      LEFT JOIN users u ON bp.author_id = u.id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (filters?.status) {
      paramCount++;
      query += ` AND bp.status = $${paramCount}`;
      params.push(filters.status);
    }

    if (filters?.category_id) {
      paramCount++;
      query += ` AND bp.category_id = $${paramCount}`;
      params.push(filters.category_id);
    }

    query += ' ORDER BY bp.published_at DESC, bp.created_at DESC';

    if (filters?.limit) {
      paramCount++;
      query += ` LIMIT $${paramCount}`;
      params.push(filters.limit);
    }

    const result = await this.query(query, params);
    return result.rows;
  }

  static async getPlaygroundProjects(filters?: { status?: string; featured?: boolean }) {
    let query = 'SELECT * FROM playground_projects WHERE 1=1';
    const params: any[] = [];
    let paramCount = 0;

    if (filters?.status) {
      paramCount++;
      query += ` AND status = $${paramCount}`;
      params.push(filters.status);
    }

    if (filters?.featured !== undefined) {
      paramCount++;
      query += ` AND featured = $${paramCount}`;
      params.push(filters.featured);
    }

    query += ' ORDER BY sort_order ASC, created_at DESC';

    const result = await this.query(query, params);
    return result.rows as PlaygroundProject[];
  }

  static async getAnalytics(days: number = 30) {
    const query = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as views,
        COUNT(DISTINCT ip_address) as unique_visitors
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    const result = await this.query(query);
    return result.rows;
  }

  static async trackPageView(data: Partial<PageView>) {
    const query = `
      INSERT INTO page_views (page_path, user_agent, ip_address, referrer, session_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `;
    
    const result = await this.query(query, [
      data.page_path,
      data.user_agent,
      data.ip_address,
      data.referrer,
      data.session_id
    ]);
    
    return result.rows[0];
  }
}
