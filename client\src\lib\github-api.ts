export interface GitHubRepo {
  id: number;
  name: string;
  description: string;
  html_url: string;
  homepage: string;
  language: string;
  stargazers_count: number;
  forks_count: number;
  updated_at: string;
  topics: string[];
}

export interface GitHubUser {
  login: string;
  name: string;
  bio: string;
  public_repos: number;
  followers: number;
  following: number;
  html_url: string;
}

export interface GitHubStats {
  repos: number;
  followers: number;
  stars: number;
  commits: number;
}

export const fetchGitHubStats = async (): Promise<GitHubStats> => {
  const response = await fetch('/api/github/stats');
  if (!response.ok) {
    throw new Error('Failed to fetch GitHub stats');
  }
  return response.json();
};

export const fetchGitHubRepos = async (): Promise<GitHubRepo[]> => {
  const response = await fetch('/api/github/repos');
  if (!response.ok) {
    throw new Error('Failed to fetch GitHub repositories');
  }
  return response.json();
};
