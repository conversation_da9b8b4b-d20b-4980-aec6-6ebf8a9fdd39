export interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  githubUrl?: string;
  demoUrl?: string;
  imageUrl?: string;
  stars?: number;
  forks?: number;
  language?: string;
  updated_at?: string;
}

export interface GitHubStats {
  repos: number;
  followers: number;
  stars: number;
  commits: number;
}

export const fallbackProjects: Project[] = [
  {
    id: '1',
    title: 'AI-Powered Data Analytics Platform',
    description: 'A comprehensive analytics platform using machine learning to provide insights from complex datasets.',
    technologies: ['Python', 'TensorFlow', 'React', 'PostgreSQL'],
    githubUrl: 'https://github.com/khiwniti/analytics-platform',
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    stars: 42,
    forks: 12,
    language: 'Python'
  },
  {
    id: '2',
    title: 'Real-time Recommendation Engine',
    description: 'Scalable recommendation system processing millions of user interactions in real-time.',
    technologies: ['Node.js', 'Redis', 'MongoDB', 'Docker'],
    githubUrl: 'https://github.com/khiwniti/recommendation-engine',
    imageUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    stars: 87,
    forks: 23,
    language: 'JavaScript'
  },
  {
    id: '3',
    title: 'Computer Vision Pipeline',
    description: 'End-to-end computer vision solution for object detection and classification in production.',
    technologies: ['PyTorch', 'OpenCV', 'FastAPI', 'AWS'],
    githubUrl: 'https://github.com/khiwniti/cv-pipeline',
    imageUrl: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    stars: 156,
    forks: 34,
    language: 'Python'
  }
];

import { API_ENDPOINTS } from './constants';

// API functions
export async function fetchProjects(): Promise<Project[]> {
  try {
    const response = await fetch(API_ENDPOINTS.GITHUB_REPOS);

    if (!response.ok) {
      throw new Error(`Failed to fetch projects: ${response.status}`);
    }

    const data = await response.json();
    return Array.isArray(data) ? data : fallbackProjects;
  } catch (error) {
    console.warn('Failed to fetch GitHub projects, using fallback data:', error);
    return fallbackProjects;
  }
}

export async function fetchGitHubStats(): Promise<GitHubStats> {
  const response = await fetch(API_ENDPOINTS.GITHUB_STATS);

  if (!response.ok) {
    throw new Error(`Failed to fetch GitHub stats: ${response.status}`);
  }

  return response.json();
}
