import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { AuthService } from '@/lib/auth';

export const POST = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const { projectId, githubUrl } = body;

    if (!projectId || !githubUrl) {
      return NextResponse.json(
        { error: 'Project ID and GitHub URL are required' },
        { status: 400 }
      );
    }

    // Extract owner and repo from URL
    const urlParts = githubUrl.split('/');
    const owner = urlParts[urlParts.length - 2];
    const repo = urlParts[urlParts.length - 1];

    if (!owner || !repo) {
      return NextResponse.json(
        { error: 'Invalid GitHub URL format' },
        { status: 400 }
      );
    }

    // Fetch repository data from GitHub API
    const githubResponse = await fetch(`https://api.github.com/repos/${owner}/${repo}`, {
      headers: {
        'Authorization': `token ${process.env.GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Portfolio-App'
      },
    });

    if (!githubResponse.ok) {
      const errorData = await githubResponse.json();
      return NextResponse.json(
        { error: `GitHub API error: ${errorData.message}` },
        { status: githubResponse.status }
      );
    }

    const githubData = await githubResponse.json();

    // Fetch additional data (languages, commits, etc.)
    const [languagesResponse, commitsResponse, releasesResponse] = await Promise.allSettled([
      fetch(`https://api.github.com/repos/${owner}/${repo}/languages`, {
        headers: {
          'Authorization': `token ${process.env.GITHUB_TOKEN}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Portfolio-App'
        },
      }),
      fetch(`https://api.github.com/repos/${owner}/${repo}/commits?per_page=1`, {
        headers: {
          'Authorization': `token ${process.env.GITHUB_TOKEN}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Portfolio-App'
        },
      }),
      fetch(`https://api.github.com/repos/${owner}/${repo}/releases?per_page=1`, {
        headers: {
          'Authorization': `token ${process.env.GITHUB_TOKEN}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Portfolio-App'
        },
      })
    ]);

    let languages = {};
    let lastCommit = null;
    let latestRelease = null;

    if (languagesResponse.status === 'fulfilled' && languagesResponse.value.ok) {
      languages = await languagesResponse.value.json();
    }

    if (commitsResponse.status === 'fulfilled' && commitsResponse.value.ok) {
      const commits = await commitsResponse.value.json();
      lastCommit = commits[0] || null;
    }

    if (releasesResponse.status === 'fulfilled' && releasesResponse.value.ok) {
      const releases = await releasesResponse.value.json();
      latestRelease = releases[0] || null;
    }

    // Prepare GitHub data object
    const githubInfo = {
      stars: githubData.stargazers_count,
      forks: githubData.forks_count,
      watchers: githubData.watchers_count,
      language: githubData.language,
      languages: languages,
      size: githubData.size,
      open_issues: githubData.open_issues_count,
      default_branch: githubData.default_branch,
      created_at: githubData.created_at,
      updated_at: githubData.updated_at,
      pushed_at: githubData.pushed_at,
      description: githubData.description,
      topics: githubData.topics || [],
      license: githubData.license ? githubData.license.name : null,
      last_commit: lastCommit ? {
        sha: lastCommit.sha,
        message: lastCommit.commit.message,
        date: lastCommit.commit.author.date,
        author: lastCommit.commit.author.name
      } : null,
      latest_release: latestRelease ? {
        tag_name: latestRelease.tag_name,
        name: latestRelease.name,
        published_at: latestRelease.published_at,
        body: latestRelease.body
      } : null
    };

    // Update project with GitHub data
    const updateQuery = `
      UPDATE projects 
      SET github_data = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `;

    const result = await DatabaseService.query(updateQuery, [
      JSON.stringify(githubInfo),
      projectId
    ]);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      project: result.rows[0],
      github_data: githubInfo
    });
  } catch (error) {
    console.error('Error syncing GitHub data:', error);
    return NextResponse.json(
      { error: 'Failed to sync GitHub data' },
      { status: 500 }
    );
  }
});

export const GET = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username') || process.env.GITHUB_USERNAME;

    if (!username) {
      return NextResponse.json(
        { error: 'GitHub username is required' },
        { status: 400 }
      );
    }

    // Fetch user repositories
    const reposResponse = await fetch(`https://api.github.com/users/${username}/repos?sort=updated&per_page=50`, {
      headers: {
        'Authorization': `token ${process.env.GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Portfolio-App'
      },
    });

    if (!reposResponse.ok) {
      const errorData = await reposResponse.json();
      return NextResponse.json(
        { error: `GitHub API error: ${errorData.message}` },
        { status: reposResponse.status }
      );
    }

    const repos = await reposResponse.json();

    // Filter and format repositories
    const formattedRepos = repos
      .filter((repo: any) => !repo.fork && !repo.archived)
      .map((repo: any) => ({
        id: repo.id,
        name: repo.name,
        full_name: repo.full_name,
        description: repo.description,
        html_url: repo.html_url,
        language: repo.language,
        stargazers_count: repo.stargazers_count,
        forks_count: repo.forks_count,
        updated_at: repo.updated_at,
        topics: repo.topics || []
      }));

    return NextResponse.json({
      username,
      repositories: formattedRepos,
      total_count: formattedRepos.length
    });
  } catch (error) {
    console.error('Error fetching GitHub repositories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch GitHub repositories' },
      { status: 500 }
    );
  }
});
