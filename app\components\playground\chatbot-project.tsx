'use client'

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, MessageSquare, Send, Bot, User, <PERSON>rk<PERSON>, <PERSON>, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ChatBotProjectProps {
  onBack: () => void;
  projectData: any;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  typing?: boolean;
}

const botPersonalities = [
  { id: 'helpful', name: 'Helpful Assistant', description: 'Professional and informative', emoji: '🤖' },
  { id: 'creative', name: 'Creative Writer', description: 'Imaginative and artistic', emoji: '🎨' },
  { id: 'technical', name: 'Tech Expert', description: 'Technical and precise', emoji: '💻' },
  { id: 'friendly', name: 'Friendly Companion', description: 'Warm and conversational', emoji: '😊' },
  { id: 'philosopher', name: 'Deep Thinker', description: 'Thoughtful and philosophical', emoji: '🧠' }
];

const quickPrompts = [
  "Tell me a joke",
  "Explain quantum computing",
  "Write a short poem",
  "What's the meaning of life?",
  "Help me plan my day",
  "Recommend a book"
];

export function ChatBotProject({ onBack, projectData }: ChatBotProjectProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm your AI assistant. I can help you with various tasks, answer questions, and have engaging conversations. What would you like to talk about today?",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedPersonality, setSelectedPersonality] = useState('helpful');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateBotResponse = (userMessage: string, personality: string): string => {
    const responses = {
      helpful: {
        greetings: ["I'm here to help! What can I assist you with today?", "Hello! How can I be of service?"],
        questions: ["That's a great question! Let me help you with that.", "I'd be happy to explain that for you."],
        default: ["I understand what you're asking. Here's what I think...", "That's interesting! Let me provide some insights."]
      },
      creative: {
        greetings: ["Welcome to our creative space! What shall we imagine together?", "Hello, fellow dreamer! Ready to explore some ideas?"],
        questions: ["Ooh, that sparks my imagination! Let me paint you a picture with words.", "What a fascinating topic to explore creatively!"],
        default: ["That reminds me of a beautiful story...", "Let me weave some creative magic around that idea!"]
      },
      technical: {
        greetings: ["System initialized. How may I assist with your technical queries?", "Technical support ready. What's your question?"],
        questions: ["Let me break down the technical aspects for you.", "From a technical perspective, here's what you need to know."],
        default: ["Analyzing your request... Here's the technical breakdown.", "Processing... Let me provide the technical details."]
      },
      friendly: {
        greetings: ["Hey there! So nice to chat with you! 😊", "Hi friend! What's on your mind today?"],
        questions: ["Oh, I love talking about this! Let me share my thoughts.", "That's such a cool question! I'm excited to discuss it."],
        default: ["You know what? That's really interesting!", "I'm so glad you brought that up! Here's what I think..."]
      },
      philosopher: {
        greetings: ["Greetings, fellow seeker of wisdom. What profound questions shall we explore?", "Welcome to our philosophical discourse. What mysteries shall we unravel?"],
        questions: ["Ah, a question that touches the very essence of existence...", "This inquiry leads us to deeper philosophical waters..."],
        default: ["This reminds me of ancient wisdom...", "Let us contemplate the deeper meaning behind this..."]
      }
    };

    const personalityResponses = responses[personality as keyof typeof responses] || responses.helpful;
    
    // Simple keyword-based response selection
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
      return personalityResponses.greetings[Math.floor(Math.random() * personalityResponses.greetings.length)];
    } else if (lowerMessage.includes('?') || lowerMessage.includes('what') || lowerMessage.includes('how') || lowerMessage.includes('why')) {
      return personalityResponses.questions[Math.floor(Math.random() * personalityResponses.questions.length)];
    } else {
      return personalityResponses.default[Math.floor(Math.random() * personalityResponses.default.length)];
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToSend = inputMessage;
    setInputMessage('');
    setIsTyping(true);

    try {
      // Import the API client
      const { PlaygroundAPI, handleApiError } = await import('@/lib/playground-api');
      
      // Make API call to get bot response
      const response = await PlaygroundAPI.sendChatMessage({
        content: messageToSend,
        personality: selectedPersonality as any
      });
      
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.message,
        sender: 'bot',
        timestamp: new Date()
      };

      setIsTyping(false);
      setMessages(prev => [...prev, botMessage]);
      
    } catch (error) {
      console.error('Chat error:', error);
      // Fallback to mock response if API fails
      await new Promise(resolve => setTimeout(resolve, 1000));
      const botResponse = generateBotResponse(messageToSend, selectedPersonality);
      
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: botResponse,
        sender: 'bot',
        timestamp: new Date()
      };

      setIsTyping(false);
      setMessages(prev => [...prev, botMessage]);
    }
  };

  const sendQuickPrompt = (prompt: string) => {
    setInputMessage(prompt);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const clearChat = () => {
    setMessages([{
      id: '1',
      content: "Chat cleared! I'm ready for a fresh conversation. What would you like to talk about?",
      sender: 'bot',
      timestamp: new Date()
    }]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-900 dark:via-purple-900 dark:to-pink-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Playground
          </Button>
          
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold gradient-text">AI Chatbot</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Intelligent conversation with personality and context awareness
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Settings Panel */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-1 space-y-4"
          >
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Bot Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Personality</label>
                  <Select value={selectedPersonality} onValueChange={setSelectedPersonality}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {botPersonalities.map(personality => (
                        <SelectItem key={personality.id} value={personality.id}>
                          <div className="flex items-center gap-2">
                            <span>{personality.emoji}</span>
                            <div>
                              <div className="font-medium">{personality.name}</div>
                              <div className="text-xs text-gray-500">{personality.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Button variant="outline" onClick={clearChat} className="w-full">
                  Clear Chat
                </Button>
              </CardContent>
            </Card>

            <Card className="glass-effect">
              <CardHeader>
                <CardTitle>Quick Prompts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {quickPrompts.map((prompt, index) => (
                    <button
                      key={index}
                      onClick={() => sendQuickPrompt(prompt)}
                      className="w-full p-2 text-left text-sm border rounded-lg hover:border-indigo-300 transition-colors"
                    >
                      {prompt}
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Chat Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="lg:col-span-3"
          >
            <Card className="glass-effect h-[600px] flex flex-col">
              <CardHeader className="flex-shrink-0">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="w-5 h-5" />
                    AI Assistant
                  </CardTitle>
                  <Badge variant="outline" className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white border-0">
                    {botPersonalities.find(p => p.id === selectedPersonality)?.emoji} {botPersonalities.find(p => p.id === selectedPersonality)?.name}
                  </Badge>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-1 overflow-y-auto space-y-4">
                <AnimatePresence>
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.sender === 'bot' && (
                        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                      )}
                      
                      <div className={`max-w-[70%] p-3 rounded-2xl ${
                        message.sender === 'user'
                          ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white'
                          : 'bg-white dark:bg-gray-800 border'
                      }`}>
                        <p className="text-sm leading-relaxed">{message.content}</p>
                        <div className={`text-xs mt-1 ${
                          message.sender === 'user' ? 'text-indigo-100' : 'text-gray-500'
                        }`}>
                          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </div>

                      {message.sender === 'user' && (
                        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                        </div>
                      )}
                    </motion.div>
                  ))}
                </AnimatePresence>

                {/* Typing Indicator */}
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex gap-3"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-white dark:bg-gray-800 border p-3 rounded-2xl">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                      </div>
                    </div>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </CardContent>

              {/* Input */}
              <div className="p-4 border-t">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type your message..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="flex-1"
                  />
                  <Button
                    onClick={sendMessage}
                    disabled={!inputMessage.trim() || isTyping}
                    className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
