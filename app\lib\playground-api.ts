/**
 * API service functions for AI Playground components
 */

import { API_ENDPOINTS, API_BASE_URL } from './constants';

// Base URL for API calls (can be environment-dependent)
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // Client-side: use API_BASE_URL or environment variable
    return process.env.NEXT_PUBLIC_API_URL || API_BASE_URL;
  }
  // Server-side: use environment variable or API_BASE_URL
  return process.env.API_URL || API_BASE_URL;
};

// Types for API requests and responses
export interface ChatRequest {
  content: string;
  personality: 'helpful' | 'creative' | 'technical' | 'friendly' | 'philosopher';
}

export interface ChatResponse {
  message: string;
  timestamp: string;
  personality: string;
  response_time: number;
}

export interface TextGenerationRequest {
  prompt: string;
  text_type: 'story' | 'blog' | 'email' | 'poem' | 'code' | 'marketing';
  model: 'gpt4' | 'claude' | 'gemini';
  word_count: number;
}

export interface TextGenerationResponse {
  generated_text: string;
  word_count: number;
  character_count: number;
  model_used: string;
  generation_time: number;
}

export interface SentimentRequest {
  text: string;
}

export interface SentimentResponse {
  overall: {
    sentiment: 'positive' | 'negative' | 'neutral';
    confidence: number;
    score: number;
  };
  emotions: Array<{
    emotion: string;
    intensity: number;
    color: string;
  }>;
  keywords: Array<{
    word: string;
    sentiment: string;
    weight: number;
  }>;
  insights: string[];
  analysis_time: number;
}

export interface CodeAssistRequest {
  code: string;
  language: string;
  task: 'explain' | 'optimize' | 'debug' | 'generate' | 'review';
}

export interface CodeAssistResponse {
  result: string;
  suggestions: string[];
  language: string;
  task_type: string;
  analysis_time: number;
}

export interface ImageAnalysisResponse {
  description: string;
  objects: Array<{
    name: string;
    confidence: number;
    bbox: number[];
  }>;
  colors: string[];
  tags: string[];
  confidence: number;
  analysis_time: number;
}

// API service functions
export class PlaygroundAPI {
  private static async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const baseUrl = getBaseUrl();
    const url = `${baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const response = await fetch(url, { ...defaultOptions, ...options });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.detail || 
        errorData.message || 
        `API request failed: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Send a chat message to the AI
   */
  static async sendChatMessage(request: ChatRequest): Promise<ChatResponse> {
    return this.makeRequest<ChatResponse>(API_ENDPOINTS.PLAYGROUND_CHAT, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Generate text using AI
   */
  static async generateText(request: TextGenerationRequest): Promise<TextGenerationResponse> {
    return this.makeRequest<TextGenerationResponse>(API_ENDPOINTS.PLAYGROUND_GENERATE_TEXT, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Analyze sentiment in text
   */
  static async analyzeSentiment(request: SentimentRequest): Promise<SentimentResponse> {
    return this.makeRequest<SentimentResponse>(API_ENDPOINTS.PLAYGROUND_SENTIMENT, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Get code assistance
   */
  static async getCodeAssistance(request: CodeAssistRequest): Promise<CodeAssistResponse> {
    return this.makeRequest<CodeAssistResponse>(API_ENDPOINTS.PLAYGROUND_CODE_ASSIST, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Analyze an uploaded image
   */
  static async analyzeImage(file: File): Promise<ImageAnalysisResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const baseUrl = getBaseUrl();
    const url = `${baseUrl}${API_ENDPOINTS.PLAYGROUND_IMAGE_ANALYSIS}`;

    const response = await fetch(url, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.detail || 
        errorData.message || 
        `Image analysis failed: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }
}

// Utility functions for error handling
export class PlaygroundError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'PlaygroundError';
  }
}

export const handleApiError = (error: unknown): PlaygroundError => {
  if (error instanceof PlaygroundError) {
    return error;
  }

  if (error instanceof Error) {
    // Check if it's a network error
    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      return new PlaygroundError(
        'Network error. Please check your connection and try again.',
        0,
        'NETWORK_ERROR'
      );
    }

    // Check if it's a timeout error
    if (error.message.includes('timeout')) {
      return new PlaygroundError(
        'Request timed out. Please try again.',
        408,
        'TIMEOUT_ERROR'
      );
    }

    return new PlaygroundError(error.message, 500, 'API_ERROR');
  }

  return new PlaygroundError('An unexpected error occurred', 500, 'UNKNOWN_ERROR');
};

// React Query hook factories (optional - for better integration with React Query)
export const createPlaygroundQueries = () => ({
  // These can be used with React Query for caching and state management
  chatMutation: {
    mutationFn: PlaygroundAPI.sendChatMessage,
    onError: handleApiError,
  },
  
  textGenerationMutation: {
    mutationFn: PlaygroundAPI.generateText,
    onError: handleApiError,
  },
  
  sentimentAnalysisMutation: {
    mutationFn: PlaygroundAPI.analyzeSentiment,
    onError: handleApiError,
  },
  
  codeAssistMutation: {
    mutationFn: PlaygroundAPI.getCodeAssistance,
    onError: handleApiError,
  },
  
  imageAnalysisMutation: {
    mutationFn: PlaygroundAPI.analyzeImage,
    onError: handleApiError,
  },
});

// Environment detection
export const isProduction = () => process.env.NODE_ENV === 'production';
export const isDevelopment = () => process.env.NODE_ENV === 'development';

// Configuration for different environments
export const getApiConfig = () => ({
  baseUrl: getBaseUrl(),
  timeout: isDevelopment() ? 30000 : 10000, // 30s in dev, 10s in prod
  retries: isDevelopment() ? 1 : 3,
  retryDelay: 1000,
});

export default PlaygroundAPI;