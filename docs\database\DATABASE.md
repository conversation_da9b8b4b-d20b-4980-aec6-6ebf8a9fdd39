# Database Management Guide

Comprehensive database management guide for the getintheq.space platform, covering schema design, migrations, backup strategies, performance optimization, and operational procedures for PostgreSQL with Drizzle ORM.

## Table of Contents

- [Database Overview](#database-overview)
- [Schema Design](#schema-design)
- [Migration Management](#migration-management)
- [Backup & Recovery](#backup--recovery)
- [Performance Optimization](#performance-optimization)
- [Security & Access Control](#security--access-control)
- [Monitoring & Maintenance](#monitoring--maintenance)
- [Disaster Recovery](#disaster-recovery)
- [Data Operations](#data-operations)
- [Development Workflows](#development-workflows)
- [Production Operations](#production-operations)
- [Troubleshooting](#troubleshooting)

## Database Overview

The getintheq.space platform uses PostgreSQL as the primary database with Drizzle ORM for type-safe database access and migrations.

### Database Architecture

```mermaid
graph TB
    subgraph "Application Layer"
        App[Next.js Application]
        API[API Routes]
        ORM[Drizzle ORM]
    end

    subgraph "Database Layer"
        Primary[(Primary Database<br/>Neon PostgreSQL)]
        ReadReplica[(Read Replica<br/>Optional)]
        Backup[(Backup Storage)]
    end

    subgraph "Infrastructure"
        Pool[Connection Pool]
        Monitor[Monitoring]
        Cache[Redis Cache]
    end

    App --> API
    API --> ORM
    ORM --> Pool
    Pool --> Primary
    Primary --> ReadReplica
    Primary --> Backup
    Monitor --> Primary
    Cache --> API
```

### Database Configuration

```typescript
// shared/database/config.ts
export const DATABASE_CONFIG = {
  // Connection settings
  connection: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'getintheq',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  },

  // Pool settings
  pool: {
    min: 2,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
    acquireTimeoutMillis: 60000,
    createTimeoutMillis: 3000,
    destroyTimeoutMillis: 5000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
  },

  // Performance settings
  performance: {
    statement_timeout: '30s',
    idle_in_transaction_session_timeout: '5min',
    lock_timeout: '10s',
    work_mem: '4MB',
    shared_buffers: '256MB',
    effective_cache_size: '1GB',
  },

  // Backup settings
  backup: {
    retention: {
      daily: 7,    // Keep daily backups for 7 days
      weekly: 4,   // Keep weekly backups for 4 weeks
      monthly: 12, // Keep monthly backups for 12 months
    },
    compression: true,
    encryption: true,
  },
} as const;
```

## Schema Design

### Core Schema Structure

```typescript
// shared/schema.ts
import { pgTable, text, timestamp, boolean, integer, jsonb, uuid, varchar, index, foreignKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull().default('user'),
  emailVerified: boolean('email_verified').default(false),
  avatar: text('avatar'),
  bio: text('bio'),
  socialLinks: jsonb('social_links').$type<Record<string, string>>(),
  preferences: jsonb('preferences').$type<UserPreferences>(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
  emailIdx: index('users_email_idx').on(table.email),
  roleIdx: index('users_role_idx').on(table.role),
  createdAtIdx: index('users_created_at_idx').on(table.createdAt),
}));

// Blog posts table
export const blogPosts = pgTable('blog_posts', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: varchar('title', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  content: text('content').notNull(),
  excerpt: text('excerpt'),
  coverImage: text('cover_image'),
  status: varchar('status', { length: 20 }).notNull().default('draft'),
  tags: jsonb('tags').$type<string[]>(),
  metadata: jsonb('metadata').$type<PostMetadata>(),
  authorId: uuid('author_id').notNull(),
  publishedAt: timestamp('published_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
  authorFk: foreignKey({
    columns: [table.authorId],
    foreignColumns: [users.id],
    name: 'blog_posts_author_fk'
  }).onDelete('cascade'),
  slugIdx: index('blog_posts_slug_idx').on(table.slug),
  statusIdx: index('blog_posts_status_idx').on(table.status),
  authorIdx: index('blog_posts_author_idx').on(table.authorId),
  publishedAtIdx: index('blog_posts_published_at_idx').on(table.publishedAt),
  tagsIdx: index('blog_posts_tags_idx').using('gin', table.tags),
}));

// Sessions table
export const sessions = pgTable('sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}, (table) => ({
  userFk: foreignKey({
    columns: [table.userId],
    foreignColumns: [users.id],
    name: 'sessions_user_fk'
  }).onDelete('cascade'),
  tokenIdx: index('sessions_token_idx').on(table.token),
  userIdx: index('sessions_user_idx').on(table.userId),
  expiresAtIdx: index('sessions_expires_at_idx').on(table.expiresAt),
}));

// Contact messages table
export const contactMessages = pgTable('contact_messages', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  subject: varchar('subject', { length: 255 }).notNull(),
  message: text('message').notNull(),
  status: varchar('status', { length: 20 }).notNull().default('unread'),
  respondedAt: timestamp('responded_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}, (table) => ({
  statusIdx: index('contact_messages_status_idx').on(table.status),
  emailIdx: index('contact_messages_email_idx').on(table.email),
  createdAtIdx: index('contact_messages_created_at_idx').on(table.createdAt),
}));

// Performance metrics table
export const performanceMetrics = pgTable('performance_metrics', {
  id: uuid('id').primaryKey().defaultRandom(),
  url: varchar('url', { length: 500 }).notNull(),
  metric: varchar('metric', { length: 50 }).notNull(),
  value: integer('value').notNull(),
  userAgent: text('user_agent'),
  timestamp: timestamp('timestamp').notNull().defaultNow(),
}, (table) => ({
  urlIdx: index('performance_metrics_url_idx').on(table.url),
  metricIdx: index('performance_metrics_metric_idx').on(table.metric),
  timestampIdx: index('performance_metrics_timestamp_idx').on(table.timestamp),
}));

// Security logs table
export const securityLogs = pgTable('security_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  eventType: varchar('event_type', { length: 50 }).notNull(),
  userId: uuid('user_id'),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  details: jsonb('details'),
  severity: varchar('severity', { length: 20 }).notNull(),
  timestamp: timestamp('timestamp').notNull().defaultNow(),
}, (table) => ({
  userFk: foreignKey({
    columns: [table.userId],
    foreignColumns: [users.id],
    name: 'security_logs_user_fk'
  }).onDelete('set null'),
  eventTypeIdx: index('security_logs_event_type_idx').on(table.eventType),
  userIdx: index('security_logs_user_idx').on(table.userId),
  severityIdx: index('security_logs_severity_idx').on(table.severity),
  timestampIdx: index('security_logs_timestamp_idx').on(table.timestamp),
}));

// Define relations
export const usersRelations = relations(users, ({ many }) => ({
  blogPosts: many(blogPosts),
  sessions: many(sessions),
  securityLogs: many(securityLogs),
}));

export const blogPostsRelations = relations(blogPosts, ({ one }) => ({
  author: one(users, {
    fields: [blogPosts.authorId],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const securityLogsRelations = relations(securityLogs, ({ one }) => ({
  user: one(users, {
    fields: [securityLogs.userId],
    references: [users.id],
  }),
}));

// Zod schemas for validation
export const insertUserSchema = createInsertSchema(users, {
  email: z.string().email(),
  name: z.string().min(2).max(255),
  role: z.enum(['user', 'admin']),
});

export const selectUserSchema = createSelectSchema(users);

export const insertBlogPostSchema = createInsertSchema(blogPosts, {
  title: z.string().min(1).max(255),
  slug: z.string().min(1).max(255),
  content: z.string().min(1),
  status: z.enum(['draft', 'published', 'archived']),
});

export const selectBlogPostSchema = createSelectSchema(blogPosts);

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type BlogPost = typeof blogPosts.$inferSelect;
export type NewBlogPost = typeof blogPosts.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type ContactMessage = typeof contactMessages.$inferSelect;

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    browser: boolean;
  };
  privacy: {
    showEmail: boolean;
    showProfile: boolean;
  };
}

export interface PostMetadata {
  readingTime: number;
  wordCount: number;
  language: string;
  seoTitle?: string;
  seoDescription?: string;
}
```

## Migration Management

### Drizzle Migration Configuration

```typescript
// drizzle.config.ts
import type { Config } from 'drizzle-kit';
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  schema: './shared/schema.ts',
  out: './migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: true,
}) satisfies Config;
```

### Migration Scripts

```bash
#!/bin/bash
# scripts/db/migrate.sh

set -e

echo "🗄️ Database Migration Script"

# Source environment variables
if [ -f .env.local ]; then
  source .env.local
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
  echo "❌ DATABASE_URL is not set"
  exit 1
fi

# Function to run migrations
run_migrations() {
  local environment=${1:-development}
  
  echo "📊 Running migrations for $environment environment..."
  
  case $environment in
    "production")
      echo "⚠️  Running production migrations - this will affect live data!"
      read -p "Are you sure? (yes/no): " confirm
      if [ "$confirm" != "yes" ]; then
        echo "Migration cancelled"
        exit 0
      fi
      ;;
    "staging")
      echo "🔧 Running staging migrations..."
      ;;
    *)
      echo "🛠️ Running development migrations..."
      ;;
  esac
  
  # Generate migrations if needed
  echo "🔍 Checking for schema changes..."
  npx drizzle-kit generate:pg --schema=./shared/schema.ts --out=./migrations
  
  # Run migrations
  echo "⚡ Applying migrations..."
  npx drizzle-kit push:pg --schema=./shared/schema.ts
  
  echo "✅ Migrations completed successfully!"
}

# Function to rollback migrations
rollback_migration() {
  echo "⏪ Rolling back last migration..."
  
  # Get the last migration
  local last_migration=$(ls -t migrations/*.sql | head -n1)
  
  if [ -z "$last_migration" ]; then
    echo "❌ No migrations found to rollback"
    exit 1
  fi
  
  echo "Rolling back: $last_migration"
  
  # This would need custom rollback logic since Drizzle doesn't have built-in rollbacks
  # You would need to manually create rollback scripts
  echo "⚠️  Manual rollback required - check migrations/rollbacks/ directory"
}

# Function to seed database
seed_database() {
  local environment=${1:-development}
  
  echo "🌱 Seeding database for $environment environment..."
  
  case $environment in
    "production")
      echo "❌ Cannot seed production database"
      exit 1
      ;;
    *)
      npm run db:seed
      ;;
  esac
  
  echo "✅ Database seeded successfully!"
}

# Function to backup before migration
backup_before_migration() {
  local environment=${1:-development}
  
  if [ "$environment" = "production" ] || [ "$environment" = "staging" ]; then
    echo "💾 Creating backup before migration..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="backups/pre_migration_${environment}_${timestamp}.sql"
    
    mkdir -p backups
    
    pg_dump "$DATABASE_URL" > "$backup_file"
    
    if [ $? -eq 0 ]; then
      echo "✅ Backup created: $backup_file"
    else
      echo "❌ Backup failed"
      exit 1
    fi
  fi
}

# Main script logic
case $1 in
  "migrate")
    backup_before_migration $2
    run_migrations $2
    ;;
  "rollback")
    rollback_migration
    ;;
  "seed")
    seed_database $2
    ;;
  "status")
    echo "📊 Migration status:"
    npx drizzle-kit introspect:pg
    ;;
  *)
    echo "Usage: $0 {migrate|rollback|seed|status} [environment]"
    echo "Environment: development (default), staging, production"
    exit 1
    ;;
esac
```

### Database Seeding

```typescript
// scripts/db/seed.ts
import { db } from '@/lib/database';
import { users, blogPosts, contactMessages } from '@shared/schema';
import { hashPassword } from '@/lib/auth/password';

export async function seedDatabase() {
  console.log('🌱 Seeding database...');

  try {
    // Clear existing data in development
    if (process.env.NODE_ENV === 'development') {
      await db.delete(contactMessages);
      await db.delete(blogPosts);
      await db.delete(users);
      console.log('🧹 Cleared existing data');
    }

    // Create admin user
    const adminPassword = await hashPassword('admin123!@#');
    const [adminUser] = await db.insert(users).values({
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      emailVerified: true,
      bio: 'System administrator',
      preferences: {
        theme: 'dark',
        notifications: { email: true, browser: true },
        privacy: { showEmail: false, showProfile: true }
      }
    }).returning();

    console.log('👤 Created admin user');

    // Create sample users
    const userPassword = await hashPassword('user123!@#');
    const sampleUsers = await db.insert(users).values([
      {
        email: '<EMAIL>',
        name: 'John Doe',
        role: 'user',
        emailVerified: true,
        bio: 'Software developer and tech enthusiast',
        socialLinks: {
          github: 'https://github.com/johndoe',
          twitter: 'https://twitter.com/johndoe'
        }
      },
      {
        email: '<EMAIL>',
        name: 'Jane Smith',
        role: 'user',
        emailVerified: true,
        bio: 'UX designer and creative thinker'
      }
    ]).returning();

    console.log('👥 Created sample users');

    // Create sample blog posts
    const samplePosts = await db.insert(blogPosts).values([
      {
        title: 'Getting Started with Next.js 14',
        slug: 'getting-started-nextjs-14',
        content: 'Next.js 14 introduces several new features...',
        excerpt: 'Learn about the latest features in Next.js 14',
        status: 'published',
        tags: ['nextjs', 'react', 'javascript'],
        metadata: {
          readingTime: 5,
          wordCount: 1200,
          language: 'en'
        },
        authorId: adminUser.id,
        publishedAt: new Date()
      },
      {
        title: 'Building Scalable APIs with TypeScript',
        slug: 'building-scalable-apis-typescript',
        content: 'TypeScript provides excellent tooling for building APIs...',
        excerpt: 'Best practices for building TypeScript APIs',
        status: 'published',
        tags: ['typescript', 'api', 'backend'],
        metadata: {
          readingTime: 8,
          wordCount: 2000,
          language: 'en'
        },
        authorId: sampleUsers[0].id,
        publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
      },
      {
        title: 'Draft: Advanced React Patterns',
        slug: 'advanced-react-patterns',
        content: 'This post covers advanced React patterns...',
        excerpt: 'Learn advanced React patterns and techniques',
        status: 'draft',
        tags: ['react', 'patterns', 'advanced'],
        metadata: {
          readingTime: 12,
          wordCount: 3000,
          language: 'en'
        },
        authorId: adminUser.id
      }
    ]).returning();

    console.log('📝 Created sample blog posts');

    // Create sample contact messages
    await db.insert(contactMessages).values([
      {
        name: 'Alice Johnson',
        email: '<EMAIL>',
        subject: 'Great website!',
        message: 'I really love your blog posts about web development.',
        status: 'unread'
      },
      {
        name: 'Bob Wilson',
        email: '<EMAIL>',
        subject: 'Collaboration opportunity',
        message: 'I would like to discuss a potential collaboration.',
        status: 'read',
        respondedAt: new Date()
      }
    ]);

    console.log('💬 Created sample contact messages');

    console.log('✅ Database seeded successfully!');
    console.log('\n📊 Summary:');
    console.log(`👤 Users: ${sampleUsers.length + 1} (including admin)`);
    console.log(`📝 Blog posts: ${samplePosts.length}`);
    console.log(`💬 Contact messages: 2`);
    console.log('\n🔑 Login credentials:');
    console.log('Admin: <EMAIL> / admin123!@#');
    console.log('User: <EMAIL> / user123!@#');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}
```

## Backup & Recovery

### Automated Backup Strategy

```bash
#!/bin/bash
# scripts/db/backup.sh

set -e

# Configuration
BACKUP_DIR="/var/backups/postgresql"
RETENTION_DAYS=30
COMPRESSION_LEVEL=9
ENCRYPTION_KEY="$BACKUP_ENCRYPTION_KEY"
WEBHOOK_URL="$BACKUP_WEBHOOK_URL"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Function to send notifications
send_notification() {
  local status=$1
  local message=$2
  
  if [ -n "$WEBHOOK_URL" ]; then
    curl -X POST "$WEBHOOK_URL" \
      -H "Content-Type: application/json" \
      -d "{\"text\":\"Database Backup: $status\", \"details\":\"$message\"}"
  fi
}

# Function to create backup
create_backup() {
  local environment=${1:-production}
  local timestamp=$(date +%Y%m%d_%H%M%S)
  local backup_file="$BACKUP_DIR/${environment}_backup_${timestamp}.sql"
  
  echo "💾 Creating backup for $environment environment..."
  
  # Create database dump
  if pg_dump "$DATABASE_URL" \
    --no-password \
    --verbose \
    --clean \
    --no-acl \
    --no-owner \
    --format=custom \
    --compress=$COMPRESSION_LEVEL \
    --file="$backup_file.dump"; then
    
    echo "✅ Database dump created successfully"
    
    # Create SQL version for readability
    pg_dump "$DATABASE_URL" \
      --no-password \
      --clean \
      --no-acl \
      --no-owner \
      --file="$backup_file"
    
    # Compress SQL file
    gzip "$backup_file"
    
    # Encrypt backup if key is provided
    if [ -n "$ENCRYPTION_KEY" ]; then
      echo "🔐 Encrypting backup..."
      openssl enc -aes-256-cbc -salt -in "$backup_file.dump" -out "$backup_file.dump.enc" -k "$ENCRYPTION_KEY"
      rm "$backup_file.dump"
      echo "✅ Backup encrypted"
    fi
    
    # Calculate file size
    local file_size=$(du -h "$backup_file.gz" | cut -f1)
    
    echo "📊 Backup completed:"
    echo "   File: $backup_file.gz"
    echo "   Size: $file_size"
    
    send_notification "SUCCESS" "Backup completed successfully. Size: $file_size"
    
    # Clean old backups
    cleanup_old_backups
    
  else
    echo "❌ Backup failed"
    send_notification "FAILED" "Database backup failed"
    exit 1
  fi
}

# Function to restore backup
restore_backup() {
  local backup_file=$1
  local target_db=${2:-$DATABASE_URL}
  
  if [ -z "$backup_file" ]; then
    echo "❌ Backup file not specified"
    exit 1
  fi
  
  if [ ! -f "$backup_file" ]; then
    echo "❌ Backup file not found: $backup_file"
    exit 1
  fi
  
  echo "⚠️  This will restore the database and overwrite existing data!"
  read -p "Are you sure? (yes/no): " confirm
  
  if [ "$confirm" != "yes" ]; then
    echo "Restore cancelled"
    exit 0
  fi
  
  echo "🔄 Restoring database from $backup_file..."
  
  # Decrypt if needed
  if [[ "$backup_file" == *.enc ]]; then
    if [ -z "$ENCRYPTION_KEY" ]; then
      echo "❌ Encryption key required for encrypted backup"
      exit 1
    fi
    
    echo "🔓 Decrypting backup..."
    openssl enc -aes-256-cbc -d -in "$backup_file" -out "${backup_file%.enc}" -k "$ENCRYPTION_KEY"
    backup_file="${backup_file%.enc}"
  fi
  
  # Restore database
  if [[ "$backup_file" == *.dump ]]; then
    # Custom format
    pg_restore --clean --no-acl --no-owner --verbose -d "$target_db" "$backup_file"
  else
    # SQL format
    if [[ "$backup_file" == *.gz ]]; then
      zcat "$backup_file" | psql "$target_db"
    else
      psql "$target_db" < "$backup_file"
    fi
  fi
  
  if [ $? -eq 0 ]; then
    echo "✅ Database restored successfully"
    send_notification "SUCCESS" "Database restored from $backup_file"
  else
    echo "❌ Database restore failed"
    send_notification "FAILED" "Database restore failed"
    exit 1
  fi
}

# Function to cleanup old backups
cleanup_old_backups() {
  echo "🧹 Cleaning up old backups..."
  
  find "$BACKUP_DIR" -name "*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete
  find "$BACKUP_DIR" -name "*.dump" -type f -mtime +$RETENTION_DAYS -delete
  find "$BACKUP_DIR" -name "*.dump.enc" -type f -mtime +$RETENTION_DAYS -delete
  
  echo "✅ Old backups cleaned up"
}

# Function to list available backups
list_backups() {
  echo "📋 Available backups:"
  ls -lah "$BACKUP_DIR" | grep -E '\.(sql\.gz|dump|dump\.enc)$'
}

# Function to verify backup integrity
verify_backup() {
  local backup_file=$1
  
  if [ -z "$backup_file" ]; then
    echo "❌ Backup file not specified"
    exit 1
  fi
  
  echo "🔍 Verifying backup integrity..."
  
  if [[ "$backup_file" == *.gz ]]; then
    if gzip -t "$backup_file"; then
      echo "✅ Backup file is valid"
    else
      echo "❌ Backup file is corrupted"
      exit 1
    fi
  elif [[ "$backup_file" == *.dump ]]; then
    if pg_restore -l "$backup_file" > /dev/null; then
      echo "✅ Backup file is valid"
    else
      echo "❌ Backup file is corrupted"
      exit 1
    fi
  fi
}

# Main script logic
case $1 in
  "create")
    create_backup $2
    ;;
  "restore")
    restore_backup $2 $3
    ;;
  "list")
    list_backups
    ;;
  "verify")
    verify_backup $2
    ;;
  "cleanup")
    cleanup_old_backups
    ;;
  *)
    echo "Usage: $0 {create|restore|list|verify|cleanup}"
    echo ""
    echo "Commands:"
    echo "  create [environment]     - Create a new backup"
    echo "  restore <file> [db_url]  - Restore from backup file"
    echo "  list                     - List available backups"
    echo "  verify <file>           - Verify backup integrity"
    echo "  cleanup                 - Remove old backups"
    exit 1
    ;;
esac
```

### Backup Monitoring

```typescript
// scripts/db/backup-monitor.ts
import { promises as fs } from 'fs';
import path from 'path';

interface BackupInfo {
  filename: string;
  size: number;
  created: Date;
  environment: string;
  type: 'sql' | 'dump' | 'encrypted';
  integrity: 'verified' | 'corrupted' | 'unknown';
}

export class BackupMonitor {
  private backupDir: string;

  constructor(backupDir: string = '/var/backups/postgresql') {
    this.backupDir = backupDir;
  }

  async getBackupStatus(): Promise<{
    totalBackups: number;
    totalSize: number;
    latestBackup: BackupInfo | null;
    oldestBackup: BackupInfo | null;
    corruptedBackups: BackupInfo[];
    backupsByEnvironment: Record<string, number>;
  }> {
    const backups = await this.listBackups();
    
    const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
    const latestBackup = backups.sort((a, b) => b.created.getTime() - a.created.getTime())[0] || null;
    const oldestBackup = backups.sort((a, b) => a.created.getTime() - b.created.getTime())[0] || null;
    const corruptedBackups = backups.filter(backup => backup.integrity === 'corrupted');
    
    const backupsByEnvironment = backups.reduce((acc, backup) => {
      acc[backup.environment] = (acc[backup.environment] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalBackups: backups.length,
      totalSize,
      latestBackup,
      oldestBackup,
      corruptedBackups,
      backupsByEnvironment
    };
  }

  async listBackups(): Promise<BackupInfo[]> {
    try {
      const files = await fs.readdir(this.backupDir);
      const backupFiles = files.filter(file => 
        file.match(/\.(sql\.gz|dump|dump\.enc)$/)
      );

      const backups: BackupInfo[] = [];

      for (const filename of backupFiles) {
        const filePath = path.join(this.backupDir, filename);
        const stats = await fs.stat(filePath);
        
        const backup: BackupInfo = {
          filename,
          size: stats.size,
          created: stats.mtime,
          environment: this.extractEnvironment(filename),
          type: this.getBackupType(filename),
          integrity: 'unknown'
        };

        backups.push(backup);
      }

      return backups.sort((a, b) => b.created.getTime() - a.created.getTime());
    } catch (error) {
      console.error('Error listing backups:', error);
      return [];
    }
  }

  async checkBackupHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const status = await this.getBackupStatus();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check if we have recent backups
    if (!status.latestBackup) {
      issues.push('No backups found');
      recommendations.push('Create initial backup');
    } else {
      const hoursOld = (Date.now() - status.latestBackup.created.getTime()) / (1000 * 60 * 60);
      
      if (hoursOld > 24) {
        issues.push(`Latest backup is ${Math.round(hoursOld)} hours old`);
        recommendations.push('Create new backup - backups should be created daily');
      }
    }

    // Check for corrupted backups
    if (status.corruptedBackups.length > 0) {
      issues.push(`${status.corruptedBackups.length} corrupted backup(s) found`);
      recommendations.push('Remove corrupted backups and investigate backup process');
    }

    // Check backup frequency by environment
    if (!status.backupsByEnvironment.production) {
      issues.push('No production backups found');
      recommendations.push('Ensure production backup schedule is configured');
    }

    // Check storage usage
    const sizeGB = status.totalSize / (1024 * 1024 * 1024);
    if (sizeGB > 100) {
      recommendations.push('Consider implementing backup rotation - storage usage is high');
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      recommendations
    };
  }

  private extractEnvironment(filename: string): string {
    if (filename.includes('production')) return 'production';
    if (filename.includes('staging')) return 'staging';
    if (filename.includes('development')) return 'development';
    return 'unknown';
  }

  private getBackupType(filename: string): 'sql' | 'dump' | 'encrypted' {
    if (filename.endsWith('.dump.enc')) return 'encrypted';
    if (filename.endsWith('.dump')) return 'dump';
    return 'sql';
  }

  async generateReport(): Promise<string> {
    const status = await this.getBackupStatus();
    const health = await this.checkBackupHealth();

    return `
# Backup Status Report

## Overview
- Total Backups: ${status.totalBackups}
- Total Size: ${(status.totalSize / 1024 / 1024 / 1024).toFixed(2)} GB
- Health Status: ${health.isHealthy ? '✅ Healthy' : '❌ Issues Found'}

## Latest Backup
${status.latestBackup ? `
- File: ${status.latestBackup.filename}
- Environment: ${status.latestBackup.environment}
- Created: ${status.latestBackup.created.toISOString()}
- Size: ${(status.latestBackup.size / 1024 / 1024).toFixed(2)} MB
` : 'No backups found'}

## Backups by Environment
${Object.entries(status.backupsByEnvironment)
  .map(([env, count]) => `- ${env}: ${count} backup(s)`)
  .join('\n')}

## Issues
${health.issues.length > 0 ? health.issues.map(issue => `- ❌ ${issue}`).join('\n') : '- ✅ No issues found'}

## Recommendations
${health.recommendations.length > 0 ? health.recommendations.map(rec => `- 💡 ${rec}`).join('\n') : '- ✅ No recommendations'}

---
Generated: ${new Date().toISOString()}
    `.trim();
  }
}
```

## Performance Optimization

### Query Optimization

```typescript
// app/lib/database/query-optimizer.ts
export class QueryOptimizer {
  static async analyzeSlowQueries(): Promise<SlowQueryReport[]> {
    const slowQueries = await db.execute(sql`
      SELECT 
        query,
        calls,
        total_exec_time,
        mean_exec_time,
        stddev_exec_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
      FROM pg_stat_statements 
      WHERE mean_exec_time > 100  -- queries taking more than 100ms on average
      ORDER BY mean_exec_time DESC 
      LIMIT 20
    `);

    return slowQueries.rows.map(row => ({
      query: row.query,
      calls: row.calls,
      totalTime: row.total_exec_time,
      meanTime: row.mean_exec_time,
      stddevTime: row.stddev_exec_time,
      rows: row.rows,
      hitPercent: row.hit_percent,
      recommendations: this.generateOptimizationRecommendations(row)
    }));
  }

  static async checkIndexUsage(): Promise<IndexUsageReport[]> {
    const indexStats = await db.execute(sql`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch,
        idx_scan,
        pg_size_pretty(pg_relation_size(indexrelid)) as size
      FROM pg_stat_user_indexes 
      ORDER BY idx_scan ASC, pg_relation_size(indexrelid) DESC
    `);

    return indexStats.rows.map(row => ({
      schema: row.schemaname,
      table: row.tablename,
      index: row.indexname,
      scans: row.idx_scan,
      tuplesRead: row.idx_tup_read,
      tuplesFetched: row.idx_tup_fetch,
      size: row.size,
      efficiency: row.idx_scan === 0 ? 0 : (row.idx_tup_fetch / row.idx_scan),
      recommendation: row.idx_scan === 0 ? 'Consider dropping unused index' : 
                     row.idx_scan < 10 ? 'Low usage - review necessity' : 'Well utilized'
    }));
  }

  static async suggestIndexes(): Promise<IndexSuggestion[]> {
    // Analyze missing indexes based on query patterns
    const suggestions: IndexSuggestion[] = [];

    // Check for sequential scans on large tables
    const seqScans = await db.execute(sql`
      SELECT 
        schemaname,
        tablename,
        seq_scan,
        seq_tup_read,
        n_tup_ins + n_tup_upd + n_tup_del as modifications,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
      FROM pg_stat_user_tables 
      WHERE seq_scan > 100 
        AND seq_tup_read > 10000
      ORDER BY seq_tup_read DESC
    `);

    for (const scan of seqScans.rows) {
      suggestions.push({
        table: scan.tablename,
        type: 'btree',
        columns: [], // Would need query analysis to determine columns
        reason: `High sequential scan count (${scan.seq_scan}) on large table`,
        estimatedBenefit: 'High',
        priority: scan.seq_tup_read > 100000 ? 'High' : 'Medium'
      });
    }

    return suggestions;
  }

  private static generateOptimizationRecommendations(queryStats: any): string[] {
    const recommendations: string[] = [];

    if (queryStats.mean_exec_time > 1000) {
      recommendations.push('Query takes >1s on average - consider optimization');
    }

    if (queryStats.hit_percent < 95) {
      recommendations.push('Low buffer hit ratio - may benefit from indexing');
    }

    if (queryStats.stddev_exec_time > queryStats.mean_exec_time) {
      recommendations.push('High execution time variance - check for plan instability');
    }

    return recommendations;
  }

  static async optimizeTableStatistics(): Promise<void> {
    console.log('📊 Updating table statistics...');

    // Get all user tables
    const tables = await db.execute(sql`
      SELECT schemaname, tablename 
      FROM pg_stat_user_tables
    `);

    for (const table of tables.rows) {
      await db.execute(sql`
        ANALYZE ${sql.identifier(table.schemaname)}.${sql.identifier(table.tablename)}
      `);
    }

    console.log('✅ Table statistics updated');
  }

  static async checkConnectionPoolStatus(): Promise<ConnectionPoolStatus> {
    const stats = await db.execute(sql`
      SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections,
        count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
      FROM pg_stat_activity 
      WHERE pid <> pg_backend_pid()
    `);

    const row = stats.rows[0];

    return {
      total: row.total_connections,
      active: row.active_connections,
      idle: row.idle_connections,
      idleInTransaction: row.idle_in_transaction,
      utilization: (row.active_connections / row.total_connections) * 100
    };
  }
}

interface SlowQueryReport {
  query: string;
  calls: number;
  totalTime: number;
  meanTime: number;
  stddevTime: number;
  rows: number;
  hitPercent: number;
  recommendations: string[];
}

interface IndexUsageReport {
  schema: string;
  table: string;
  index: string;
  scans: number;
  tuplesRead: number;
  tuplesFetched: number;
  size: string;
  efficiency: number;
  recommendation: string;
}

interface IndexSuggestion {
  table: string;
  type: string;
  columns: string[];
  reason: string;
  estimatedBenefit: string;
  priority: string;
}

interface ConnectionPoolStatus {
  total: number;
  active: number;
  idle: number;
  idleInTransaction: number;
  utilization: number;
}
```

### Database Maintenance

```bash
#!/bin/bash
# scripts/db/maintenance.sh

set -e

echo "🔧 Database Maintenance Script"

# Function to run VACUUM
run_vacuum() {
  local mode=${1:-analyze}
  
  echo "🧹 Running VACUUM $mode..."
  
  case $mode in
    "full")
      echo "⚠️  VACUUM FULL will lock tables - use during maintenance window"
      read -p "Continue? (yes/no): " confirm
      if [ "$confirm" != "yes" ]; then
        exit 0
      fi
      psql "$DATABASE_URL" -c "VACUUM FULL;"
      ;;
    "analyze")
      psql "$DATABASE_URL" -c "VACUUM ANALYZE;"
      ;;
    *)
      psql "$DATABASE_URL" -c "VACUUM;"
      ;;
  esac
  
  echo "✅ VACUUM completed"
}

# Function to reindex
run_reindex() {
  local target=${1:-all}
  
  echo "🔄 Reindexing $target..."
  
  case $target in
    "all")
      psql "$DATABASE_URL" -c "REINDEX DATABASE \"$DB_NAME\";"
      ;;
    *)
      psql "$DATABASE_URL" -c "REINDEX TABLE $target;"
      ;;
  esac
  
  echo "✅ Reindex completed"
}

# Function to update statistics
update_statistics() {
  echo "📊 Updating table statistics..."
  
  psql "$DATABASE_URL" -c "ANALYZE;"
  
  echo "✅ Statistics updated"
}

# Function to check bloat
check_bloat() {
  echo "🔍 Checking table bloat..."
  
  psql "$DATABASE_URL" -c "
    SELECT 
      schemaname,
      tablename,
      pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
      pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
      round(100 * pg_relation_size(schemaname||'.'||tablename) / pg_total_relation_size(schemaname||'.'||tablename)) as table_percent
    FROM pg_stat_user_tables 
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    LIMIT 10;
  "
}

# Function to optimize performance
optimize_performance() {
  echo "⚡ Running performance optimization..."
  
  # Update statistics
  update_statistics
  
  # Vacuum analyze
  run_vacuum analyze
  
  # Check for unused indexes
  echo "🔍 Checking for unused indexes..."
  psql "$DATABASE_URL" -c "
    SELECT 
      schemaname,
      tablename,
      indexname,
      idx_scan,
      pg_size_pretty(pg_relation_size(indexrelid)) as size
    FROM pg_stat_user_indexes 
    WHERE idx_scan < 10
    ORDER BY pg_relation_size(indexrelid) DESC;
  "
  
  echo "✅ Performance optimization completed"
}

# Function to generate maintenance report
generate_report() {
  local report_file="maintenance_report_$(date +%Y%m%d_%H%M%S).txt"
  
  echo "📋 Generating maintenance report: $report_file"
  
  {
    echo "Database Maintenance Report"
    echo "=========================="
    echo "Generated: $(date)"
    echo ""
    
    echo "Database Size:"
    psql "$DATABASE_URL" -c "
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as database_size;
    "
    
    echo ""
    echo "Largest Tables:"
    psql "$DATABASE_URL" -c "
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_stat_get_live_tuples(c.oid) as live_tuples,
        pg_stat_get_dead_tuples(c.oid) as dead_tuples
      FROM pg_stat_user_tables s
      JOIN pg_class c ON c.relname = s.tablename
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
      LIMIT 10;
    "
    
    echo ""
    echo "Index Usage:"
    psql "$DATABASE_URL" -c "
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan,
        pg_size_pretty(pg_relation_size(indexrelid)) as size
      FROM pg_stat_user_indexes 
      ORDER BY idx_scan DESC
      LIMIT 10;
    "
    
  } > "$report_file"
  
  echo "✅ Report generated: $report_file"
}

# Main script logic
case $1 in
  "vacuum")
    run_vacuum $2
    ;;
  "reindex")
    run_reindex $2
    ;;
  "analyze")
    update_statistics
    ;;
  "bloat")
    check_bloat
    ;;
  "optimize")
    optimize_performance
    ;;
  "report")
    generate_report
    ;;
  "all")
    echo "🔧 Running full maintenance..."
    update_statistics
    run_vacuum analyze
    check_bloat
    generate_report
    echo "✅ Full maintenance completed"
    ;;
  *)
    echo "Usage: $0 {vacuum|reindex|analyze|bloat|optimize|report|all}"
    echo ""
    echo "Commands:"
    echo "  vacuum [mode]    - Run VACUUM (modes: analyze, full)"
    echo "  reindex [table]  - Reindex database or specific table"
    echo "  analyze          - Update table statistics"
    echo "  bloat            - Check table bloat"
    echo "  optimize         - Run performance optimization"
    echo "  report           - Generate maintenance report"
    echo "  all              - Run full maintenance routine"
    exit 1
    ;;
esac
```

## Production Operations

### Database Monitoring Dashboard

```typescript
// app/admin/database/page.tsx
import { DatabaseMonitor } from '@/components/admin/database-monitor';
import { QueryOptimizer } from '@/lib/database/query-optimizer';
import { BackupMonitor } from '@/scripts/db/backup-monitor';

export default async function DatabasePage() {
  // Get database statistics
  const [
    slowQueries,
    indexUsage,
    connectionStatus,
    backupStatus
  ] = await Promise.all([
    QueryOptimizer.analyzeSlowQueries(),
    QueryOptimizer.checkIndexUsage(),
    QueryOptimizer.checkConnectionPoolStatus(),
    new BackupMonitor().getBackupStatus()
  ]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Database Management</h1>
        <div className="space-x-2">
          <Button variant="outline">
            Export Report
          </Button>
          <Button>
            Run Maintenance
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Connection Pool</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {connectionStatus.active}/{connectionStatus.total}
            </div>
            <p className="text-sm text-muted-foreground">
              {connectionStatus.utilization.toFixed(1)}% utilization
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Slow Queries</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {slowQueries.length}
            </div>
            <p className="text-sm text-muted-foreground">
              Queries > 100ms avg
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Unused Indexes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {indexUsage.filter(idx => idx.scans === 0).length}
            </div>
            <p className="text-sm text-muted-foreground">
              Zero scan count
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Last Backup</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {backupStatus.latestBackup ? 
                formatDistanceToNow(backupStatus.latestBackup.created) + ' ago' :
                'None'
              }
            </div>
            <p className="text-sm text-muted-foreground">
              {backupStatus.totalBackups} total backups
            </p>
          </CardContent>
        </Card>
      </div>

      <DatabaseMonitor 
        slowQueries={slowQueries}
        indexUsage={indexUsage}
        connectionStatus={connectionStatus}
        backupStatus={backupStatus}
      />
    </div>
  );
}
```

### Operational Scripts Package

```json
{
  "scripts": {
    "db:migrate": "drizzle-kit generate:pg && drizzle-kit push:pg",
    "db:migrate:production": "scripts/db/migrate.sh migrate production",
    "db:seed": "tsx scripts/db/seed.ts",
    "db:backup": "scripts/db/backup.sh create",
    "db:backup:production": "scripts/db/backup.sh create production",
    "db:restore": "scripts/db/backup.sh restore",
    "db:maintenance": "scripts/db/maintenance.sh all",
    "db:vacuum": "scripts/db/maintenance.sh vacuum",
    "db:analyze": "scripts/db/maintenance.sh analyze",
    "db:check": "scripts/db/health-check.sh",
    "db:monitor": "tsx scripts/db/monitor.ts",
    "db:report": "scripts/db/maintenance.sh report"
  }
}
```

---

**Database Management Guide Version**: 1.0.0  
**Last Updated**: 2024-08-14  
**Database Version**: PostgreSQL 16  
**ORM**: Drizzle ORM  
**Next Review**: 2024-11-14  
**Database Contact**: <EMAIL>