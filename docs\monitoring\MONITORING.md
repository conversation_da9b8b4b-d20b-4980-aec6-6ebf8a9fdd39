# Monitoring & Alerting Guide

Comprehensive monitoring and alerting setup documentation for the getintheq.space platform, covering application performance monitoring, infrastructure monitoring, log aggregation, alerting strategies, and incident response procedures.

## Table of Contents

- [Monitoring Overview](#monitoring-overview)
- [Application Performance Monitoring](#application-performance-monitoring)
- [Infrastructure Monitoring](#infrastructure-monitoring)
- [Log Management](#log-management)
- [Alerting Strategy](#alerting-strategy)
- [Metrics & KPIs](#metrics--kpis)
- [Dashboard Configuration](#dashboard-configuration)
- [Incident Response](#incident-response)
- [Health Checks](#health-checks)
- [Synthetic Monitoring](#synthetic-monitoring)
- [Cost Monitoring](#cost-monitoring)
- [Security Monitoring](#security-monitoring)
- [Setup Instructions](#setup-instructions)
- [Troubleshooting](#troubleshooting)

## Monitoring Overview

The getintheq.space platform uses a multi-layered monitoring approach to ensure optimal performance, availability, and user experience across all environments.

### Monitoring Architecture

```mermaid
graph TB
    subgraph "Application Layer"
        App[Next.js Application]
        API[API Routes]
        DB[(PostgreSQL Database)]
    end

    subgraph "Monitoring Stack"
        APM[Application Performance Monitoring]
        Logs[Log Aggregation]
        Metrics[Metrics Collection]
        Traces[Distributed Tracing]
    end

    subgraph "Platforms & Tools"
        Vercel[Vercel Analytics]
        Sentry[Sentry Error Tracking]
        DataDog[DataDog APM]
        LogRocket[LogRocket Session Replay]
        Grafana[Grafana Dashboards]
        Prometheus[Prometheus Metrics]
    end

    subgraph "Alerting"
        PagerDuty[PagerDuty]
        Slack[Slack Notifications]
        Email[Email Alerts]
        SMS[SMS Alerts]
    end

    subgraph "External Monitoring"
        Pingdom[Pingdom Uptime]
        StatusPage[Status Page]
        Lighthouse[Lighthouse CI]
    end

    App --> APM
    API --> APM
    DB --> Metrics
    
    APM --> Sentry
    APM --> DataDog
    APM --> LogRocket
    
    Logs --> DataDog
    Metrics --> Prometheus
    Traces --> DataDog
    
    Prometheus --> Grafana
    DataDog --> PagerDuty
    Sentry --> Slack
    
    Pingdom --> StatusPage
    Lighthouse --> Grafana
```

### Monitoring Pillars

1. **Golden Signals**: Latency, Traffic, Errors, Saturation (USE method)
2. **SLIs/SLOs**: Service Level Indicators and Objectives
3. **Error Tracking**: Application errors and exceptions
4. **Performance Monitoring**: Web Vitals and application performance
5. **Infrastructure Monitoring**: Server resources and dependencies
6. **Security Monitoring**: Security events and anomalies
7. **Business Metrics**: User engagement and conversion tracking

## Application Performance Monitoring

### Vercel Analytics Integration

```typescript
// app/lib/monitoring/vercel-analytics.ts
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';

export function VercelMonitoring() {
  return (
    <>
      <Analytics />
      <SpeedInsights />
    </>
  );
}

// Custom event tracking
export function trackEvent(eventName: string, properties?: Record<string, any>) {
  if (typeof window !== 'undefined' && window.va) {
    window.va('track', eventName, properties);
  }
}

// Performance monitoring
export function trackPerformance(metric: string, value: number, unit: string = 'ms') {
  if (typeof window !== 'undefined' && window.va) {
    window.va('track', 'Performance', {
      metric,
      value,
      unit,
      url: window.location.pathname,
      timestamp: Date.now()
    });
  }
}

// Error tracking
export function trackError(error: Error, context?: Record<string, any>) {
  if (typeof window !== 'undefined' && window.va) {
    window.va('track', 'Error', {
      error: error.message,
      stack: error.stack,
      context,
      url: window.location.pathname,
      timestamp: Date.now()
    });
  }
}
```

### Sentry Error Tracking

```typescript
// app/lib/monitoring/sentry.ts
import * as Sentry from '@sentry/nextjs';

export function initSentry() {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    environment: process.env.NODE_ENV,
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // Performance monitoring
    profilesSampleRate: 0.1,
    
    // Error filtering
    beforeSend(event, hint) {
      // Filter out known issues
      if (event.exception) {
        const error = hint.originalException as Error;
        
        // Skip network errors
        if (error?.name === 'NetworkError') {
          return null;
        }
        
        // Skip cancelled requests
        if (error?.message?.includes('AbortError')) {
          return null;
        }
      }
      
      return event;
    },
    
    // Performance monitoring configuration
    beforeSendTransaction(event) {
      // Sample based on transaction name
      if (event.transaction?.includes('_next/static')) {
        return null; // Skip static asset transactions
      }
      
      return event;
    },
    
    // Integration configuration
    integrations: [
      new Sentry.BrowserTracing({
        tracePropagationTargets: [
          'localhost',
          /^https:\/\/getintheq\.space/,
          /^https:\/\/staging\.getintheq\.space/
        ],
      }),
      new Sentry.Replay({
        maskAllText: false,
        blockAllMedia: false,
        maskAllInputs: true,
      }),
    ],
  });
}

// Custom error reporting
export function reportError(error: Error, context?: Record<string, any>) {
  Sentry.withScope((scope) => {
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        scope.setContext(key, value);
      });
    }
    
    scope.setLevel('error');
    Sentry.captureException(error);
  });
}

// Performance monitoring
export function startTransaction(name: string, op: string) {
  return Sentry.startTransaction({ name, op });
}

// User context
export function setUserContext(user: { id: string; email?: string; role?: string }) {
  Sentry.setUser({
    id: user.id,
    email: user.email,
    role: user.role,
  });
}
```

### DataDog APM Integration

```typescript
// app/lib/monitoring/datadog.ts
import { datadogRum } from '@datadog/browser-rum';
import { datadogLogs } from '@datadog/browser-logs';

export function initDataDog() {
  if (typeof window === 'undefined') return;

  // RUM (Real User Monitoring)
  datadogRum.init({
    applicationId: process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID!,
    clientToken: process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN!,
    site: 'datadoghq.com',
    service: 'getintheq-frontend',
    env: process.env.NODE_ENV,
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    
    // Performance monitoring
    sessionSampleRate: 100,
    premiumSampleRate: 100,
    trackUserInteractions: true,
    trackResources: true,
    trackLongTasks: true,
    
    // Privacy settings
    defaultPrivacyLevel: 'mask-user-input',
    
    // Custom configuration
    beforeSend: (event) => {
      // Filter sensitive data
      if (event.type === 'resource' && event.resource.url.includes('api/auth')) {
        return false;
      }
      return true;
    }
  });

  // Browser logs
  datadogLogs.init({
    clientToken: process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN!,
    site: 'datadoghq.com',
    service: 'getintheq-frontend',
    env: process.env.NODE_ENV,
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    forwardErrorsToLogs: true,
    sessionSampleRate: 100,
  });
}

// Custom metrics
export function addCustomMetric(name: string, value: number, tags?: Record<string, string>) {
  datadogRum.addAction(name, {
    value,
    tags,
    timestamp: Date.now()
  });
}

// Custom logging
export function logEvent(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: any) {
  datadogLogs.logger[level](message, context);
}

// Performance tracking
export function trackWebVitals() {
  if (typeof window === 'undefined') return;

  // Track Core Web Vitals
  import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
    getCLS((metric) => {
      addCustomMetric('web_vitals.cls', metric.value, {
        metric_type: 'cls',
        page: window.location.pathname
      });
    });

    getFID((metric) => {
      addCustomMetric('web_vitals.fid', metric.value, {
        metric_type: 'fid',
        page: window.location.pathname
      });
    });

    getFCP((metric) => {
      addCustomMetric('web_vitals.fcp', metric.value, {
        metric_type: 'fcp',
        page: window.location.pathname
      });
    });

    getLCP((metric) => {
      addCustomMetric('web_vitals.lcp', metric.value, {
        metric_type: 'lcp',
        page: window.location.pathname
      });
    });

    getTTFB((metric) => {
      addCustomMetric('web_vitals.ttfb', metric.value, {
        metric_type: 'ttfb',
        page: window.location.pathname
      });
    });
  });
}
```

### Custom Performance Monitoring

```typescript
// app/lib/monitoring/performance.ts
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  unit?: string;
}

export class PerformanceMonitor {
  private static metrics: PerformanceMetric[] = [];
  private static batchSize = 50;
  private static flushInterval = 30000; // 30 seconds

  static init() {
    if (typeof window === 'undefined') return;

    // Start periodic flushing
    setInterval(() => {
      this.flush();
    }, this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flush();
    });

    // Track navigation timing
    this.trackNavigationTiming();

    // Track resource timing
    this.trackResourceTiming();

    // Track custom timing
    this.trackCustomTiming();
  }

  static addMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);

    // Auto-flush if batch is full
    if (this.metrics.length >= this.batchSize) {
      this.flush();
    }
  }

  static trackTiming(name: string, duration: number, tags?: Record<string, string>) {
    this.addMetric({
      name: `timing.${name}`,
      value: duration,
      timestamp: Date.now(),
      tags,
      unit: 'ms'
    });
  }

  static trackCounter(name: string, value: number = 1, tags?: Record<string, string>) {
    this.addMetric({
      name: `counter.${name}`,
      value,
      timestamp: Date.now(),
      tags
    });
  }

  static trackGauge(name: string, value: number, tags?: Record<string, string>) {
    this.addMetric({
      name: `gauge.${name}`,
      value,
      timestamp: Date.now(),
      tags
    });
  }

  private static trackNavigationTiming() {
    if (!window.performance || !window.performance.timing) return;

    const timing = window.performance.timing;
    const navigationStart = timing.navigationStart;

    const metrics = [
      { name: 'dns_lookup', value: timing.domainLookupEnd - timing.domainLookupStart },
      { name: 'tcp_connect', value: timing.connectEnd - timing.connectStart },
      { name: 'request_response', value: timing.responseEnd - timing.requestStart },
      { name: 'dom_processing', value: timing.domComplete - timing.domLoading },
      { name: 'page_load', value: timing.loadEventEnd - navigationStart },
    ];

    metrics.forEach(metric => {
      if (metric.value > 0) {
        this.trackTiming(metric.name, metric.value, {
          page: window.location.pathname,
          type: 'navigation'
        });
      }
    });
  }

  private static trackResourceTiming() {
    if (!window.performance || !window.performance.getEntriesByType) return;

    const resources = window.performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    resources.forEach(resource => {
      // Only track significant resources
      if (resource.duration > 10) {
        const resourceType = this.getResourceType(resource.name);
        
        this.trackTiming('resource_load', resource.duration, {
          resource_type: resourceType,
          resource_name: resource.name.split('/').pop() || 'unknown',
          page: window.location.pathname
        });
      }
    });
  }

  private static trackCustomTiming() {
    // Track Paint timing
    if (window.performance && window.performance.getEntriesByType) {
      const paintEntries = window.performance.getEntriesByType('paint');
      
      paintEntries.forEach(entry => {
        this.trackTiming(entry.name.replace('-', '_'), entry.startTime, {
          page: window.location.pathname,
          type: 'paint'
        });
      });
    }

    // Track Layout Shift
    if ('LayoutShift' in window) {
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            this.trackGauge('layout_shift', (entry as any).value, {
              page: window.location.pathname
            });
          }
        }
      }).observe({ entryTypes: ['layout-shift'] });
    }
  }

  private static getResourceType(url: string): string {
    if (url.includes('.css')) return 'css';
    if (url.includes('.js')) return 'javascript';
    if (url.includes('.png') || url.includes('.jpg') || url.includes('.jpeg') || url.includes('.webp')) return 'image';
    if (url.includes('.woff') || url.includes('.ttf')) return 'font';
    if (url.includes('/api/')) return 'api';
    return 'other';
  }

  private static async flush() {
    if (this.metrics.length === 0) return;

    const metricsToSend = [...this.metrics];
    this.metrics = [];

    try {
      await fetch('/api/monitoring/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ metrics: metricsToSend }),
      });
    } catch (error) {
      console.error('Failed to send metrics:', error);
      // Re-add metrics back if sending failed
      this.metrics.unshift(...metricsToSend);
    }
  }
}

// Utility functions for measuring performance
export function measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
  const start = performance.now();
  
  return fn().finally(() => {
    const duration = performance.now() - start;
    PerformanceMonitor.trackTiming(name, duration);
  });
}

export function measureSync<T>(name: string, fn: () => T): T {
  const start = performance.now();
  
  try {
    return fn();
  } finally {
    const duration = performance.now() - start;
    PerformanceMonitor.trackTiming(name, duration);
  }
}
```

## Infrastructure Monitoring

### Server-Side Monitoring

```typescript
// app/lib/monitoring/server.ts
import { NextRequest, NextResponse } from 'next/server';
import { performance } from 'perf_hooks';

interface ServerMetric {
  timestamp: number;
  endpoint: string;
  method: string;
  statusCode: number;
  duration: number;
  memoryUsage: NodeJS.MemoryUsage;
  userAgent?: string;
  ip?: string;
}

export class ServerMonitor {
  private static metrics: ServerMetric[] = [];

  static trackRequest(req: NextRequest, res: NextResponse, duration: number) {
    const metric: ServerMetric = {
      timestamp: Date.now(),
      endpoint: req.nextUrl.pathname,
      method: req.method,
      statusCode: res.status,
      duration,
      memoryUsage: process.memoryUsage(),
      userAgent: req.headers.get('user-agent') || undefined,
      ip: this.getClientIP(req),
    };

    this.metrics.push(metric);
    this.processMetric(metric);
  }

  private static getClientIP(req: NextRequest): string {
    const xForwardedFor = req.headers.get('x-forwarded-for');
    const xRealIP = req.headers.get('x-real-ip');
    
    if (xForwardedFor) {
      return xForwardedFor.split(',')[0].trim();
    }
    
    if (xRealIP) {
      return xRealIP;
    }
    
    return 'unknown';
  }

  private static processMetric(metric: ServerMetric) {
    // Send to external monitoring services
    this.sendToDataDog(metric);
    this.checkAlerts(metric);
  }

  private static sendToDataDog(metric: ServerMetric) {
    // Send metric to DataDog
    if (process.env.DATADOG_API_KEY) {
      // Implementation for DataDog API
    }
  }

  private static checkAlerts(metric: ServerMetric) {
    // Check for alert conditions
    if (metric.duration > 5000) { // 5 seconds
      console.warn(`Slow request detected: ${metric.endpoint} took ${metric.duration}ms`);
    }

    if (metric.statusCode >= 500) {
      console.error(`Server error: ${metric.endpoint} returned ${metric.statusCode}`);
    }

    if (metric.memoryUsage.heapUsed > 512 * 1024 * 1024) { // 512MB
      console.warn(`High memory usage: ${Math.round(metric.memoryUsage.heapUsed / 1024 / 1024)}MB`);
    }
  }

  static getHealthStatus() {
    const now = Date.now();
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 60000); // Last minute

    if (recentMetrics.length === 0) {
      return { status: 'unknown', metrics: {} };
    }

    const avgDuration = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length;
    const errorRate = recentMetrics.filter(m => m.statusCode >= 400).length / recentMetrics.length;
    const p95Duration = this.calculatePercentile(recentMetrics.map(m => m.duration), 95);

    const status = errorRate > 0.1 || avgDuration > 2000 ? 'unhealthy' : 'healthy';

    return {
      status,
      metrics: {
        avgDuration: Math.round(avgDuration),
        errorRate: Math.round(errorRate * 100),
        p95Duration: Math.round(p95Duration),
        requestCount: recentMetrics.length,
        memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      }
    };
  }

  private static calculatePercentile(values: number[], percentile: number): number {
    const sorted = values.sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }
}

// Middleware for automatic request tracking
export function withMonitoring<T extends any[], R>(
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const start = performance.now();
    
    try {
      const result = await fn(...args);
      const duration = performance.now() - start;
      
      // Log successful execution
      console.log(`Function executed successfully in ${duration.toFixed(2)}ms`);
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      
      // Log error
      console.error(`Function failed after ${duration.toFixed(2)}ms:`, error);
      
      throw error;
    }
  };
}
```

### Database Monitoring

```typescript
// app/lib/monitoring/database.ts
import { db } from '@/lib/database';
import { sql } from 'drizzle-orm';

interface DatabaseMetrics {
  connections: {
    active: number;
    idle: number;
    total: number;
  };
  performance: {
    avgQueryTime: number;
    slowQueries: number;
    lockWaits: number;
  };
  storage: {
    databaseSize: number;
    tablesSizes: Record<string, number>;
  };
  health: 'healthy' | 'warning' | 'critical';
}

export class DatabaseMonitor {
  static async getMetrics(): Promise<DatabaseMetrics> {
    try {
      // Get connection stats
      const connectionStats = await this.getConnectionStats();
      
      // Get performance stats
      const performanceStats = await this.getPerformanceStats();
      
      // Get storage stats
      const storageStats = await this.getStorageStats();
      
      // Determine overall health
      const health = this.determineHealth(connectionStats, performanceStats);

      return {
        connections: connectionStats,
        performance: performanceStats,
        storage: storageStats,
        health
      };
    } catch (error) {
      console.error('Failed to get database metrics:', error);
      return {
        connections: { active: 0, idle: 0, total: 0 },
        performance: { avgQueryTime: 0, slowQueries: 0, lockWaits: 0 },
        storage: { databaseSize: 0, tablesSizes: {} },
        health: 'critical'
      };
    }
  }

  private static async getConnectionStats() {
    const result = await db.execute(sql`
      SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections
      FROM pg_stat_activity 
      WHERE pid <> pg_backend_pid()
    `);

    const row = result.rows[0];
    return {
      total: row.total_connections,
      active: row.active_connections,
      idle: row.idle_connections
    };
  }

  private static async getPerformanceStats() {
    // Check if pg_stat_statements extension is available
    const extensionCheck = await db.execute(sql`
      SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
      ) as has_extension
    `);

    if (!extensionCheck.rows[0].has_extension) {
      return { avgQueryTime: 0, slowQueries: 0, lockWaits: 0 };
    }

    const result = await db.execute(sql`
      SELECT 
        avg(mean_exec_time) as avg_query_time,
        count(*) FILTER (WHERE mean_exec_time > 1000) as slow_queries
      FROM pg_stat_statements
    `);

    const lockWaitsResult = await db.execute(sql`
      SELECT count(*) as lock_waits
      FROM pg_stat_activity 
      WHERE wait_event_type = 'Lock'
    `);

    const row = result.rows[0];
    const lockWaits = lockWaitsResult.rows[0].lock_waits;

    return {
      avgQueryTime: Math.round(row.avg_query_time || 0),
      slowQueries: row.slow_queries || 0,
      lockWaits: lockWaits || 0
    };
  }

  private static async getStorageStats() {
    const databaseSizeResult = await db.execute(sql`
      SELECT pg_database_size(current_database()) as database_size
    `);

    const tableSizesResult = await db.execute(sql`
      SELECT 
        schemaname,
        tablename,
        pg_total_relation_size(schemaname||'.'||tablename) as size
      FROM pg_stat_user_tables
      ORDER BY size DESC
      LIMIT 10
    `);

    const databaseSize = databaseSizeResult.rows[0].database_size;
    const tablesSizes: Record<string, number> = {};

    tableSizesResult.rows.forEach(row => {
      tablesSizes[`${row.schemaname}.${row.tablename}`] = row.size;
    });

    return {
      databaseSize,
      tablesSizes
    };
  }

  private static determineHealth(
    connections: any,
    performance: any
  ): 'healthy' | 'warning' | 'critical' {
    // Critical conditions
    if (connections.active > 80 || performance.lockWaits > 10) {
      return 'critical';
    }

    // Warning conditions
    if (connections.active > 50 || performance.avgQueryTime > 500 || performance.slowQueries > 5) {
      return 'warning';
    }

    return 'healthy';
  }

  static async checkHealth(): Promise<boolean> {
    try {
      await db.execute(sql`SELECT 1`);
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }
}
```

## Alerting Strategy

### Alert Configuration

```typescript
// app/lib/monitoring/alerts.ts
interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  severity: 'info' | 'warning' | 'critical';
  channels: AlertChannel[];
  enabled: boolean;
  cooldown: number; // minutes
}

interface AlertChannel {
  type: 'slack' | 'email' | 'pagerduty' | 'webhook';
  config: Record<string, any>;
}

export const ALERT_RULES: AlertRule[] = [
  {
    id: 'high_error_rate',
    name: 'High Error Rate',
    condition: 'error_rate > threshold',
    threshold: 5, // 5%
    severity: 'critical',
    channels: [
      { type: 'slack', config: { channel: '#alerts' } },
      { type: 'pagerduty', config: { service: 'web-app' } }
    ],
    enabled: true,
    cooldown: 15
  },
  {
    id: 'slow_response_time',
    name: 'Slow Response Time',
    condition: 'avg_response_time > threshold',
    threshold: 2000, // 2 seconds
    severity: 'warning',
    channels: [
      { type: 'slack', config: { channel: '#alerts' } }
    ],
    enabled: true,
    cooldown: 30
  },
  {
    id: 'high_memory_usage',
    name: 'High Memory Usage',
    condition: 'memory_usage > threshold',
    threshold: 85, // 85%
    severity: 'warning',
    channels: [
      { type: 'slack', config: { channel: '#infrastructure' } }
    ],
    enabled: true,
    cooldown: 60
  },
  {
    id: 'database_connection_pool_exhausted',
    name: 'Database Connection Pool Exhausted',
    condition: 'db_connections_used > threshold',
    threshold: 90, // 90%
    severity: 'critical',
    channels: [
      { type: 'slack', config: { channel: '#alerts' } },
      { type: 'email', config: { recipients: ['<EMAIL>'] } }
    ],
    enabled: true,
    cooldown: 10
  },
  {
    id: 'cert_expiry_warning',
    name: 'SSL Certificate Expiry Warning',
    condition: 'cert_days_until_expiry < threshold',
    threshold: 30, // 30 days
    severity: 'warning',
    channels: [
      { type: 'slack', config: { channel: '#infrastructure' } },
      { type: 'email', config: { recipients: ['<EMAIL>'] } }
    ],
    enabled: true,
    cooldown: 1440 // 24 hours
  }
];

export class AlertManager {
  private static firedAlerts = new Map<string, number>();

  static async evaluateRules(metrics: any) {
    for (const rule of ALERT_RULES) {
      if (!rule.enabled) continue;

      const shouldAlert = this.evaluateCondition(rule, metrics);
      
      if (shouldAlert && this.canFireAlert(rule.id, rule.cooldown)) {
        await this.fireAlert(rule, metrics);
        this.firedAlerts.set(rule.id, Date.now());
      }
    }
  }

  private static evaluateCondition(rule: AlertRule, metrics: any): boolean {
    const { condition, threshold } = rule;
    
    switch (rule.id) {
      case 'high_error_rate':
        return metrics.errorRate > threshold;
      
      case 'slow_response_time':
        return metrics.avgResponseTime > threshold;
      
      case 'high_memory_usage':
        return metrics.memoryUsagePercent > threshold;
      
      case 'database_connection_pool_exhausted':
        return metrics.dbConnectionsUsedPercent > threshold;
      
      case 'cert_expiry_warning':
        return metrics.certDaysUntilExpiry < threshold;
      
      default:
        return false;
    }
  }

  private static canFireAlert(ruleId: string, cooldownMinutes: number): boolean {
    const lastFired = this.firedAlerts.get(ruleId);
    if (!lastFired) return true;

    const cooldownMs = cooldownMinutes * 60 * 1000;
    return Date.now() - lastFired > cooldownMs;
  }

  private static async fireAlert(rule: AlertRule, metrics: any) {
    console.log(`🚨 ALERT: ${rule.name}`);

    for (const channel of rule.channels) {
      try {
        await this.sendAlert(channel, rule, metrics);
      } catch (error) {
        console.error(`Failed to send alert to ${channel.type}:`, error);
      }
    }
  }

  private static async sendAlert(channel: AlertChannel, rule: AlertRule, metrics: any) {
    switch (channel.type) {
      case 'slack':
        await this.sendSlackAlert(channel.config, rule, metrics);
        break;
      
      case 'email':
        await this.sendEmailAlert(channel.config, rule, metrics);
        break;
      
      case 'pagerduty':
        await this.sendPagerDutyAlert(channel.config, rule, metrics);
        break;
      
      case 'webhook':
        await this.sendWebhookAlert(channel.config, rule, metrics);
        break;
    }
  }

  private static async sendSlackAlert(config: any, rule: AlertRule, metrics: any) {
    const webhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!webhookUrl) return;

    const color = rule.severity === 'critical' ? 'danger' : 'warning';
    const emoji = rule.severity === 'critical' ? '🚨' : '⚠️';

    const payload = {
      channel: config.channel,
      username: 'Alert Bot',
      icon_emoji: ':rotating_light:',
      attachments: [{
        color,
        title: `${emoji} ${rule.name}`,
        fields: [
          {
            title: 'Severity',
            value: rule.severity.toUpperCase(),
            short: true
          },
          {
            title: 'Environment',
            value: process.env.NODE_ENV,
            short: true
          },
          {
            title: 'Threshold',
            value: rule.threshold.toString(),
            short: true
          },
          {
            title: 'Current Value',
            value: this.getCurrentValue(rule.id, metrics),
            short: true
          }
        ],
        footer: 'getintheq.space Monitoring',
        ts: Math.floor(Date.now() / 1000)
      }]
    };

    await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
  }

  private static async sendEmailAlert(config: any, rule: AlertRule, metrics: any) {
    // Implementation for email alerts
    console.log('Email alert would be sent to:', config.recipients);
  }

  private static async sendPagerDutyAlert(config: any, rule: AlertRule, metrics: any) {
    // Implementation for PagerDuty alerts
    console.log('PagerDuty alert would be sent for service:', config.service);
  }

  private static async sendWebhookAlert(config: any, rule: AlertRule, metrics: any) {
    await fetch(config.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        rule: rule.name,
        severity: rule.severity,
        metrics,
        timestamp: new Date().toISOString()
      })
    });
  }

  private static getCurrentValue(ruleId: string, metrics: any): string {
    switch (ruleId) {
      case 'high_error_rate':
        return `${metrics.errorRate}%`;
      case 'slow_response_time':
        return `${metrics.avgResponseTime}ms`;
      case 'high_memory_usage':
        return `${metrics.memoryUsagePercent}%`;
      case 'database_connection_pool_exhausted':
        return `${metrics.dbConnectionsUsedPercent}%`;
      case 'cert_expiry_warning':
        return `${metrics.certDaysUntilExpiry} days`;
      default:
        return 'Unknown';
    }
  }
}
```

## Health Checks

### Health Check API

```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { DatabaseMonitor } from '@/lib/monitoring/database';
import { ServerMonitor } from '@/lib/monitoring/server';

interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  environment: string;
  checks: {
    database: HealthCheckResult;
    server: HealthCheckResult;
    external: HealthCheckResult;
  };
  uptime: number;
}

interface HealthCheckResult {
  status: 'pass' | 'fail' | 'warn';
  duration: number;
  error?: string;
  details?: any;
}

const startTime = Date.now();

export async function GET() {
  const healthCheck: HealthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    checks: {
      database: await checkDatabase(),
      server: await checkServer(),
      external: await checkExternalServices(),
    },
    uptime: Date.now() - startTime
  };

  // Determine overall status
  const checkStatuses = Object.values(healthCheck.checks).map(check => check.status);
  
  if (checkStatuses.includes('fail')) {
    healthCheck.status = 'unhealthy';
  } else if (checkStatuses.includes('warn')) {
    healthCheck.status = 'degraded';
  }

  const statusCode = healthCheck.status === 'healthy' ? 200 : 
                    healthCheck.status === 'degraded' ? 200 : 503;

  return NextResponse.json(healthCheck, { status: statusCode });
}

async function checkDatabase(): Promise<HealthCheckResult> {
  const start = Date.now();
  
  try {
    const isHealthy = await DatabaseMonitor.checkHealth();
    const metrics = await DatabaseMonitor.getMetrics();
    
    return {
      status: isHealthy ? 'pass' : 'fail',
      duration: Date.now() - start,
      details: {
        connections: metrics.connections,
        health: metrics.health
      }
    };
  } catch (error) {
    return {
      status: 'fail',
      duration: Date.now() - start,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function checkServer(): Promise<HealthCheckResult> {
  const start = Date.now();
  
  try {
    const serverHealth = ServerMonitor.getHealthStatus();
    const memoryUsage = process.memoryUsage();
    
    const status = serverHealth.status === 'healthy' ? 'pass' : 
                  serverHealth.status === 'unhealthy' ? 'fail' : 'warn';
    
    return {
      status,
      duration: Date.now() - start,
      details: {
        ...serverHealth.metrics,
        memory: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024)
        }
      }
    };
  } catch (error) {
    return {
      status: 'fail',
      duration: Date.now() - start,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function checkExternalServices(): Promise<HealthCheckResult> {
  const start = Date.now();
  
  try {
    // Check external dependencies
    const checks = await Promise.allSettled([
      // Example: Check if external API is responsive
      fetch('https://api.external-service.com/health', { 
        method: 'GET',
        timeout: 5000 
      }).then(res => ({ service: 'external-api', status: res.ok })),
      
      // Add other external service checks here
    ]);

    const results = checks.map((check, index) => {
      if (check.status === 'fulfilled') {
        return check.value;
      } else {
        return { service: `service-${index}`, status: false, error: check.reason };
      }
    });

    const hasFailures = results.some(result => !result.status);

    return {
      status: hasFailures ? 'warn' : 'pass',
      duration: Date.now() - start,
      details: { external_services: results }
    };
  } catch (error) {
    return {
      status: 'fail',
      duration: Date.now() - start,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
```

### Synthetic Monitoring

```typescript
// scripts/monitoring/synthetic-tests.ts
import { chromium, Browser, Page } from 'playwright';

interface SyntheticTest {
  name: string;
  url: string;
  assertions: SyntheticAssertion[];
  frequency: number; // minutes
  timeout: number; // milliseconds
}

interface SyntheticAssertion {
  type: 'response_time' | 'status_code' | 'content' | 'element_exists';
  target: string | number;
  operator: 'lt' | 'gt' | 'eq' | 'contains';
  value: any;
}

const SYNTHETIC_TESTS: SyntheticTest[] = [
  {
    name: 'Homepage Load Test',
    url: 'https://getintheq.space',
    frequency: 5,
    timeout: 30000,
    assertions: [
      { type: 'status_code', target: 200, operator: 'eq', value: 200 },
      { type: 'response_time', target: 'load', operator: 'lt', value: 3000 },
      { type: 'content', target: 'title', operator: 'contains', value: 'getintheq' },
      { type: 'element_exists', target: 'nav', operator: 'eq', value: true }
    ]
  },
  {
    name: 'Blog Page Test',
    url: 'https://getintheq.space/blog',
    frequency: 15,
    timeout: 30000,
    assertions: [
      { type: 'status_code', target: 200, operator: 'eq', value: 200 },
      { type: 'response_time', target: 'load', operator: 'lt', value: 5000 },
      { type: 'element_exists', target: '[data-testid="blog-posts"]', operator: 'eq', value: true }
    ]
  },
  {
    name: 'API Health Check',
    url: 'https://getintheq.space/api/health',
    frequency: 2,
    timeout: 10000,
    assertions: [
      { type: 'status_code', target: 200, operator: 'eq', value: 200 },
      { type: 'response_time', target: 'response', operator: 'lt', value: 1000 },
      { type: 'content', target: 'status', operator: 'contains', value: 'healthy' }
    ]
  }
];

export class SyntheticMonitor {
  private browser: Browser | null = null;

  async init() {
    this.browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });
  }

  async runTest(test: SyntheticTest): Promise<SyntheticResult> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();
    const start = Date.now();

    try {
      // Set viewport and user agent
      await page.setViewportSize({ width: 1280, height: 720 });
      await page.setExtraHTTPHeaders({
        'User-Agent': 'SyntheticMonitor/1.0 (getintheq.space)'
      });

      // Navigate to page
      const response = await page.goto(test.url, {
        waitUntil: 'networkidle',
        timeout: test.timeout
      });

      const loadTime = Date.now() - start;

      // Run assertions
      const assertionResults = await this.runAssertions(page, response, test.assertions, loadTime);
      
      const passed = assertionResults.every(result => result.passed);

      return {
        testName: test.name,
        url: test.url,
        timestamp: new Date().toISOString(),
        passed,
        duration: Date.now() - start,
        assertions: assertionResults,
        metrics: {
          loadTime,
          statusCode: response?.status() || 0,
          responseSize: await this.getResponseSize(response)
        }
      };

    } catch (error) {
      return {
        testName: test.name,
        url: test.url,
        timestamp: new Date().toISOString(),
        passed: false,
        duration: Date.now() - start,
        error: error instanceof Error ? error.message : 'Unknown error',
        assertions: [],
        metrics: { loadTime: 0, statusCode: 0, responseSize: 0 }
      };
    } finally {
      await page.close();
    }
  }

  private async runAssertions(
    page: Page, 
    response: any, 
    assertions: SyntheticAssertion[], 
    loadTime: number
  ): Promise<AssertionResult[]> {
    const results: AssertionResult[] = [];

    for (const assertion of assertions) {
      try {
        const result = await this.evaluateAssertion(page, response, assertion, loadTime);
        results.push(result);
      } catch (error) {
        results.push({
          type: assertion.type,
          passed: false,
          expected: assertion.value,
          actual: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  private async evaluateAssertion(
    page: Page, 
    response: any, 
    assertion: SyntheticAssertion, 
    loadTime: number
  ): Promise<AssertionResult> {
    let actual: any;
    let passed: boolean;

    switch (assertion.type) {
      case 'status_code':
        actual = response?.status() || 0;
        passed = this.compareValues(actual, assertion.operator, assertion.value);
        break;

      case 'response_time':
        actual = assertion.target === 'load' ? loadTime : loadTime;
        passed = this.compareValues(actual, assertion.operator, assertion.value);
        break;

      case 'content':
        if (assertion.target === 'title') {
          actual = await page.title();
        } else {
          actual = await page.textContent('body');
        }
        passed = this.compareValues(actual, assertion.operator, assertion.value);
        break;

      case 'element_exists':
        const element = await page.$(assertion.target as string);
        actual = element !== null;
        passed = this.compareValues(actual, assertion.operator, assertion.value);
        break;

      default:
        throw new Error(`Unknown assertion type: ${assertion.type}`);
    }

    return {
      type: assertion.type,
      passed,
      expected: assertion.value,
      actual
    };
  }

  private compareValues(actual: any, operator: string, expected: any): boolean {
    switch (operator) {
      case 'eq':
        return actual === expected;
      case 'lt':
        return actual < expected;
      case 'gt':
        return actual > expected;
      case 'contains':
        return String(actual).toLowerCase().includes(String(expected).toLowerCase());
      default:
        return false;
    }
  }

  private async getResponseSize(response: any): Promise<number> {
    try {
      const headers = response?.headers() || {};
      const contentLength = headers['content-length'];
      return contentLength ? parseInt(contentLength, 10) : 0;
    } catch {
      return 0;
    }
  }

  async runAllTests(): Promise<SyntheticResult[]> {
    const results: SyntheticResult[] = [];

    for (const test of SYNTHETIC_TESTS) {
      try {
        const result = await this.runTest(test);
        results.push(result);

        // Report result
        if (!result.passed) {
          console.error(`❌ Synthetic test failed: ${test.name}`);
          // Send alert
          await this.sendAlert(result);
        } else {
          console.log(`✅ Synthetic test passed: ${test.name}`);
        }

      } catch (error) {
        console.error(`Failed to run synthetic test ${test.name}:`, error);
      }
    }

    return results;
  }

  private async sendAlert(result: SyntheticResult) {
    // Send alert for failed synthetic test
    const alertPayload = {
      type: 'synthetic_test_failure',
      testName: result.testName,
      url: result.url,
      error: result.error,
      timestamp: result.timestamp
    };

    try {
      await fetch('/api/monitoring/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(alertPayload)
      });
    } catch (error) {
      console.error('Failed to send synthetic test alert:', error);
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

interface SyntheticResult {
  testName: string;
  url: string;
  timestamp: string;
  passed: boolean;
  duration: number;
  error?: string;
  assertions: AssertionResult[];
  metrics: {
    loadTime: number;
    statusCode: number;
    responseSize: number;
  };
}

interface AssertionResult {
  type: string;
  passed: boolean;
  expected: any;
  actual: any;
  error?: string;
}

// CLI script for running synthetic tests
if (require.main === module) {
  const monitor = new SyntheticMonitor();
  
  monitor.init()
    .then(() => monitor.runAllTests())
    .then((results) => {
      console.log(`\n📊 Synthetic test results:`);
      console.log(`Total tests: ${results.length}`);
      console.log(`Passed: ${results.filter(r => r.passed).length}`);
      console.log(`Failed: ${results.filter(r => !r.passed).length}`);
      
      const failedTests = results.filter(r => !r.passed);
      if (failedTests.length > 0) {
        console.log('\n❌ Failed tests:');
        failedTests.forEach(test => {
          console.log(`  - ${test.testName}: ${test.error || 'Assertion failures'}`);
        });
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Synthetic monitoring failed:', error);
      process.exit(1);
    })
    .finally(() => {
      monitor.close();
    });
}
```

## Setup Instructions

### Environment Variables

```bash
# Monitoring Configuration
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
NEXT_PUBLIC_DATADOG_APPLICATION_ID=your_datadog_app_id
NEXT_PUBLIC_DATADOG_CLIENT_TOKEN=your_datadog_client_token
DATADOG_API_KEY=your_datadog_api_key

# Alerting
SLACK_WEBHOOK_URL=your_slack_webhook_url
PAGERDUTY_INTEGRATION_KEY=your_pagerduty_key

# External Monitoring
PINGDOM_API_TOKEN=your_pingdom_token
LIGHTHOUSE_CI_TOKEN=your_lighthouse_ci_token
```

### Installation Script

```bash
#!/bin/bash
# scripts/monitoring/setup.sh

echo "🔧 Setting up monitoring and alerting..."

# Install monitoring dependencies
npm install --save @sentry/nextjs @datadog/browser-rum @datadog/browser-logs web-vitals

# Install development dependencies for synthetic monitoring
npm install --save-dev playwright @types/node

# Install Playwright browsers
npx playwright install

# Create monitoring directories
mkdir -p logs monitoring/dashboards monitoring/alerts

# Set up log rotation
sudo tee /etc/logrotate.d/getintheq > /dev/null <<EOF
/var/log/getintheq/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    postrotate
        systemctl reload nginx > /dev/null 2>&1 || true
    endscript
}
EOF

# Create monitoring cron jobs
(crontab -l 2>/dev/null; echo "*/5 * * * * cd $(pwd) && npm run monitor:synthetic") | crontab -
(crontab -l 2>/dev/null; echo "0 */6 * * * cd $(pwd) && npm run monitor:health-check") | crontab -

echo "✅ Monitoring setup complete!"
echo ""
echo "Next steps:"
echo "1. Configure your monitoring service credentials in .env.local"
echo "2. Set up Slack webhook for alerts"
echo "3. Configure PagerDuty integration"
echo "4. Test synthetic monitoring: npm run monitor:synthetic"
echo "5. Access monitoring dashboard: npm run monitor:dashboard"
```

### Package.json Scripts

```json
{
  "scripts": {
    "monitor:synthetic": "tsx scripts/monitoring/synthetic-tests.ts",
    "monitor:health-check": "curl -f http://localhost:3000/api/health || exit 1",
    "monitor:dashboard": "open http://localhost:3000/admin/monitoring",
    "monitor:alerts-test": "tsx scripts/monitoring/test-alerts.ts",
    "monitor:metrics": "tsx scripts/monitoring/collect-metrics.ts",
    "monitor:setup": "bash scripts/monitoring/setup.sh"
  }
}
```

---

**Monitoring & Alerting Guide Version**: 1.0.0  
**Last Updated**: 2024-08-14  
**Monitoring Stack**: Sentry, DataDog, Vercel Analytics, Custom Metrics  
**Next Review**: 2024-11-14  
**Monitoring Contact**: <EMAIL>