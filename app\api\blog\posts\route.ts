import { NextResponse } from 'next/server';

const enhancedBlogPosts = [
  {
    id: '1',
    title: 'Building Scalable Machine Learning Pipelines with MLOps',
    excerpt: 'Learn how to design and implement production-ready ML pipelines using modern MLOps practices and tools like Kubeflow, MLflow, and Docker.',
    content: 'Full article content here...',
    category: 'Machine Learning',
    tags: ['MLOps', 'Kubernetes', 'Python', 'Docker', 'CI/CD'],
    readTime: 12,
    publishedAt: '2024-01-20',
    slug: 'scalable-ml-pipelines-mlops',
    featured: true,
    imageUrl: 'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: '<PERSON><PERSON><PERSON>',
      avatar: 'https://github.com/khiwniti.png'
    }
  },
  {
    id: '2',
    title: 'Real-time Data Processing with Apache Kafka and Python',
    excerpt: 'Explore how to build real-time data pipelines using Apache Kafka, Python, and stream processing techniques for handling millions of events.',
    content: 'Full article content here...',
    category: 'Data Science',
    tags: ['Kafka', 'Stream Processing', 'Python', 'Real-time', 'Big Data'],
    readTime: 15,
    publishedAt: '2024-01-18',
    slug: 'realtime-data-processing-kafka-python',
    featured: false,
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: 'Khiw Nitithachot',
      avatar: 'https://github.com/khiwniti.png'
    }
  },
  {
    id: '3',
    title: 'Ethical AI: Addressing Bias in Machine Learning Models',
    excerpt: 'Understanding and mitigating bias in AI systems to build more fair and responsible machine learning applications.',
    content: 'Full article content here...',
    category: 'AI Ethics',
    tags: ['AI Ethics', 'Bias', 'Fairness', 'Responsible AI', 'Machine Learning'],
    readTime: 10,
    publishedAt: '2024-01-15',
    slug: 'ethical-ai-addressing-bias-ml-models',
    featured: true,
    imageUrl: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: 'Khiw Nitithachot',
      avatar: 'https://github.com/khiwniti.png'
    }
  },
  {
    id: '4',
    title: 'Building Modern Web Applications with Next.js 14 and TypeScript',
    excerpt: 'A comprehensive guide to building performant, type-safe web applications using the latest features of Next.js 14 and TypeScript.',
    content: 'Full article content here...',
    category: 'Web Development',
    tags: ['Next.js', 'TypeScript', 'React', 'Web Development', 'Frontend'],
    readTime: 8,
    publishedAt: '2024-01-12',
    slug: 'modern-web-apps-nextjs-typescript',
    featured: false,
    imageUrl: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: 'Khiw Nitithachot',
      avatar: 'https://github.com/khiwniti.png'
    }
  },
  {
    id: '5',
    title: 'Computer Vision with PyTorch: Object Detection Tutorial',
    excerpt: 'Step-by-step tutorial on implementing state-of-the-art object detection models using PyTorch and modern architectures like YOLO and R-CNN.',
    content: 'Full article content here...',
    category: 'Machine Learning',
    tags: ['Computer Vision', 'PyTorch', 'Object Detection', 'Deep Learning', 'Tutorial'],
    readTime: 20,
    publishedAt: '2024-01-10',
    slug: 'computer-vision-pytorch-object-detection',
    featured: false,
    imageUrl: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: 'Khiw Nitithachot',
      avatar: 'https://github.com/khiwniti.png'
    }
  },
  {
    id: '6',
    title: 'Data Visualization Best Practices with D3.js and React',
    excerpt: 'Learn how to create compelling and interactive data visualizations using D3.js with React for modern web applications.',
    content: 'Full article content here...',
    category: 'Data Science',
    tags: ['Data Visualization', 'D3.js', 'React', 'JavaScript', 'Frontend'],
    readTime: 14,
    publishedAt: '2024-01-08',
    slug: 'data-visualization-d3js-react',
    featured: false,
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: 'Khiw Nitithachot',
      avatar: 'https://github.com/khiwniti.png'
    }
  },
  {
    id: '7',
    title: 'Getting Started with Large Language Models: A Practical Guide',
    excerpt: 'A comprehensive introduction to working with large language models, from fine-tuning to deployment in production environments.',
    content: 'Full article content here...',
    category: 'Machine Learning',
    tags: ['LLM', 'NLP', 'Transformers', 'Fine-tuning', 'AI'],
    readTime: 18,
    publishedAt: '2024-01-05',
    slug: 'getting-started-large-language-models',
    featured: true,
    imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: 'Khiw Nitithachot',
      avatar: 'https://github.com/khiwniti.png'
    }
  },
  {
    id: '8',
    title: 'Microservices Architecture with Docker and Kubernetes',
    excerpt: 'Design and deploy scalable microservices using containerization technologies and orchestration platforms.',
    content: 'Full article content here...',
    category: 'Web Development',
    tags: ['Microservices', 'Docker', 'Kubernetes', 'DevOps', 'Architecture'],
    readTime: 16,
    publishedAt: '2024-01-03',
    slug: 'microservices-docker-kubernetes',
    featured: false,
    imageUrl: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    author: {
      name: 'Khiw Nitithachot',
      avatar: 'https://github.com/khiwniti.png'
    }
  }
];

export async function GET() {
  try {
    await new Promise(resolve => setTimeout(resolve, 300));
    return NextResponse.json(enhancedBlogPosts);
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch blog posts" }, { status: 500 });
  }
}