import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Resume | Khiw Nitithachot',
  description: 'Professional resume and CV of <PERSON>hi<PERSON>, Data & AI Solutions Engineer. Experience, education, skills, and certifications.',
  keywords: [
    'resume',
    'CV',
    'curriculum vitae',
    'experience',
    'skills',
    'data engineer',
    'AI engineer',
    'machine learning engineer'
  ],
  openGraph: {
    title: 'Resume | Khiw Nitithachot',
    description: 'Professional resume and CV of Khiw Nitithachot, Data & AI Solutions Engineer.',
    type: 'website',
    url: 'https://getintheq.space/resume',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Resume | Khiw Nitithachot',
    description: 'Professional resume and CV of Khiw Nitithachot, Data & AI Solutions Engineer.',
  },
}

export default function ResumeLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}