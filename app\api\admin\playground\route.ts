import { NextRequest, NextResponse } from 'next/server';
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { playgroundTools, insertPlaygroundToolSchema } from '@shared/schema';
import { eq, desc } from 'drizzle-orm';

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const limit = searchParams.get('limit');

    let query = db.select().from(playgroundTools);
    
    if (status && status !== 'all') {
      query = query.where(eq(playgroundTools.status, status as any));
    }
    
    query = query.orderBy(desc(playgroundTools.updatedAt));

    if (limit) {
      query = query.limit(parseInt(limit));
    }

    const tools = await query;

    return NextResponse.json(tools);
  } catch (error) {
    console.error('Error fetching playground tools:', error);
    return NextResponse.json(
      { error: 'Failed to fetch playground tools' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input using Zod schema
    const validatedData = insertPlaygroundToolSchema.parse(body);
    
    // Insert playground tool
    const newTool = await db.insert(playgroundTools).values(validatedData).returning();
    
    return NextResponse.json(newTool[0]);
  } catch (error) {
    console.error('Error creating playground tool:', error);
    return NextResponse.json(
      { error: 'Failed to create playground tool' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;
    
    // Validate input using Zod schema (partial for updates)
    const validatedData = insertPlaygroundToolSchema.partial().parse(updateData);
    
    const updatedTool = await db
      .update(playgroundTools)
      .set({ ...validatedData, updatedAt: new Date() })
      .where(eq(playgroundTools.id, id))
      .returning();

    if (updatedTool.length === 0) {
      return NextResponse.json(
        { error: 'Playground tool not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedTool[0]);
  } catch (error) {
    console.error('Error updating playground tool:', error);
    return NextResponse.json(
      { error: 'Failed to update playground tool' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Tool ID is required' },
        { status: 400 }
      );
    }
    
    const deletedTool = await db
      .delete(playgroundTools)
      .where(eq(playgroundTools.id, id))
      .returning({ id: playgroundTools.id });

    if (deletedTool.length === 0) {
      return NextResponse.json(
        { error: 'Playground tool not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting playground tool:', error);
    return NextResponse.json(
      { error: 'Failed to delete playground tool' },
      { status: 500 }
    );
  }
}
