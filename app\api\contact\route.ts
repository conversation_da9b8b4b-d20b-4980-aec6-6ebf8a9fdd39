import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { DatabaseService } from '@/lib/database-service';
import { insertContactSchema } from '@shared/schema';
import { RequestTimer, PerformanceMonitor } from '@/lib/performance';
import { InputSanitizer, SecurityHeaders, IPUtils } from '@/lib/security';

const contactRequestSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  email: z.string().email('Invalid email address').max(100, 'Email too long'),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject too long'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(5000, 'Message too long'),
});

export async function POST(request: NextRequest): Promise<NextResponse> {
  const timer = new RequestTimer();
  const clientIP = IPUtils.getRealIP(request.headers);
  const userAgent = request.headers.get('user-agent') || '';

  try {
    // Parse and validate JSON body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.warn(`Invalid JSON from IP: ${clientIP}`);
      return NextResponse.json(
        { error: 'Invalid JSON format' },
        {
          status: 400,
          headers: SecurityHeaders.getApiHeaders()
        }
      );
    }

    // Validate input using Zod schema
    const validationResult = contactRequestSchema.safeParse(body);
    if (!validationResult.success) {
      console.warn(`Validation failed from IP: ${clientIP}:`, validationResult.error.issues);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.issues.map(issue => ({
            field: issue.path.join('.'),
            message: issue.message
          }))
        },
        {
          status: 400,
          headers: SecurityHeaders.getApiHeaders()
        }
      );
    }

    const { firstName, lastName, email, subject, message } = validationResult.data;

    // Sanitize inputs to prevent XSS
    const sanitizedData = {
      firstName: InputSanitizer.sanitizeHtml(firstName.trim()),
      lastName: InputSanitizer.sanitizeHtml(lastName.trim()),
      email: InputSanitizer.sanitizeEmail(email),
      subject: InputSanitizer.sanitizeHtml(subject.trim()),
      message: InputSanitizer.sanitizeHtml(message.trim()),
    };

    // Create contact submission using proper schema
    const contactData = insertContactSchema.parse({
      ...sanitizedData,
      ipAddress: clientIP,
      userAgent: userAgent.substring(0, 500), // Limit user agent length
    });

    // Track database performance
    const submission = await DatabaseService.createContactSubmission(contactData);

    // Send notification email (if configured)
    try {
      await sendNotificationEmail(submission);
    } catch (emailError) {
      console.error('Failed to send notification email:', emailError);
      // Don't fail the request if email fails
    }

    // Record performance metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: timer.end(),
      url: '/api/contact',
      userAgent,
    });

    // Log successful submission
    console.log(`Contact form submitted successfully by ${email} from IP: ${clientIP}`);

    return NextResponse.json(
      {
        success: true,
        message: 'Thank you for your message! I\'ll get back to you soon.',
        id: submission.id
      },
      {
        headers: SecurityHeaders.getApiHeaders()
      }
    );
  } catch (error) {
    const duration = timer.end();
    
    // Record error metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: duration,
      url: '/api/contact',
      userAgent,
    });

    console.error(`Contact form error from IP: ${clientIP}:`, error);
    
    // Don't leak internal error details
    const errorMessage = process.env.NODE_ENV === 'development'
      ? `Internal error: ${(error as Error).message}`
      : 'Failed to submit message. Please try again.';

    return NextResponse.json(
      { error: errorMessage },
      {
        status: 500,
        headers: SecurityHeaders.getApiHeaders()
      }
    );
  }
}

interface ContactSubmission {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  subject: string;
  message: string;
  createdAt: Date;
}

async function sendNotificationEmail(submission: ContactSubmission): Promise<void> {
  // This would integrate with your email service (SendGrid, Nodemailer, etc.)
  // For now, we'll just log it
  console.log('New contact submission:', {
    id: submission.id,
    name: `${submission.firstName} ${submission.lastName}`,
    email: submission.email,
    subject: submission.subject,
    message: submission.message.substring(0, 100) + '...'
  });
  
  // Example implementation with SendGrid:
  /*
  const sgMail = require('@sendgrid/mail');
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  
  const msg = {
    to: process.env.ADMIN_EMAIL,
    from: process.env.FROM_EMAIL,
    subject: `New Contact Form Submission: ${submission.subject}`,
    html: `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${submission.name}</p>
      <p><strong>Email:</strong> ${submission.email}</p>
      <p><strong>Subject:</strong> ${submission.subject}</p>
      <p><strong>Message:</strong></p>
      <p>${submission.message.replace(/\n/g, '<br>')}</p>
      <hr>
      <p><small>Submitted at: ${new Date(submission.created_at).toLocaleString()}</small></p>
    `
  };
  
  await sgMail.send(msg);
  */
}
