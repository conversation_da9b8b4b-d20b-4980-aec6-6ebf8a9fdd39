#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# Run lint-staged to check staged files
npx lint-staged

# Check for console.log statements (warn but don't block)
if git diff --cached --name-only | grep -E '\.(js|jsx|ts|tsx)$' | xargs grep -l 'console\.log' 2>/dev/null; then
  echo "⚠️  Warning: Found console.log statements in staged files"
  echo "   Consider removing them before committing to production"
fi

# Check for TODO/FIXME comments (warn but don't block)
if git diff --cached --name-only | grep -E '\.(js|jsx|ts|tsx)$' | xargs grep -l -E '(TODO|FIXME|XXX)' 2>/dev/null; then
  echo "📝 Info: Found TODO/FIXME comments in staged files"
fi

echo "✅ Pre-commit checks completed successfully!"