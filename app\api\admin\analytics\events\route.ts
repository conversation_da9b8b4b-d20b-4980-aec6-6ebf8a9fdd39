import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { AuthService } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { event_name, properties } = body;

    if (!event_name) {
      return NextResponse.json(
        { error: 'Event name is required' },
        { status: 400 }
      );
    }

    // Get client IP
    const ip_address = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown';

    // Track event
    const query = `
      INSERT INTO analytics_events (
        event_name, properties, ip_address, user_agent, session_id
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `;

    const values = [
      event_name,
      JSON.stringify(properties || {}),
      ip_address,
      request.headers.get('user-agent'),
      properties?.session_id
    ];

    const result = await DatabaseService.query(query, values);

    return NextResponse.json({ success: true, id: result.rows[0].id });
  } catch (error) {
    console.error('Error tracking event:', error);
    return NextResponse.json(
      { error: 'Failed to track event' },
      { status: 500 }
    );
  }
}

export const GET = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const event_name = searchParams.get('event');

    let whereClause = `WHERE created_at >= NOW() - INTERVAL '${days} days'`;
    const params: any[] = [];

    if (event_name) {
      whereClause += ` AND event_name = $1`;
      params.push(event_name);
    }

    // Get event summary
    const summaryQuery = `
      SELECT 
        event_name,
        COUNT(*) as count,
        COUNT(DISTINCT session_id) as unique_sessions
      FROM analytics_events 
      ${whereClause}
      GROUP BY event_name
      ORDER BY count DESC
    `;

    const summaryResult = await DatabaseService.query(summaryQuery, params);

    // Get daily breakdown
    const dailyQuery = `
      SELECT 
        DATE(created_at) as date,
        event_name,
        COUNT(*) as count
      FROM analytics_events 
      ${whereClause}
      GROUP BY DATE(created_at), event_name
      ORDER BY date DESC, count DESC
    `;

    const dailyResult = await DatabaseService.query(dailyQuery, params);

    // Get top properties for specific event
    let topProperties = [];
    if (event_name) {
      const propertiesQuery = `
        SELECT 
          properties,
          COUNT(*) as count
        FROM analytics_events 
        WHERE event_name = $1 
          AND created_at >= NOW() - INTERVAL '${days} days'
        GROUP BY properties
        ORDER BY count DESC
        LIMIT 10
      `;

      const propertiesResult = await DatabaseService.query(propertiesQuery, [event_name]);
      topProperties = propertiesResult.rows;
    }

    return NextResponse.json({
      summary: summaryResult.rows,
      daily_breakdown: dailyResult.rows,
      top_properties: topProperties
    });
  } catch (error) {
    console.error('Error fetching events analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events analytics' },
      { status: 500 }
    );
  }
});
