import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'AI/ML Projects | Khiw Nitithachot',
  description: 'Interactive showcase of AI and machine learning projects by <PERSON><PERSON><PERSON>. Computer vision, NLP, data visualization, and predictive analytics demos.',
  keywords: [
    'AI projects',
    'machine learning',
    'computer vision',
    'NLP',
    'natural language processing',
    'data visualization',
    'interactive demos',
    'artificial intelligence'
  ],
  openGraph: {
    title: 'AI/ML Projects | Khiw Nitithachot',
    description: 'Interactive showcase of AI and machine learning projects. Computer vision, NLP, data visualization, and predictive analytics demos.',
    type: 'website',
    url: 'https://getintheq.space/projects',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI/ML Projects | Khiw Nitithachot',
    description: 'Interactive showcase of AI and machine learning projects. Computer vision, NLP, data visualization, and predictive analytics demos.',
  },
}

export default function ProjectsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}