"""
AI Playground endpoints for the serverless backend
Handles text generation, sentiment analysis, chatbot, and image analysis
"""

from fastapi import APIRouter, HTTPException, File, UploadFile, BackgroundTasks
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
import httpx
import asyncio
import json
import base64
import io
from PIL import Image
import logging
import re
from datetime import datetime

logger = logging.getLogger(__name__)

# Create router for playground endpoints
playground_router = APIRouter(prefix="/api/playground", tags=["AI Playground"])

# Models for requests and responses
class ChatMessage(BaseModel):
    content: str = Field(..., min_length=1, max_length=1000)
    personality: str = Field(default="helpful", regex="^(helpful|creative|technical|friendly|philosopher)$")

class ChatResponse(BaseModel):
    message: str
    timestamp: datetime
    personality: str
    response_time: float

class TextGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=500)
    text_type: str = Field(default="story", regex="^(story|blog|email|poem|code|marketing)$")
    model: str = Field(default="gpt4", regex="^(gpt4|claude|gemini)$")
    word_count: int = Field(default=150, ge=50, le=500)

class TextGenerationResponse(BaseModel):
    generated_text: str
    word_count: int
    character_count: int
    model_used: str
    generation_time: float

class SentimentRequest(BaseModel):
    text: str = Field(..., min_length=10, max_length=2000)

class EmotionScore(BaseModel):
    emotion: str
    intensity: int
    color: str

class SentimentKeyword(BaseModel):
    word: str
    sentiment: str
    weight: float

class SentimentResponse(BaseModel):
    overall: Dict[str, Union[str, float]]
    emotions: List[EmotionScore]
    keywords: List[SentimentKeyword]
    insights: List[str]
    analysis_time: float

class CodeAssistRequest(BaseModel):
    code: str = Field(..., min_length=1, max_length=5000)
    language: str = Field(default="python")
    task: str = Field(default="explain", regex="^(explain|optimize|debug|generate|review)$")

class CodeAssistResponse(BaseModel):
    result: str
    suggestions: List[str]
    language: str
    task_type: str
    analysis_time: float

class ImageAnalysisResponse(BaseModel):
    description: str
    objects: List[Dict[str, Any]]
    colors: List[str]
    tags: List[str]
    confidence: float
    analysis_time: float

# AI Personality responses for chatbot
PERSONALITY_RESPONSES = {
    "helpful": {
        "greetings": [
            "I'm here to help! What can I assist you with today?",
            "Hello! How can I be of service?",
            "Hi there! What would you like to know or discuss?"
        ],
        "questions": [
            "That's a great question! Let me help you with that.",
            "I'd be happy to explain that for you.",
            "Excellent question! Here's what I can tell you about that."
        ],
        "default": [
            "I understand what you're asking. Here's what I think...",
            "That's interesting! Let me provide some insights.",
            "Based on your question, here's my response..."
        ]
    },
    "creative": {
        "greetings": [
            "Welcome to our creative space! What shall we imagine together?",
            "Hello, fellow dreamer! Ready to explore some ideas?",
            "Greetings, creative soul! What inspires you today?"
        ],
        "questions": [
            "Ooh, that sparks my imagination! Let me paint you a picture with words.",
            "What a fascinating topic to explore creatively!",
            "That opens up a world of creative possibilities!"
        ],
        "default": [
            "That reminds me of a beautiful story...",
            "Let me weave some creative magic around that idea!",
            "What an inspiring thought! Here's my creative take..."
        ]
    },
    "technical": {
        "greetings": [
            "System initialized. How may I assist with your technical queries?",
            "Technical support ready. What's your question?",
            "Hello! Ready to dive into some technical details?"
        ],
        "questions": [
            "Let me break down the technical aspects for you.",
            "From a technical perspective, here's what you need to know.",
            "Analyzing your technical query... Here's the breakdown."
        ],
        "default": [
            "Processing your request... Here's the technical analysis.",
            "Let me provide the technical details.",
            "From a systems perspective, here's what's happening..."
        ]
    },
    "friendly": {
        "greetings": [
            "Hey there! So nice to chat with you! 😊",
            "Hi friend! What's on your mind today?",
            "Hello! I'm excited to talk with you!"
        ],
        "questions": [
            "Oh, I love talking about this! Let me share my thoughts.",
            "That's such a cool question! I'm excited to discuss it.",
            "What a fun topic! Here's what I think about that."
        ],
        "default": [
            "You know what? That's really interesting!",
            "I'm so glad you brought that up! Here's what I think...",
            "That's awesome! Let me tell you what comes to mind."
        ]
    },
    "philosopher": {
        "greetings": [
            "Greetings, fellow seeker of wisdom. What profound questions shall we explore?",
            "Welcome to our philosophical discourse. What mysteries shall we unravel?",
            "Hello, thoughtful soul. What deeper truths are you pondering?"
        ],
        "questions": [
            "Ah, a question that touches the very essence of existence...",
            "This inquiry leads us to deeper philosophical waters...",
            "What a profound question that deserves contemplation..."
        ],
        "default": [
            "This reminds me of ancient wisdom...",
            "Let us contemplate the deeper meaning behind this...",
            "In the grand tapestry of existence, this represents..."
        ]
    }
}

# Text generation templates
TEXT_TEMPLATES = {
    "story": """Once upon a time, in a world where {prompt} held great significance, there lived a character whose life was about to change forever. The morning sun cast long shadows as they discovered something extraordinary about {prompt}. Through unexpected adventures and meaningful encounters, they learned valuable lessons about courage, perseverance, and the power of believing in oneself. The journey revealed hidden truths and opened new possibilities that would shape their future in ways they never imagined.""",
    
    "blog": """# Understanding {prompt}: A Comprehensive Guide

In today's rapidly evolving world, {prompt} has become increasingly important for both individuals and organizations. This comprehensive guide explores the key aspects, benefits, and practical applications you need to know.

## Why {prompt} Matters

The significance of {prompt} cannot be overstated. Recent developments have shown that those who understand and effectively utilize {prompt} gain significant advantages in their personal and professional lives.

## Key Benefits and Applications

- Enhanced productivity and efficiency
- Improved decision-making capabilities  
- Better resource utilization
- Increased competitive advantage
- Long-term sustainable growth

## Getting Started

Implementing {prompt} successfully requires a strategic approach. Start with understanding the fundamentals, then gradually build your expertise through practical application and continuous learning.

## Conclusion

By embracing {prompt} and implementing these strategies, you can unlock new opportunities and achieve remarkable results in your endeavors.""",
    
    "email": """Subject: Regarding {prompt} - Important Update

Dear [Recipient],

I hope this email finds you well. I am writing to discuss {prompt} and share some important updates that may be of interest to you.

Based on recent developments and our ongoing analysis, {prompt} presents significant opportunities for our organization. The potential benefits include improved efficiency, cost savings, and enhanced competitive positioning.

I would like to propose that we schedule a meeting to discuss this matter in greater detail. I believe that by working together on {prompt}, we can achieve substantial value and positive outcomes.

Please let me know your availability for a discussion at your earliest convenience. I look forward to hearing from you.

Best regards,
[Your Name]""",
    
    "poem": """In the realm where {prompt} dwells,
Beauty and wonder softly tell
Stories of magic, hope, and light,
Dancing through the endless night.

Each moment holds a precious gleam,
Like fragments of a vivid dream,
Where {prompt} flows like gentle streams,
Fulfilling all our deepest dreams.

Through seasons of both joy and change,
{prompt} weaves patterns rich and strange,
A tapestry of life's grand art,
That speaks directly to the heart.""",
    
    "code": """# {prompt} Implementation Guide

## Overview
This module provides comprehensive functionality for {prompt}. It includes robust error handling, efficient algorithms, and extensive customization options.

## Features
- High-performance implementation optimized for scale
- Comprehensive error handling and validation
- Extensive configuration options for flexibility
- Full TypeScript support with type safety
- Built-in logging and monitoring capabilities

## Installation
```bash
npm install {prompt}-module
```

## Usage Example
```typescript
import {{ {prompt}Handler }} from '{prompt}-module';

const handler = new {prompt}Handler({{
  config: {{
    enableLogging: true,
    maxRetries: 3,
    timeout: 5000
  }}
}});

// Basic usage
const result = await handler.process({{
  input: data,
  options: {{ optimize: true }}
}});

console.log('Result:', result);
```

## Configuration
The module supports various configuration options to customize behavior according to your specific needs.""",
    
    "marketing": """🚀 Discover the Revolutionary Power of {prompt}!

Transform Your Business with Cutting-Edge {prompt} Solutions

Are you ready to unlock unprecedented growth and success? Our innovative approach to {prompt} has already helped thousands of forward-thinking companies achieve remarkable results that exceed their wildest expectations.

✨ Why Choose Our {prompt} Solution?

🎯 PROVEN RESULTS: 10x faster implementation than traditional methods
💰 COST EFFECTIVE: Up to 50% reduction in operational costs
🔒 RELIABLE: Industry-leading 99.9% uptime guarantee
🌟 EXPERT SUPPORT: 24/7 dedicated customer success team
📈 SCALABLE: Grows seamlessly with your business needs

Don't let your competitors get ahead while you're still using outdated approaches. Join the revolution and see why industry leaders choose our {prompt} solutions.

🎁 LIMITED TIME OFFER: Get started today and receive a FREE consultation plus 30% off your first month!

[GET STARTED NOW] [LEARN MORE] [BOOK FREE DEMO]

*Join thousands of satisfied customers who have transformed their business with {prompt}*"""
}

@playground_router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatMessage):
    """
    Chat with AI assistant with different personalities
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Get personality responses
        personality_responses = PERSONALITY_RESPONSES.get(request.personality, PERSONALITY_RESPONSES["helpful"])
        
        # Simple keyword-based response selection
        message_lower = request.content.lower()
        
        if any(word in message_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            responses = personality_responses["greetings"]
        elif any(word in message_lower for word in ['?', 'what', 'how', 'why', 'when', 'where']):
            responses = personality_responses["questions"]
        else:
            responses = personality_responses["default"]
        
        # Select random response and add some context
        import random
        base_response = random.choice(responses)
        
        # Add some context based on the message
        if "help" in message_lower:
            context = " I'm designed to assist with a wide range of topics and questions."
        elif "learn" in message_lower:
            context = " Learning is a wonderful journey, and I'm here to support you along the way."
        elif "create" in message_lower or "make" in message_lower:
            context = " Creativity is one of my favorite topics to explore together."
        else:
            context = " Feel free to ask me anything you'd like to know more about."
            
        response_message = base_response + context
        
        end_time = asyncio.get_event_loop().time()
        
        return ChatResponse(
            message=response_message,
            timestamp=datetime.now(),
            personality=request.personality,
            response_time=round(end_time - start_time, 3)
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate chat response")

@playground_router.post("/generate-text", response_model=TextGenerationResponse)
async def generate_text(request: TextGenerationRequest):
    """
    Generate text content using AI models
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Get template for text type
        template = TEXT_TEMPLATES.get(request.text_type, TEXT_TEMPLATES["story"])
        
        # Generate text by filling template
        generated_text = template.format(prompt=request.prompt)
        
        # Adjust length based on word count request
        words = generated_text.split()
        if len(words) > request.word_count:
            words = words[:request.word_count]
            generated_text = ' '.join(words) + "..."
        elif len(words) < request.word_count:
            # Add some additional content if needed
            additional = f" Furthermore, {request.prompt} represents an important aspect of modern life that deserves careful consideration and thoughtful implementation."
            generated_text += additional
        
        end_time = asyncio.get_event_loop().time()
        
        return TextGenerationResponse(
            generated_text=generated_text,
            word_count=len(generated_text.split()),
            character_count=len(generated_text),
            model_used=request.model,
            generation_time=round(end_time - start_time, 3)
        )
        
    except Exception as e:
        logger.error(f"Text generation error: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate text")

@playground_router.post("/analyze-sentiment", response_model=SentimentResponse)
async def analyze_sentiment(request: SentimentRequest):
    """
    Analyze sentiment and emotions in text
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Define sentiment keywords
        positive_words = ['love', 'amazing', 'fantastic', 'excellent', 'great', 'wonderful', 'excited', 'happy', 'awesome', 'perfect', 'brilliant', 'outstanding']
        negative_words = ['hate', 'terrible', 'awful', 'worst', 'disappointed', 'broken', 'unhelpful', 'bad', 'horrible', 'disgusting', 'angry', 'frustrated']
        
        text_lower = request.text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        # Count sentiment words
        positive_count = sum(1 for word in words if any(pos in word for pos in positive_words))
        negative_count = sum(1 for word in words if any(neg in word for neg in negative_words))
        
        # Calculate sentiment score
        total_sentiment_words = positive_count + negative_count
        if total_sentiment_words > 0:
            score = (positive_count - negative_count) / len(words)
        else:
            score = 0
        
        # Determine overall sentiment
        if score > 0.1:
            sentiment = "positive"
            confidence = min(0.95, 0.6 + abs(score) * 2)
        elif score < -0.1:
            sentiment = "negative"
            confidence = min(0.95, 0.6 + abs(score) * 2)
        else:
            sentiment = "neutral"
            confidence = 0.7 + (1 - abs(score)) * 0.2
        
        # Generate emotion scores
        emotions = [
            EmotionScore(emotion="Joy", intensity=max(20, int(85 if sentiment == "positive" else 20)), color="bg-yellow-500"),
            EmotionScore(emotion="Sadness", intensity=max(15, int(75 if sentiment == "negative" else 15)), color="bg-blue-500"),
            EmotionScore(emotion="Anger", intensity=max(10, int(60 if sentiment == "negative" else 10)), color="bg-red-500"),
            EmotionScore(emotion="Love", intensity=max(25, int(70 if sentiment == "positive" else 25)), color="bg-pink-500"),
            EmotionScore(emotion="Neutral", intensity=max(30, int(80 if sentiment == "neutral" else 30)), color="bg-gray-500")
        ]
        
        # Extract keywords
        keywords = []
        for word in words:
            if any(pos in word for pos in positive_words):
                keywords.append(SentimentKeyword(word=word, sentiment="positive", weight=0.8))
            elif any(neg in word for neg in negative_words):
                keywords.append(SentimentKeyword(word=word, sentiment="negative", weight=0.8))
        
        # Generate insights
        insights = [
            f"The text expresses {sentiment} sentiment with {confidence:.0%} confidence",
            f"Found {len(keywords)} sentiment-bearing keywords in the analysis",
            f"Emotional tone is primarily {sentiment} based on linguistic patterns",
            "Analysis considers word choice, context, and emotional indicators"
        ]
        
        end_time = asyncio.get_event_loop().time()
        
        return SentimentResponse(
            overall={
                "sentiment": sentiment,
                "confidence": confidence,
                "score": round(score, 3)
            },
            emotions=emotions,
            keywords=keywords,
            insights=insights,
            analysis_time=round(end_time - start_time, 3)
        )
        
    except Exception as e:
        logger.error(f"Sentiment analysis error: {e}")
        raise HTTPException(status_code=500, detail="Failed to analyze sentiment")

@playground_router.post("/code-assist", response_model=CodeAssistResponse)
async def code_assist(request: CodeAssistRequest):
    """
    AI code assistance for explanation, optimization, and debugging
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        code = request.code.strip()
        language = request.language.lower()
        task = request.task
        
        # Generate response based on task type
        if task == "explain":
            result = f"This {language} code appears to implement functionality related to data processing. The code structure suggests it handles input validation, processes the data through several steps, and returns formatted results. Key components include error handling, optimization for performance, and maintainable code organization."
            
            suggestions = [
                "Consider adding more detailed comments for complex logic",
                "Implement comprehensive error handling for edge cases",
                "Add unit tests to verify functionality",
                "Consider performance optimizations for large datasets"
            ]
            
        elif task == "optimize":
            result = f"Here are optimization recommendations for your {language} code: Use more efficient data structures, implement caching where appropriate, reduce unnecessary computations, and consider async/await patterns for I/O operations. The current implementation can be improved by applying these optimization strategies."
            
            suggestions = [
                "Replace O(n²) operations with O(n log n) alternatives",
                "Implement memoization for expensive calculations",
                "Use batch operations instead of individual requests",
                "Consider lazy loading for large datasets"
            ]
            
        elif task == "debug":
            result = f"Potential issues identified in your {language} code: Check for null/undefined values, verify array bounds, ensure proper error handling, and validate input parameters. Common debugging approaches include adding logging statements, using debugger tools, and implementing comprehensive test cases."
            
            suggestions = [
                "Add null checks before accessing object properties",
                "Implement try-catch blocks for error-prone operations",
                "Add logging to track variable values and execution flow",
                "Validate function parameters at the beginning"
            ]
            
        elif task == "review":
            result = f"Code review for your {language} implementation: The code shows good structure and follows many best practices. Areas for improvement include adding documentation, implementing error handling, optimizing performance, and ensuring code consistency. Overall, the implementation demonstrates solid programming fundamentals."
            
            suggestions = [
                "Add comprehensive documentation and comments",
                "Implement consistent naming conventions",
                "Add error handling and validation",
                "Consider refactoring large functions into smaller ones"
            ]
            
        else:  # generate
            result = f"Here's a {language} code example based on your requirements: The generated code includes proper structure, error handling, and follows best practices for {language} development. You can use this as a starting point and modify it according to your specific needs."
            
            suggestions = [
                "Customize the code for your specific use case",
                "Add additional error handling as needed",
                "Implement unit tests for the functionality",
                "Consider adding configuration options"
            ]
        
        end_time = asyncio.get_event_loop().time()
        
        return CodeAssistResponse(
            result=result,
            suggestions=suggestions,
            language=language,
            task_type=task,
            analysis_time=round(end_time - start_time, 3)
        )
        
    except Exception as e:
        logger.error(f"Code assist error: {e}")
        raise HTTPException(status_code=500, detail="Failed to analyze code")

@playground_router.post("/analyze-image", response_model=ImageAnalysisResponse)
async def analyze_image(file: UploadFile = File(...)):
    """
    Analyze uploaded images for objects, colors, and content
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Validate file type
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read and process image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Get image dimensions
        width, height = image.size
        
        # Analyze colors (simplified)
        colors = image.getcolors(maxcolors=256)
        if colors:
            # Get dominant colors
            sorted_colors = sorted(colors, key=lambda x: x[0], reverse=True)
            dominant_colors = []
            for count, color in sorted_colors[:5]:
                if isinstance(color, tuple) and len(color) >= 3:
                    hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
                    dominant_colors.append(hex_color)
        else:
            dominant_colors = ["#808080", "#606060", "#404040"]
        
        # Mock object detection (would use real AI models in production)
        objects = [
            {"name": "object", "confidence": 0.85, "bbox": [100, 100, 200, 200]},
            {"name": "background", "confidence": 0.92, "bbox": [0, 0, width, height]}
        ]
        
        # Generate description
        description = f"This image appears to be a {width}x{height} pixel image with rich color composition. The dominant colors suggest a well-balanced visual composition with good contrast and detail. The image contains various elements that create an interesting and engaging visual narrative."
        
        # Generate tags
        tags = ["visual", "composition", "colors", "digital", "image"]
        
        end_time = asyncio.get_event_loop().time()
        
        return ImageAnalysisResponse(
            description=description,
            objects=objects,
            colors=dominant_colors,
            tags=tags,
            confidence=0.87,
            analysis_time=round(end_time - start_time, 3)
        )
        
    except Exception as e:
        logger.error(f"Image analysis error: {e}")
        raise HTTPException(status_code=500, detail="Failed to analyze image")

# Export the router to be included in main app
__all__ = ["playground_router"]