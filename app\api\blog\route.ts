import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import type { BlogPost } from '@shared/schema';
import { RequestTimer, PerformanceMonitor } from '@/lib/performance';
import { SecurityHeaders, IPUtils } from '@/lib/security';

// Query parameters validation schema
const blogQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).optional().default("1"),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(50)).optional().default("10"),
  category: z.string().max(50).optional(),
  search: z.string().max(100).optional(),
});

// Mock blog data - replace with actual blog API or database
const mockBlogPosts: Pick<BlogPost, 'id' | 'title' | 'excerpt' | 'category' | 'readTime' | 'publishedAt' | 'slug'>[] = [
  {
    id: '1',
    title: 'Getting Started with Next.js and TypeScript',
    excerpt: 'Learn how to build modern web applications with Next.js and TypeScript for better developer experience.',
    category: 'Web Development',
    readTime: 8,
    publishedAt: new Date('2024-01-15'),
    slug: 'getting-started-nextjs-typescript'
  },
  {
    id: '2',
    title: 'Building Scalable APIs with Node.js',
    excerpt: 'Best practices for creating robust and scalable APIs using Node.js and Express.',
    category: 'Backend Development',
    readTime: 12,
    publishedAt: new Date('2024-01-10'),
    slug: 'building-scalable-apis-nodejs'
  },
  {
    id: '3',
    title: 'Modern Frontend Development with React',
    excerpt: 'Exploring the latest React features and best practices for modern frontend development.',
    category: 'Frontend Development',
    readTime: 10,
    publishedAt: new Date('2024-01-05'),
    slug: 'modern-frontend-development-react'
  },
  {
    id: '4',
    title: 'Performance Optimization in Web Applications',
    excerpt: 'Comprehensive guide to optimizing web application performance and Core Web Vitals.',
    category: 'Performance',
    readTime: 15,
    publishedAt: new Date('2024-01-20'),
    slug: 'performance-optimization-web-applications'
  },
  {
    id: '5',
    title: 'Security Best Practices for Modern Web Apps',
    excerpt: 'Essential security measures every developer should implement in their applications.',
    category: 'Security',
    readTime: 12,
    publishedAt: new Date('2024-01-25'),
    slug: 'security-best-practices-web-apps'
  }
];

export async function GET(request: NextRequest): Promise<NextResponse> {
  const timer = new RequestTimer();
  const clientIP = IPUtils.getRealIP(request.headers);
  const userAgent = request.headers.get('user-agent') || '';

  try {
    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryValidation = blogQuerySchema.safeParse({
      page: searchParams.get('page') || undefined,
      limit: searchParams.get('limit') || undefined,
      category: searchParams.get('category') || undefined,
      search: searchParams.get('search') || undefined,
    });

    if (!queryValidation.success) {
      console.warn(`Invalid blog query from IP: ${clientIP}:`, queryValidation.error.issues);
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: queryValidation.error.issues.map(issue => ({
            field: issue.path.join('.'),
            message: issue.message
          }))
        },
        {
          status: 400,
          headers: SecurityHeaders.getApiHeaders()
        }
      );
    }

    const { page, limit, category, search } = queryValidation.data;

    // Filter posts based on query parameters
    let filteredPosts = [...mockBlogPosts];

    if (category) {
      filteredPosts = filteredPosts.filter(post =>
        post.category.toLowerCase().includes(category.toLowerCase())
      );
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredPosts = filteredPosts.filter(post =>
        post.title.toLowerCase().includes(searchLower) ||
        post.excerpt.toLowerCase().includes(searchLower) ||
        post.category.toLowerCase().includes(searchLower)
      );
    }

    // Sort by published date (newest first)
    filteredPosts.sort((a, b) => {
      const dateA = a.publishedAt ? a.publishedAt.getTime() : 0;
      const dateB = b.publishedAt ? b.publishedAt.getTime() : 0;
      return dateB - dateA;
    });

    // Implement pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

    // Calculate pagination metadata
    const totalPosts = filteredPosts.length;
    const totalPages = Math.ceil(totalPosts / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Simulate realistic API delay based on data complexity
    const delay = Math.min(50 + (paginatedPosts.length * 2), 200);
    await new Promise(resolve => setTimeout(resolve, delay));

    // Record performance metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: timer.end(),
      url: '/api/blog',
      userAgent,
    });

    // Log API access
    console.log(`Blog API accessed from IP: ${clientIP}, returned ${paginatedPosts.length} posts`);

    const response = NextResponse.json({
      posts: paginatedPosts,
      pagination: {
        current: page,
        total: totalPages,
        hasNext: hasNextPage,
        hasPrev: hasPrevPage,
        totalPosts,
      },
      filters: {
        category: category || null,
        search: search || null,
      }
    });

    // Add caching headers for better performance
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=60'); // 5 minutes cache
    
    // Add security headers
    const securityHeaders = SecurityHeaders.getApiHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  } catch (error) {
    const duration = timer.end();
    
    // Record error metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: duration,
      url: '/api/blog',
      userAgent,
    });

    console.error(`Blog API error from IP: ${clientIP}:`, error);
    
    // Don't leak internal error details
    const errorMessage = process.env.NODE_ENV === 'development'
      ? `Internal error: ${(error as Error).message}`
      : 'Failed to fetch blog posts';

    return NextResponse.json(
      { error: errorMessage },
      {
        status: 500,
        headers: SecurityHeaders.getApiHeaders()
      }
    );
  }
}