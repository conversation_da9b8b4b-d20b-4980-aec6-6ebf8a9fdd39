# Dependencies
node_modules/
.pnp
.pnp.js

# Build outputs
.next/
out/
build/
dist/

# Environment files
.env*

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
.DS_Store
*.tsbuildinfo

# Coverage directory used by tools like istanbul
coverage/

# Package manager lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE files
.vscode/
.idea/

# Generated files
*.min.js
*.min.css

# Database
*.db
*.sqlite

# Temporary files
*.tmp
*.temp

# Documentation
CHANGELOG.md

# Third-party components (already formatted)
app/components/ui/

# Build artifacts
.vercel/
.netlify/

# Specific files
public/
*.config.js