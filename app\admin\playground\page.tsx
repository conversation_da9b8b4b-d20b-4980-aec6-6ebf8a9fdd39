'use client'

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Search, 
  Brain, 
  BarChart3, 
  Settings, 
  Eye, 
  Edit, 
  Trash2,
  TrendingUp,
  Users,
  Clock,
  Star,
  MoreHorizontal
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Link from 'next/link';

interface PlaygroundProject {
  id: number;
  title: string;
  description: string;
  project_type: string;
  icon: string;
  gradient: string;
  tags: string[];
  status: 'active' | 'inactive' | 'maintenance';
  featured: boolean;
  usage_count: number;
  daily_usage: number;
  avg_session_time: number;
  user_rating: number;
  created_at: string;
  updated_at: string;
}

export default function PlaygroundManagement() {
  const [projects, setProjects] = useState<PlaygroundProject[]>([
    {
      id: 1,
      title: 'AI Text Generator',
      description: 'Generate creative content, stories, and professional text using advanced language models',
      project_type: 'text-generator',
      icon: 'Sparkles',
      gradient: 'from-purple-500 to-pink-500',
      tags: ['GPT', 'Creative Writing', 'Content Generation'],
      status: 'active',
      featured: true,
      usage_count: 1247,
      daily_usage: 89,
      avg_session_time: 4.2,
      user_rating: 4.8,
      created_at: '2024-01-01',
      updated_at: '2024-01-15'
    },
    {
      id: 2,
      title: 'AI Image Analysis',
      description: 'Upload images for intelligent analysis, object detection, and detailed descriptions',
      project_type: 'image-analysis',
      icon: 'Image',
      gradient: 'from-blue-500 to-cyan-500',
      tags: ['Computer Vision', 'Object Detection', 'Image Processing'],
      status: 'active',
      featured: true,
      usage_count: 892,
      daily_usage: 67,
      avg_session_time: 3.8,
      user_rating: 4.6,
      created_at: '2024-01-05',
      updated_at: '2024-01-14'
    },
    {
      id: 3,
      title: 'AI Code Assistant',
      description: 'Get help with code explanation, optimization, debugging, and generation',
      project_type: 'code-assistant',
      icon: 'Code',
      gradient: 'from-green-500 to-emerald-500',
      tags: ['Code Generation', 'Debugging', 'Optimization'],
      status: 'active',
      featured: false,
      usage_count: 634,
      daily_usage: 45,
      avg_session_time: 6.1,
      user_rating: 4.7,
      created_at: '2024-01-08',
      updated_at: '2024-01-13'
    },
    {
      id: 4,
      title: 'Sentiment Analysis',
      description: 'Analyze emotions and sentiment in text, reviews, and social media content',
      project_type: 'sentiment-analysis',
      icon: 'BarChart3',
      gradient: 'from-orange-500 to-red-500',
      tags: ['NLP', 'Emotion Detection', 'Text Analysis'],
      status: 'maintenance',
      featured: false,
      usage_count: 423,
      daily_usage: 12,
      avg_session_time: 2.9,
      user_rating: 4.3,
      created_at: '2024-01-10',
      updated_at: '2024-01-12'
    },
    {
      id: 5,
      title: 'AI Chatbot',
      description: 'Intelligent conversational AI with personality and domain expertise',
      project_type: 'chatbot',
      icon: 'MessageSquare',
      gradient: 'from-indigo-500 to-purple-500',
      tags: ['Conversational AI', 'Context Awareness', 'Personality'],
      status: 'active',
      featured: true,
      usage_count: 756,
      daily_usage: 78,
      avg_session_time: 8.4,
      user_rating: 4.9,
      created_at: '2024-01-12',
      updated_at: '2024-01-15'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('usage_count');

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const totalUsage = projects.reduce((sum, p) => sum + p.usage_count, 0);
  const activeProjects = projects.filter(p => p.status === 'active').length;
  const avgRating = projects.reduce((sum, p) => sum + p.user_rating, 0) / projects.length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this AI project?')) {
      setProjects(projects.filter(p => p.id !== id));
    }
  };

  const toggleFeatured = (id: number) => {
    setProjects(projects.map(p => 
      p.id === id ? { ...p, featured: !p.featured } : p
    ));
  };

  const toggleStatus = (id: number) => {
    setProjects(projects.map(p => 
      p.id === id ? { 
        ...p, 
        status: p.status === 'active' ? 'inactive' : 'active' 
      } : p
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">AI Playground</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage AI projects, monitor usage, and track performance
          </p>
        </div>
        <Button asChild className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
          <Link href="/admin/playground/new">
            <Plus className="w-4 h-4 mr-2" />
            New AI Project
          </Link>
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Usage</p>
                <p className="text-2xl font-bold">{totalUsage.toLocaleString()}</p>
              </div>
              <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Projects</p>
                <p className="text-2xl font-bold">{activeProjects}</p>
              </div>
              <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                <Brain className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Rating</p>
                <p className="text-2xl font-bold">{avgRating.toFixed(1)}</p>
              </div>
              <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
                <Star className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Daily Users</p>
                <p className="text-2xl font-bold">{projects.reduce((sum, p) => sum + p.daily_usage, 0)}</p>
              </div>
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                <Users className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search AI projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="usage_count">Usage Count</SelectItem>
                <SelectItem value="daily_usage">Daily Usage</SelectItem>
                <SelectItem value="user_rating">Rating</SelectItem>
                <SelectItem value="updated_at">Last Updated</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.map((project, index) => (
          <motion.div
            key={project.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="group hover:shadow-lg transition-all duration-300">
              <div className="relative">
                {/* Gradient Background */}
                <div className={`h-24 bg-gradient-to-r ${project.gradient} rounded-t-lg relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/10" />
                  
                  {/* Featured Badge */}
                  {project.featured && (
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-white/20 text-white border-white/30">
                        <Star className="w-3 h-3 mr-1" />
                        Featured
                      </Badge>
                    </div>
                  )}

                  {/* Actions Menu */}
                  <div className="absolute top-3 right-3">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="secondary" size="sm" className="bg-white/20 border-white/30 text-white hover:bg-white/30">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/playground/${project.id}/edit`}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleFeatured(project.id)}>
                          <Star className="w-4 h-4 mr-2" />
                          {project.featured ? 'Unfeature' : 'Feature'}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleStatus(project.id)}>
                          <Settings className="w-4 h-4 mr-2" />
                          {project.status === 'active' ? 'Deactivate' : 'Activate'}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(project.id)} className="text-red-600">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Icon */}
                  <div className="absolute bottom-3 left-3">
                    <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <Brain className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>
              </div>

              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Title and Status */}
                  <div className="flex items-start justify-between">
                    <h3 className="font-semibold text-lg leading-tight">{project.title}</h3>
                    <Badge className={getStatusColor(project.status)}>
                      {project.status}
                    </Badge>
                  </div>

                  {/* Description */}
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                    {project.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    {project.tags.slice(0, 2).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {project.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{project.tags.length - 2}
                      </Badge>
                    )}
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-500">{project.usage_count}</div>
                      <div className="text-xs text-gray-500">Total Uses</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-500">{project.user_rating}</div>
                      <div className="text-xs text-gray-500">Rating</div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/playground`}>
                        <Eye className="w-3 h-3 mr-1" />
                        View Live
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm" asChild className="ml-auto">
                      <Link href={`/admin/playground/${project.id}/analytics`}>
                        <BarChart3 className="w-3 h-3 mr-1" />
                        Analytics
                      </Link>
                    </Button>
                  </div>

                  {/* Meta */}
                  <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                    <span>{project.daily_usage} uses today</span>
                    <span>{project.avg_session_time}m avg session</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {filteredProjects.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <Brain className="w-6 h-6 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No AI projects found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your search or filter criteria
            </p>
            <Button variant="outline" onClick={() => {
              setSearchTerm('');
              setStatusFilter('all');
            }}>
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
