import { NextRequest, NextResponse } from 'next/server';
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { projects, insertProjectSchema } from '@shared/schema';
import { eq, desc } from 'drizzle-orm';

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const featured = searchParams.get('featured');
    const limit = searchParams.get('limit');

    let query = db.select().from(projects);
    
    if (status && status !== 'all') {
      query = query.where(eq(projects.status, status as any));
    }
    
    if (featured !== null && featured === 'true') {
      query = query.where(eq(projects.featured, true));
    }

    query = query.orderBy(desc(projects.updatedAt));

    if (limit) {
      query = query.limit(parseInt(limit));
    }

    const projectsList = await query;

    return NextResponse.json(projectsList);
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input using Zod schema
    const validatedData = insertProjectSchema.parse(body);
    
    // Insert project
    const newProject = await db.insert(projects).values(validatedData).returning();
    
    // Fetch GitHub data if URL provided
    if (validatedData.githubUrl) {
      try {
        const urlParts = validatedData.githubUrl.split('/');
        const owner = urlParts[urlParts.length - 2];
        const repo = urlParts[urlParts.length - 1];
        
        const githubResponse = await fetch(`https://api.github.com/repos/${owner}/${repo}`, {
          headers: {
            'Authorization': `token ${process.env.GITHUB_TOKEN}`,
            'Accept': 'application/vnd.github.v3+json',
          },
        });
        
        if (githubResponse.ok) {
          const githubData = await githubResponse.json();
          
          // Update project with GitHub data
          const githubInfo = {
            stars: githubData.stargazers_count,
            forks: githubData.forks_count,
            language: githubData.language,
            updated_at: githubData.updated_at,
            description: githubData.description
          };
          
          const updatedProject = await db
            .update(projects)
            .set({ githubData: githubInfo })
            .where(eq(projects.id, newProject[0].id))
            .returning();
          
          return NextResponse.json(updatedProject[0]);
        }
      } catch (githubError) {
        console.error('Error fetching GitHub data:', githubError);
        // Continue without GitHub data
      }
    }
    
    return NextResponse.json(newProject[0]);
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
