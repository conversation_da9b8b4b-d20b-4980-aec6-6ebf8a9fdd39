-- Initial database schema for D1
-- Convert PostgreSQL schema to SQLite (D1) compatible format

-- Users table
CREATE TABLE users (
    id TEXT PRIMARY KEY DEFAULT (hex(randomblob(16))),
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL
);

-- Blog posts table
CREATE TABLE blog_posts (
    id TEXT PRIMARY KEY DEFAULT (hex(randomblob(16))),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT NOT NULL,
    category TEXT NOT NULL,
    read_time INTEGER NOT NULL,
    published_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    slug TEXT NOT NULL UNIQUE
);

-- Projects table
CREATE TABLE projects (
    id TEXT PRIMARY KEY DEFAULT (hex(randomblob(16))),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    technologies TEXT NOT NULL, -- JSO<PERSON> string for array storage
    github_url TEXT,
    demo_url TEXT,
    image_url TEXT,
    featured TEXT NOT NULL DEFAULT 'false'
);

-- Contacts table
CREATE TABLE contacts (
    id TEXT PRIMARY KEY DEFAULT (hex(randomblob(16))),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX idx_blog_posts_published_at ON blog_posts(published_at);
CREATE INDEX idx_projects_featured ON projects(featured);
CREATE INDEX idx_contacts_created_at ON contacts(created_at);