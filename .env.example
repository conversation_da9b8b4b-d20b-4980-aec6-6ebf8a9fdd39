# =================================================================
# Environment Configuration Template
# =================================================================
# Copy this file to .env.local and update with your actual values
# Never commit .env.local or any file containing real secrets!

# =================================================================
# Database Configuration
# =================================================================
# PostgreSQL connection string - Required for all database operations
# Format: postgresql://username:password@hostname:port/database
# Example: postgresql://user:pass@localhost:5432/mydb
# Neon: postgresql://username:<EMAIL>/database
DATABASE_URL="postgresql://username:password@localhost:5432/getintheq"

# =================================================================
# Authentication & Security
# =================================================================
# JWT Secret - CRITICAL: Use a strong, unique secret in production
# Generate with: openssl rand -base64 32
JWT_SECRET="your-super-secret-jwt-key-change-in-production"

# Session secret for server-side sessions (if using express sessions)
SESSION_SECRET="your-session-secret-key"

# =================================================================
# GitHub Integration (Optional)
# =================================================================
# GitHub personal access token for repository data
# Permissions needed: public_repo (for public repos)
GITHUB_TOKEN="ghp_your_github_personal_access_token"

# Your GitHub username for fetching repositories
GITHUB_USERNAME="your-github-username"

# =================================================================
# Email Configuration (Optional)
# =================================================================
# SMTP settings for sending emails (contact forms, notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-specific-password"

# Email addresses
FROM_EMAIL="<EMAIL>"
ADMIN_EMAIL="<EMAIL>"

# =================================================================
# External APIs (Optional)
# =================================================================
# OpenAI API key for AI playground features
OPENAI_API_KEY="sk-your-openai-api-key"

# Anthropic API key for Claude integration
ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key"

# =================================================================
# Development & Deployment
# =================================================================
# Environment mode
NODE_ENV="development"

# Next.js specific
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# API base URL for playground (adjust for deployment)
NEXT_PUBLIC_API_URL="http://localhost:8000"

# =================================================================
# Analytics & Monitoring (Optional)
# =================================================================
# Google Analytics
NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"

# Sentry for error tracking
SENTRY_DSN="https://your-sentry-dsn"

# =================================================================
# Feature Flags (Optional)
# =================================================================
# Enable/disable features
ENABLE_PLAYGROUND="true"
ENABLE_BLOG="true"
ENABLE_ANALYTICS="true"
ENABLE_CONTACT_FORM="true"

# =================================================================
# Rate Limiting & Security
# =================================================================
# Rate limiting configuration
RATE_LIMIT_MAX_REQUESTS="100"
RATE_LIMIT_WINDOW_MS="900000"

# CORS origins (comma-separated)
CORS_ORIGINS="http://localhost:3000,https://yourdomain.com"

# =================================================================
# File Storage (Optional)
# =================================================================
# AWS S3 for file uploads
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-s3-bucket"

# Cloudinary for image optimization
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# =================================================================
# Development Tools
# =================================================================
# Log level for development
LOG_LEVEL="info"

# Debug mode
DEBUG="false"

# Enable source maps in production
GENERATE_SOURCEMAP="false"

# =================================================================
# Database Seeds & Setup
# =================================================================
# Default admin user for initial setup
DEFAULT_ADMIN_USERNAME="admin"
DEFAULT_ADMIN_EMAIL="<EMAIL>"
DEFAULT_ADMIN_PASSWORD="change-me-immediately"

# =================================================================
# Notes for Developers
# =================================================================
# 1. Copy this file to .env.local for local development
# 2. Copy to .env.production for production deployment
# 3. Never commit files with real values to version control
# 4. Use strong, unique secrets for JWT_SECRET and SESSION_SECRET
# 5. Generate GitHub token at: https://github.com/settings/tokens
# 6. For production, consider using environment variable management tools
# 7. Some variables are optional - only set what you need
# 8. Restart your development server after changing environment variables