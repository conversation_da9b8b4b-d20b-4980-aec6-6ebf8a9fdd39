'use client'

interface PageViewData {
  page_path: string;
  user_agent?: string;
  referrer?: string;
  session_id?: string;
}

class AnalyticsTracker {
  private sessionId: string;
  private isEnabled: boolean;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.isEnabled = typeof window !== 'undefined' && !this.isBot();
  }

  private generateSessionId(): string {
    if (typeof window === 'undefined') return '';
    
    let sessionId = sessionStorage.getItem('analytics_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('analytics_session_id', sessionId);
    }
    return sessionId;
  }

  private isBot(): boolean {
    if (typeof window === 'undefined') return true;
    
    const userAgent = navigator.userAgent.toLowerCase();
    const botPatterns = [
      'bot', 'crawler', 'spider', 'crawling', 'facebook', 'google',
      'baidu', 'bing', 'msn', 'duckduckbot', 'teoma', 'slurp',
      'yandex', 'lighthouse', 'pagespeed', 'gtmetrix'
    ];
    
    return botPatterns.some(pattern => userAgent.includes(pattern));
  }

  async trackPageView(path?: string): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const pageData: PageViewData = {
        page_path: path || window.location.pathname,
        user_agent: navigator.userAgent,
        referrer: document.referrer || undefined,
        session_id: this.sessionId
      };

      // Send to analytics API
      await fetch('/api/admin/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pageData),
      });
    } catch (error) {
      console.warn('Analytics tracking failed:', error);
    }
  }

  async trackEvent(eventName: string, properties?: Record<string, any>): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const eventData = {
        event_name: eventName,
        properties: {
          ...properties,
          page_path: window.location.pathname,
          session_id: this.sessionId,
          timestamp: new Date().toISOString()
        }
      };

      // For now, we'll just log events. In a full implementation,
      // you'd send this to an events API endpoint
      console.log('Analytics Event:', eventData);
    } catch (error) {
      console.warn('Event tracking failed:', error);
    }
  }

  // Track specific portfolio interactions
  async trackProjectView(projectId: string, projectTitle: string): Promise<void> {
    await this.trackEvent('project_view', {
      project_id: projectId,
      project_title: projectTitle
    });
  }

  async trackPlaygroundUsage(toolId: string, toolName: string): Promise<void> {
    await this.trackEvent('playground_usage', {
      tool_id: toolId,
      tool_name: toolName
    });
  }

  async trackBlogPostView(postId: string, postTitle: string): Promise<void> {
    await this.trackEvent('blog_post_view', {
      post_id: postId,
      post_title: postTitle
    });
  }

  async trackContactFormSubmission(): Promise<void> {
    await this.trackEvent('contact_form_submission');
  }

  async trackDownload(fileName: string, fileType: string): Promise<void> {
    await this.trackEvent('file_download', {
      file_name: fileName,
      file_type: fileType
    });
  }

  async trackExternalLinkClick(url: string, linkText?: string): Promise<void> {
    await this.trackEvent('external_link_click', {
      url,
      link_text: linkText
    });
  }

  // Performance tracking
  async trackPerformance(): Promise<void> {
    if (!this.isEnabled || typeof window === 'undefined') return;

    try {
      // Wait for page load to complete
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          
          if (navigation) {
            this.trackEvent('page_performance', {
              load_time: navigation.loadEventEnd - navigation.loadEventStart,
              dom_content_loaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
              first_paint: this.getFirstPaint(),
              largest_contentful_paint: this.getLargestContentfulPaint()
            });
          }
        }, 1000);
      });
    } catch (error) {
      console.warn('Performance tracking failed:', error);
    }
  }

  private getFirstPaint(): number | null {
    try {
      const paintEntries = performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
      return firstPaint ? firstPaint.startTime : null;
    } catch {
      return null;
    }
  }

  private getLargestContentfulPaint(): number | null {
    try {
      const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
      const lastEntry = lcpEntries[lcpEntries.length - 1];
      return lastEntry ? lastEntry.startTime : null;
    } catch {
      return null;
    }
  }

  // User engagement tracking
  trackTimeOnPage(): void {
    if (!this.isEnabled) return;

    const startTime = Date.now();
    let isActive = true;
    let totalActiveTime = 0;
    let lastActiveTime = startTime;

    // Track when user becomes inactive
    const handleVisibilityChange = () => {
      if (document.hidden) {
        if (isActive) {
          totalActiveTime += Date.now() - lastActiveTime;
          isActive = false;
        }
      } else {
        if (!isActive) {
          lastActiveTime = Date.now();
          isActive = true;
        }
      }
    };

    // Track when user leaves the page
    const handleBeforeUnload = () => {
      if (isActive) {
        totalActiveTime += Date.now() - lastActiveTime;
      }

      this.trackEvent('time_on_page', {
        total_time: Date.now() - startTime,
        active_time: totalActiveTime,
        engagement_rate: totalActiveTime / (Date.now() - startTime)
      });
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }
}

// Create singleton instance
const analytics = new AnalyticsTracker();

export default analytics;

// React hook for easy usage in components
export function useAnalytics() {
  return {
    trackPageView: analytics.trackPageView.bind(analytics),
    trackEvent: analytics.trackEvent.bind(analytics),
    trackProjectView: analytics.trackProjectView.bind(analytics),
    trackPlaygroundUsage: analytics.trackPlaygroundUsage.bind(analytics),
    trackBlogPostView: analytics.trackBlogPostView.bind(analytics),
    trackContactFormSubmission: analytics.trackContactFormSubmission.bind(analytics),
    trackDownload: analytics.trackDownload.bind(analytics),
    trackExternalLinkClick: analytics.trackExternalLinkClick.bind(analytics),
    trackTimeOnPage: analytics.trackTimeOnPage.bind(analytics)
  };
}
