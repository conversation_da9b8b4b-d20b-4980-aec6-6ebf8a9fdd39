"""
Security utilities for the serverless portfolio API
"""

import jwt
import hashlib
import hmac
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from pydantic import BaseModel
import re

class SecurityConfig:
    """Security configuration"""
    JWT_ALGORITHM = "HS256"
    JWT_EXPIRATION_HOURS = 24
    PASSWORD_MIN_LENGTH = 8
    MAX_REQUEST_SIZE = 1024 * 1024  # 1MB
    
class RateLimit:
    """Rate limiting configuration"""
    def __init__(self, requests: int = 100, window: int = 3600):
        self.requests = requests
        self.window = window

# Rate limiting storage (in production, use Redis or database)
rate_limit_storage: Dict[str, list] = {}

def get_client_ip(request: Request) -> str:
    """Extract client IP from request"""
    # Check for forwarded headers (common in serverless environments)
    forwarded_for = request.headers.get("x-forwarded-for")
    if forwarded_for:
        # Take the first IP in the chain
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("x-real-ip")
    if real_ip:
        return real_ip
    
    # Fallback to direct connection IP
    if hasattr(request, "client") and request.client:
        return request.client.host
    
    return "unknown"

def check_rate_limit(client_ip: str, rate_limit: RateLimit) -> bool:
    """Check if client has exceeded rate limit"""
    now = datetime.now()
    
    # Initialize if not exists
    if client_ip not in rate_limit_storage:
        rate_limit_storage[client_ip] = []
    
    # Clean old requests
    cutoff_time = now - timedelta(seconds=rate_limit.window)
    rate_limit_storage[client_ip] = [
        timestamp for timestamp in rate_limit_storage[client_ip]
        if timestamp > cutoff_time
    ]
    
    # Check limit
    if len(rate_limit_storage[client_ip]) >= rate_limit.requests:
        return False
    
    # Add current request
    rate_limit_storage[client_ip].append(now)
    return True

def rate_limit_middleware(rate_limit: RateLimit = RateLimit()):
    """Rate limiting middleware decorator"""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            client_ip = get_client_ip(request)
            
            if not check_rate_limit(client_ip, rate_limit):
                raise HTTPException(
                    status_code=429,
                    detail={
                        "error": "Rate limit exceeded",
                        "limit": rate_limit.requests,
                        "window": rate_limit.window,
                        "retry_after": rate_limit.window
                    }
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator

def validate_email(email: str) -> bool:
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def sanitize_input(text: str, max_length: int = 1000) -> str:
    """Sanitize text input"""
    if not isinstance(text, str):
        raise ValueError("Input must be a string")
    
    # Limit length
    text = text[:max_length]
    
    # Remove potentially dangerous characters
    # Allow alphanumeric, basic punctuation, and whitespace
    allowed_chars = re.compile(r'[^a-zA-Z0-9\s\.,!?\-@()[\]{}:;"\'/\\]')
    text = allowed_chars.sub('', text)
    
    # Normalize whitespace
    text = ' '.join(text.split())
    
    return text.strip()

def validate_github_username(username: str) -> bool:
    """Validate GitHub username format"""
    # GitHub username rules: alphanumeric and hyphens, no consecutive hyphens
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9-])*[a-zA-Z0-9]$'
    return bool(re.match(pattern, username)) and len(username) <= 39

def generate_secure_token() -> str:
    """Generate a cryptographically secure token"""
    return secrets.token_urlsafe(32)

def hash_password(password: str) -> str:
    """Hash password using PBKDF2"""
    salt = secrets.token_bytes(32)
    pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
    return salt + pwdhash

def verify_password(password: str, stored_hash: bytes) -> bool:
    """Verify password against stored hash"""
    salt = stored_hash[:32]
    stored_pwdhash = stored_hash[32:]
    pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
    return pwdhash == stored_pwdhash

def create_jwt_token(payload: Dict[str, Any], secret: str) -> str:
    """Create JWT token"""
    # Add expiration
    payload['exp'] = datetime.utcnow() + timedelta(hours=SecurityConfig.JWT_EXPIRATION_HOURS)
    payload['iat'] = datetime.utcnow()
    
    return jwt.encode(payload, secret, algorithm=SecurityConfig.JWT_ALGORITHM)

def verify_jwt_token(token: str, secret: str) -> Optional[Dict[str, Any]]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, secret, algorithms=[SecurityConfig.JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

def verify_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Verify webhook signature (for GitHub webhooks, etc.)"""
    if not signature.startswith('sha256='):
        return False
    
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    received_signature = signature[7:]  # Remove 'sha256=' prefix
    
    return hmac.compare_digest(expected_signature, received_signature)

def validate_request_size(request: Request, max_size: int = SecurityConfig.MAX_REQUEST_SIZE):
    """Validate request size"""
    content_length = request.headers.get("content-length")
    if content_length and int(content_length) > max_size:
        raise HTTPException(
            status_code=413,
            detail=f"Request too large. Maximum size: {max_size} bytes"
        )

class SecurityHeaders:
    """Security headers for responses"""
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        return {
            # Prevent XSS attacks
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            
            # HTTPS enforcement
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            
            # Content Security Policy
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self' https://api.github.com; "
                "font-src 'self' https://fonts.gstatic.com; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "frame-ancestors 'none';"
            ),
            
            # Referrer policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Permissions policy
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }

def apply_security_headers(response):
    """Apply security headers to response"""
    headers = SecurityHeaders.get_security_headers()
    for key, value in headers.items():
        response.headers[key] = value
    return response

# Input validation schemas
class SecureContactForm(BaseModel):
    """Secure contact form validation"""
    first_name: str
    last_name: str  
    email: str
    subject: str
    message: str
    
    def validate_and_sanitize(self):
        """Validate and sanitize all fields"""
        # Validate email
        if not validate_email(self.email):
            raise ValueError("Invalid email format")
        
        # Sanitize text fields
        self.first_name = sanitize_input(self.first_name, 50)
        self.last_name = sanitize_input(self.last_name, 50)
        self.subject = sanitize_input(self.subject, 100)
        self.message = sanitize_input(self.message, 1000)
        
        # Validate lengths
        if len(self.first_name) < 1:
            raise ValueError("First name is required")
        if len(self.last_name) < 1:
            raise ValueError("Last name is required")
        if len(self.subject) < 1:
            raise ValueError("Subject is required")
        if len(self.message) < 10:
            raise ValueError("Message must be at least 10 characters")
        
        return self