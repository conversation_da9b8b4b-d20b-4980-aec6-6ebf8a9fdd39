import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { AuthService } from '@/lib/auth';

export const GET = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const stats = await DatabaseService.getDashboardStats();
    
    // Calculate growth rate for analytics
    const analyticsQuery = `
      SELECT 
        COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as current_period,
        COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '60 days' AND created_at < NOW() - INTERVAL '30 days') as previous_period
      FROM page_views
    `;
    
    const analyticsResult = await DatabaseService.query(analyticsQuery);
    const analytics = analyticsResult.rows[0];
    
    const currentViews = parseInt(analytics.current_period);
    const previousViews = parseInt(analytics.previous_period);
    const growthRate = previousViews > 0 
      ? ((currentViews - previousViews) / previousViews * 100).toFixed(1)
      : '0';

    // Get recent activity
    const recentActivityQuery = `
      SELECT 
        'project' as type,
        title,
        'Project updated' as description,
        updated_at as timestamp,
        status
      FROM projects 
      WHERE updated_at >= NOW() - INTERVAL '7 days'
      
      UNION ALL
      
      SELECT 
        'blog' as type,
        title,
        CASE 
          WHEN status = 'published' THEN 'Blog post published'
          ELSE 'Blog post updated'
        END as description,
        updated_at as timestamp,
        status
      FROM blog_posts 
      WHERE updated_at >= NOW() - INTERVAL '7 days'
      
      UNION ALL
      
      SELECT 
        'contact' as type,
        CONCAT(name, ' - ', subject) as title,
        'New contact message' as description,
        created_at as timestamp,
        status
      FROM contact_submissions 
      WHERE created_at >= NOW() - INTERVAL '7 days'
      
      ORDER BY timestamp DESC
      LIMIT 10
    `;
    
    const activityResult = await DatabaseService.query(recentActivityQuery);
    const recentActivity = activityResult.rows;

    return NextResponse.json({
      stats: {
        ...stats,
        analytics: {
          ...stats.analytics,
          growth_rate: parseFloat(growthRate)
        }
      },
      recent_activity: recentActivity
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
});
