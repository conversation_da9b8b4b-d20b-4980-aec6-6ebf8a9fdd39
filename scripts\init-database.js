const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/portfolio_db',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function initializeDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('🗄️  Initializing database schema...');
    
    // Read and execute schema file
    const schemaPath = path.join(__dirname, '../shared/database-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    await client.query(schema);
    console.log('✅ Database schema created successfully');
    
    // Insert sample data
    await insertSampleData(client);
    console.log('✅ Sample data inserted successfully');
    
    console.log('🎉 Database initialization complete!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

async function insertSampleData(client) {
  console.log('📝 Inserting sample data...');
  
  // Insert sample projects
  const projectsQuery = `
    INSERT INTO projects (title, description, long_description, github_url, demo_url, image_url, technologies, category, status, featured, sort_order, github_data) VALUES
    ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12),
    ($13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24),
    ($25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36)
    ON CONFLICT DO NOTHING
  `;
  
  const projectsValues = [
    // Project 1
    'AI-Powered Analytics Platform',
    'A comprehensive analytics platform using machine learning to provide insights from complex datasets.',
    'This platform combines the power of machine learning with intuitive data visualization to help businesses make data-driven decisions. Features include real-time data processing, predictive analytics, custom dashboards, and automated reporting.',
    'https://github.com/khiwniti/analytics-platform',
    'https://analytics.khiwniti.dev',
    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    JSON.stringify(['Python', 'TensorFlow', 'React', 'PostgreSQL', 'Docker']),
    'AI/ML',
    'active',
    true,
    1,
    JSON.stringify({ stars: 42, forks: 12, language: 'Python', updated_at: '2024-01-15' }),
    
    // Project 2
    'Real-time Recommendation Engine',
    'Scalable recommendation system processing millions of user interactions in real-time.',
    'A high-performance recommendation engine built to handle massive scale. Uses collaborative filtering, content-based filtering, and deep learning models to provide personalized recommendations. Processes over 1M events per second.',
    'https://github.com/khiwniti/recommendation-engine',
    null,
    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    JSON.stringify(['Node.js', 'Redis', 'MongoDB', 'Docker', 'Kubernetes']),
    'Backend',
    'active',
    false,
    2,
    JSON.stringify({ stars: 87, forks: 23, language: 'JavaScript', updated_at: '2024-01-10' }),
    
    // Project 3
    'Computer Vision Pipeline',
    'End-to-end computer vision solution for object detection and classification.',
    'Production-ready computer vision pipeline for real-time object detection, classification, and tracking. Supports multiple model architectures and can process video streams in real-time with high accuracy.',
    'https://github.com/khiwniti/cv-pipeline',
    'https://cv-demo.khiwniti.dev',
    'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    JSON.stringify(['PyTorch', 'OpenCV', 'FastAPI', 'AWS', 'Docker']),
    'AI/ML',
    'active',
    true,
    3,
    JSON.stringify({ stars: 156, forks: 34, language: 'Python', updated_at: '2024-01-08' })
  ];
  
  await client.query(projectsQuery, projectsValues);
  
  // Insert sample blog posts
  const blogQuery = `
    INSERT INTO blog_posts (title, slug, excerpt, content, featured_image, status, author_id, category_id, tags, reading_time, view_count, like_count, published_at) VALUES
    ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13),
    ($14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26)
    ON CONFLICT (slug) DO NOTHING
  `;
  
  const blogValues = [
    // Blog Post 1
    'Building AI-Powered Web Applications with Next.js',
    'ai-powered-web-apps-nextjs',
    'Learn how to integrate AI capabilities into your Next.js applications using modern tools and techniques.',
    'In this comprehensive guide, we\'ll explore how to build modern web applications that leverage artificial intelligence...',
    'https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    'published',
    1,
    2,
    JSON.stringify(['AI', 'Next.js', 'Web Development', 'Machine Learning']),
    8,
    1247,
    89,
    '2024-01-12',
    
    // Blog Post 2
    'The Future of Frontend Development',
    'future-frontend-development',
    'Exploring emerging trends and technologies that will shape the future of frontend development.',
    'Frontend development is evolving rapidly with new frameworks, tools, and paradigms emerging constantly...',
    'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400',
    'published',
    1,
    1,
    JSON.stringify(['Frontend', 'React', 'Vue', 'Angular', 'Web Development']),
    12,
    892,
    67,
    '2024-01-09'
  ];
  
  await client.query(blogQuery, blogValues);
  
  // Insert sample playground projects
  const playgroundQuery = `
    INSERT INTO playground_projects (title, description, project_type, icon, gradient, tags, status, featured, sort_order, usage_count) VALUES
    ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10),
    ($11, $12, $13, $14, $15, $16, $17, $18, $19, $20),
    ($21, $22, $23, $24, $25, $26, $27, $28, $29, $30)
    ON CONFLICT DO NOTHING
  `;
  
  const playgroundValues = [
    // AI Text Generator
    'AI Text Generator',
    'Generate creative content, stories, and professional text using advanced language models',
    'text-generator',
    'Sparkles',
    'from-purple-500 to-pink-500',
    JSON.stringify(['GPT', 'Creative Writing', 'Content Generation']),
    'active',
    true,
    1,
    1247,
    
    // AI Image Analysis
    'AI Image Analysis',
    'Upload images for intelligent analysis, object detection, and detailed descriptions',
    'image-analysis',
    'Image',
    'from-blue-500 to-cyan-500',
    JSON.stringify(['Computer Vision', 'Object Detection', 'Image Processing']),
    'active',
    true,
    2,
    892,
    
    // AI Code Assistant
    'AI Code Assistant',
    'Get help with code explanation, optimization, debugging, and generation',
    'code-assistant',
    'Code',
    'from-green-500 to-emerald-500',
    JSON.stringify(['Code Generation', 'Debugging', 'Optimization']),
    'active',
    false,
    3,
    634
  ];
  
  await client.query(playgroundQuery, playgroundValues);
  
  // Insert sample page views for analytics
  const analyticsQuery = `
    INSERT INTO page_views (page_path, ip_address, user_agent, referrer, session_id, created_at) VALUES
    ($1, $2, $3, $4, $5, NOW() - INTERVAL '1 day'),
    ($6, $7, $8, $9, $10, NOW() - INTERVAL '2 days'),
    ($11, $12, $13, $14, $15, NOW() - INTERVAL '3 days')
  `;
  
  const analyticsValues = [
    '/', '***********', 'Mozilla/5.0...', 'https://google.com', 'session1',
    '/playground', '***********', 'Mozilla/5.0...', 'https://github.com', 'session2',
    '/blog/ai-powered-web-apps-nextjs', '***********', 'Mozilla/5.0...', null, 'session3'
  ];
  
  await client.query(analyticsQuery, analyticsValues);
  
  console.log('✅ Sample data inserted');
}

// Run if called directly
if (require.main === module) {
  initializeDatabase().catch(console.error);
}

module.exports = { initializeDatabase };
