import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { users } from '@shared/schema';
import { eq } from 'drizzle-orm';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = '7d';

// Initialize database connection
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

export interface AuthUser {
  id: string;
  username: string;
  email?: string;
  role: string;
}

export class AuthService {
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  static generateToken(user: AuthUser): string {
    return jwt.sign(
      { 
        id: user.id, 
        username: user.username,
        email: user.email, 
        role: user.role 
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );
  }

  static verifyToken(token: string): AuthUser | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      return {
        id: decoded.id,
        username: decoded.username,
        email: decoded.email,
        role: decoded.role
      };
    } catch (error) {
      return null;
    }
  }

  static async login(username: string, password: string): Promise<{ user: AuthUser; token: string } | null> {
    try {
      const user = await db.select().from(users).where(eq(users.username, username)).limit(1);
      
      if (user.length === 0) {
        return null;
      }

      const userData = user[0];
      const isValidPassword = await this.verifyPassword(password, userData.password);
      
      if (!isValidPassword) {
        return null;
      }

      const authUser: AuthUser = {
        id: userData.id,
        username: userData.username,
        email: userData.email || undefined,
        role: userData.role
      };

      const token = this.generateToken(authUser);

      return { user: authUser, token };
    } catch (error) {
      console.error('Login error:', error);
      return null;
    }
  }

  static async createUser(userData: {
    username: string;
    password: string;
    email?: string;
    role?: string;
  }): Promise<AuthUser | null> {
    try {
      const hashedPassword = await this.hashPassword(userData.password);
      
      const newUser = await db.insert(users).values({
        username: userData.username,
        password: hashedPassword,
        email: userData.email,
        role: userData.role || 'admin'
      }).returning();
      
      if (newUser.length > 0) {
        const user = newUser[0];
        return {
          id: user.id,
          username: user.username,
          email: user.email || undefined,
          role: user.role
        };
      }
      
      return null;
    } catch (error) {
      console.error('Create user error:', error);
      return null;
    }
  }

  static async getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {
    try {
      const authHeader = request.headers.get('authorization');
      const cookieToken = request.cookies.get('auth-token')?.value;
      
      const token = authHeader?.replace('Bearer ', '') || cookieToken;
      
      if (!token) {
        return null;
      }

      return this.verifyToken(token);
    } catch (error) {
      return null;
    }
  }

  static requireAuth(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {
    return async (request: NextRequest) => {
      const user = await this.getUserFromRequest(request);
      
      if (!user) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          { 
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      return handler(request, user);
    };
  }

  static requireRole(role: string, handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {
    return async (request: NextRequest) => {
      const user = await this.getUserFromRequest(request);
      
      if (!user) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          { 
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      if (user.role !== role && user.role !== 'admin') {
        return new Response(
          JSON.stringify({ error: 'Insufficient permissions' }),
          { 
            status: 403,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      return handler(request, user);
    };
  }
}

// Middleware helper for client-side auth
export function withAuth<T extends (...args: any[]) => any>(
  WrappedComponent: T
): T {
  const AuthenticatedComponent = (props: any) => {
    // This would be implemented on the client side
    // For now, we'll just return the component
    return WrappedComponent(props);
  };

  return AuthenticatedComponent as T;
}

// Session management
export class SessionService {
  static setAuthCookie(token: string): string {
    const maxAge = 7 * 24 * 60 * 60; // 7 days in seconds
    return `auth-token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=${maxAge}; Path=/`;
  }

  static clearAuthCookie(): string {
    return 'auth-token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/';
  }

  static async validateSession(token: string): Promise<AuthUser | null> {
    return AuthService.verifyToken(token);
  }
}

// Rate limiting for auth endpoints
export class RateLimiter {
  private static attempts: Map<string, { count: number; resetTime: number }> = new Map();

  static isRateLimited(identifier: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record || now > record.resetTime) {
      this.attempts.set(identifier, { count: 1, resetTime: now + windowMs });
      return false;
    }

    if (record.count >= maxAttempts) {
      return true;
    }

    record.count++;
    return false;
  }

  static clearAttempts(identifier: string): void {
    this.attempts.delete(identifier);
  }
}
