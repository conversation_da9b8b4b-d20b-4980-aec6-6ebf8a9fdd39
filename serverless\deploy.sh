#!/bin/bash

# Deployment script for Portfolio API
set -e

echo "🚀 Portfolio API Deployment Script"
echo "=================================="

# Function to display help
show_help() {
    echo "Usage: ./deploy.sh [PLATFORM] [OPTIONS]"
    echo ""
    echo "Platforms:"
    echo "  vercel    Deploy to Vercel"
    echo "  aws       Deploy to AWS Lambda"
    echo "  gcp       Deploy to Google Cloud Functions"
    echo "  docker    Build and run Docker container"
    echo "  local     Run locally for development"
    echo ""
    echo "Options:"
    echo "  --env     Environment file to use (default: .env)"
    echo "  --help    Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./deploy.sh vercel"
    echo "  ./deploy.sh aws --env .env.production"
    echo "  ./deploy.sh local"
}

# Parse arguments
PLATFORM=""
ENV_FILE=".env"

while [[ $# -gt 0 ]]; do
    case $1 in
        vercel|aws|gcp|docker|local)
            PLATFORM="$1"
            shift
            ;;
        --env)
            ENV_FILE="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

if [ -z "$PLATFORM" ]; then
    echo "❌ Platform is required"
    show_help
    exit 1
fi

# Check if environment file exists
if [ ! -f "$ENV_FILE" ] && [ "$PLATFORM" != "local" ]; then
    echo "❌ Environment file $ENV_FILE not found"
    echo "💡 Copy .env.example to $ENV_FILE and configure your variables"
    exit 1
fi

# Load environment variables
if [ -f "$ENV_FILE" ]; then
    echo "📄 Loading environment from $ENV_FILE"
    export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
fi

# Deployment functions
deploy_vercel() {
    echo "🔄 Deploying to Vercel..."
    
    # Check if Vercel CLI is installed
    if ! command -v vercel &> /dev/null; then
        echo "📦 Installing Vercel CLI..."
        npm install -g vercel
    fi
    
    # Set environment variables
    if [ -f "$ENV_FILE" ]; then
        echo "🔧 Setting environment variables..."
        vercel env add GITHUB_TOKEN production
        vercel env add GITHUB_USERNAME production
        vercel env add RESEND_API_KEY production
        vercel env add CONTACT_EMAIL production
    fi
    
    # Deploy
    echo "🚀 Deploying..."
    vercel --prod
    
    echo "✅ Deployment to Vercel completed!"
}

deploy_aws() {
    echo "🔄 Deploying to AWS Lambda..."
    
    # Check if AWS SAM CLI is installed
    if ! command -v sam &> /dev/null; then
        echo "❌ AWS SAM CLI is required. Install it first:"
        echo "https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
        exit 1
    fi
    
    # Build and deploy
    echo "🔨 Building SAM application..."
    sam build
    
    echo "🚀 Deploying to AWS..."
    sam deploy --guided
    
    echo "✅ Deployment to AWS completed!"
}

deploy_gcp() {
    echo "🔄 Deploying to Google Cloud Functions..."
    
    # Check if gcloud CLI is installed
    if ! command -v gcloud &> /dev/null; then
        echo "❌ Google Cloud CLI is required. Install it first:"
        echo "https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Deploy function
    echo "🚀 Deploying function..."
    gcloud functions deploy portfolio-api \
        --runtime python39 \
        --trigger-http \
        --allow-unauthenticated \
        --set-env-vars "GITHUB_TOKEN=$GITHUB_TOKEN,GITHUB_USERNAME=$GITHUB_USERNAME,RESEND_API_KEY=$RESEND_API_KEY,CONTACT_EMAIL=$CONTACT_EMAIL"
    
    echo "✅ Deployment to Google Cloud completed!"
}

deploy_docker() {
    echo "🔄 Building Docker container..."
    
    # Build Docker image
    echo "🔨 Building image..."
    docker build -t portfolio-api .
    
    # Run container
    echo "🚀 Running container..."
    docker run -d \
        --name portfolio-api \
        -p 8000:8000 \
        --env-file "$ENV_FILE" \
        portfolio-api
    
    echo "✅ Docker container is running on http://localhost:8000"
    echo "🔗 API Documentation: http://localhost:8000/api/docs"
}

run_local() {
    echo "🔄 Running locally for development..."
    
    # Check if Python is installed
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 is required"
        exit 1
    fi
    
    # Install dependencies
    echo "📦 Installing dependencies..."
    pip install -r requirements.txt
    
    # Load environment variables and run
    if [ -f "$ENV_FILE" ]; then
        echo "🚀 Starting development server..."
        export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    fi
    
    uvicorn main:app --host 0.0.0.0 --port 8000 --reload
}

# Execute deployment based on platform
case $PLATFORM in
    vercel)
        deploy_vercel
        ;;
    aws)
        deploy_aws
        ;;
    gcp)
        deploy_gcp
        ;;
    docker)
        deploy_docker
        ;;
    local)
        run_local
        ;;
esac

echo ""
echo "🎉 Deployment completed successfully!"
echo "📚 Remember to update your frontend API endpoints to point to the new serverless backend."