'use client'

import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { APIClient } from "@/lib/api-client";
import { QUERY_KEYS, CACHE_TIMES } from "@/lib/constants";

interface GitHubStatsCardProps {
  className?: string;
}

const StatItem = ({ 
  value, 
  label, 
  color, 
  testId,
  formatter = (val: number) => val.toString()
}: { 
  value?: number; 
  label: string; 
  color: string; 
  testId: string;
  formatter?: (val: number) => string;
}) => (
  <motion.div 
    className="p-4"
    whileHover={{ scale: 1.05 }}
    data-testid={testId}
  >
    <div className={`text-3xl font-bold ${color} mb-2`}>
      {value !== undefined ? formatter(value) : '0'}
    </div>
    <div className="text-sm text-gray-600 dark:text-gray-400">{label}</div>
  </motion.div>
);

const StatSkeleton = () => (
  <div className="p-4">
    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse" />
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
  </div>
);

export function GitHubStatsCard({ className = "" }: GitHubStatsCardProps) {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: [QUERY_KEYS.GITHUB_STATS],
    queryFn: APIClient.getGitHubStats,
    staleTime: CACHE_TIMES.GITHUB_STATS,
    retry: 2,
  });

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  return (
    <motion.div 
      className={`glass-effect rounded-2xl p-8 text-center ${className}`}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
    >
      <h3 className="text-2xl font-bold mb-6 gradient-text">GitHub Activity</h3>
      
      {error && (
        <div className="text-red-500 text-sm mb-4">
          Failed to load GitHub stats. Using default values.
        </div>
      )}
      
      {isLoading ? (
        <div className="grid md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <StatSkeleton key={i} />
          ))}
        </div>
      ) : (
        <div className="grid md:grid-cols-4 gap-6">
          <StatItem 
            value={stats?.public_repos || 0} 
            label="Repositories" 
            color="text-primary" 
            testId="github-repos" 
          />
          <StatItem 
            value={stats?.contributions_last_year || 0} 
            label="Contributions" 
            color="text-secondary" 
            testId="github-contributions" 
            formatter={formatNumber}
          />
          <StatItem 
            value={stats?.total_stars || 0} 
            label="Stars Received" 
            color="text-accent" 
            testId="github-stars" 
          />
          <StatItem 
            value={stats?.followers || 0} 
            label="Followers" 
            color="text-primary" 
            testId="github-followers" 
          />
        </div>
      )}
    </motion.div>
  );
}
