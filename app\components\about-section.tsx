'use client'

import { useRef } from "react";

import { motion, useScroll, useTransform } from "framer-motion";

import { SkillBar } from "./skill-bar";

export function AboutSection() {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });
  
  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [0, 1, 0]);

  return (
    <section id="about" className="py-20 relative overflow-hidden" ref={ref}>
      {/* Animated background elements */}
      <motion.div 
        className="absolute inset-0 opacity-10"
        style={{ y }}
      >
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full filter blur-3xl" />
        <div className="absolute top-1/2 right-1/4 w-48 h-48 bg-gradient-to-br from-accent/20 to-primary/20 rounded-full filter blur-3xl" />
        <div className="absolute bottom-1/4 left-1/2 w-72 h-72 bg-gradient-to-br from-secondary/20 to-accent/20 rounded-full filter blur-3xl" />
      </motion.div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">About Me</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Passionate about leveraging AI and data science to solve real-world problems
          </p>
        </motion.div>
        
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div 
            className="space-y-6 relative"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            {/* Professional image with floating frame */}
            <motion.div 
              className="relative"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute -inset-4 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-3xl blur-xl"
                animate={{
                  rotate: 360,
                }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  ease: "linear",
                  repeatType: "loop"
                }}
                style={{
                  transformOrigin: "center center"
                }}
              />
              
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/10 to-white/5 dark:from-gray-800/50 dark:to-gray-900/50 backdrop-blur-sm border border-white/20 dark:border-gray-700/50">
                <img 
                  src="/attached_assets/image_1754923605155.png"
                  alt="Khiw Nitithachot - Professional developer" 
                  className="w-full h-auto object-cover"
                  data-testid="about-image"
                />
                
                {/* Overlay with glassmorphism effect */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
                
                {/* Floating tech icons */}
                <motion.div 
                  className="absolute top-4 right-4 w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30"
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <i className="fas fa-code text-primary text-lg" />
                </motion.div>
                
                <motion.div 
                  className="absolute bottom-6 left-4 w-10 h-10 bg-secondary/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30"
                  animate={{ y: [0, -8, 0] }}
                  transition={{ duration: 2.5, repeat: Infinity, delay: 0.5 }}
                >
                  <i className="fas fa-brain text-secondary" />
                </motion.div>
                
                <motion.div 
                  className="absolute top-1/2 left-4 w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30"
                  animate={{ y: [0, -6, 0] }}
                  transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                >
                  <i className="fas fa-database text-accent text-sm" />
                </motion.div>
              </div>
            </motion.div>

            {/* Statistics cards with enhanced design */}
            <motion.div 
              className="grid grid-cols-2 gap-4"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <motion.div 
                className="text-center p-6 glass-effect rounded-2xl border border-primary/20"
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <motion.div 
                  className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  5+
                </motion.div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Years Experience</div>
              </motion.div>
              
              <motion.div 
                className="text-center p-6 glass-effect rounded-2xl border border-secondary/20"
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <motion.div 
                  className="text-3xl font-bold bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  50+
                </motion.div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Projects Completed</div>
              </motion.div>
            </motion.div>
          </motion.div>
          
          <motion.div 
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="glass-effect rounded-2xl p-8 border border-primary/10"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Data & AI Solutions Engineer
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                With extensive experience at Tesco Lotus PLC and Freshair, I specialize in developing innovative data solutions and AI-powered applications that drive business value and operational efficiency.
              </p>
              
              {/* Enhanced experience highlights */}
              <div className="space-y-3">
                <motion.div 
                  className="flex items-center space-x-3 p-3 rounded-lg bg-primary/5 border border-primary/10"
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="w-2 h-2 rounded-full bg-primary"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Machine Learning & Deep Learning Solutions</span>
                </motion.div>
                
                <motion.div 
                  className="flex items-center space-x-3 p-3 rounded-lg bg-secondary/5 border border-secondary/10"
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="w-2 h-2 rounded-full bg-secondary"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Data Pipeline & ETL Architecture</span>
                </motion.div>
                
                <motion.div 
                  className="flex items-center space-x-3 p-3 rounded-lg bg-accent/5 border border-accent/10"
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="w-2 h-2 rounded-full bg-accent"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Cloud Infrastructure & DevOps</span>
                </motion.div>
              </div>
            </motion.div>
            
            <div className="glass-effect rounded-2xl p-8">
              <h3 className="text-2xl font-bold mb-6 bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent">
                Technical Expertise
              </h3>
              <div className="grid gap-4">
                <SkillBar
                  name="Python & Data Science"
                  level={95}
                  gradient="bg-gradient-to-r from-blue-500 to-purple-600"
                  delay={0}
                  icon="🐍"
                  category="Programming"
                />
                <SkillBar
                  name="Machine Learning & AI"
                  level={92}
                  gradient="bg-gradient-to-r from-purple-500 to-pink-600"
                  delay={0.1}
                  icon="🤖"
                  category="AI/ML"
                />
                <SkillBar
                  name="React & TypeScript"
                  level={88}
                  gradient="bg-gradient-to-r from-cyan-500 to-blue-600"
                  delay={0.2}
                  icon="⚛️"
                  category="Frontend"
                />
                <SkillBar
                  name="Cloud Platforms (AWS, Azure)"
                  level={85}
                  gradient="bg-gradient-to-r from-orange-500 to-red-600"
                  delay={0.3}
                  icon="☁️"
                  category="Cloud"
                />
                <SkillBar
                  name="Data Engineering & ETL"
                  level={90}
                  gradient="bg-gradient-to-r from-green-500 to-teal-600"
                  delay={0.4}
                  icon="🔧"
                  category="Data"
                />
                <SkillBar
                  name="Docker & Kubernetes"
                  level={82}
                  gradient="bg-gradient-to-r from-indigo-500 to-purple-600"
                  delay={0.5}
                  icon="🐳"
                  category="DevOps"
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
