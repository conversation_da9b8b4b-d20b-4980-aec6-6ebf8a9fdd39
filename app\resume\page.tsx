'use client'

import { motion } from 'framer-motion';
import { Download, Calendar, MapPin, Mail, Phone, Award, Briefcase, GraduationCap, Code, Database } from 'lucide-react';
import { Linkedin, Github } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';

interface Experience {
  title: string;
  company: string;
  location: string;
  period: string;
  description: string[];
  technologies: string[];
}

interface Education {
  degree: string;
  institution: string;
  location: string;
  period: string;
  gpa?: string;
  achievements: string[];
}

interface Skill {
  name: string;
  level: number;
  category: 'programming' | 'frameworks' | 'tools' | 'databases';
}

interface Certification {
  name: string;
  issuer: string;
  date: string;
  credentialId?: string;
  url?: string;
}

const personalInfo = {
  name: '<PERSON><PERSON><PERSON>t',
  title: 'Data & AI Solutions Engineer',
  email: '<EMAIL>',
  phone: '+****************',
  location: 'San Francisco, CA',
  linkedin: 'https://linkedin.com/in/khiwniti',
  github: 'https://github.com/khiwniti',
  summary: 'Experienced Data & AI Solutions Engineer with 5+ years of expertise in machine learning, data engineering, and full-stack development. Proven track record of building scalable AI systems that drive business value and impact millions of users.',
};

const experiences: Experience[] = [
  {
    title: 'Senior Data & AI Engineer',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    period: '2022 - Present',
    description: [
      'Led development of ML-powered recommendation system serving 10M+ users daily',
      'Designed and implemented real-time data pipelines processing 1TB+ data/day using Apache Kafka and Spark',
      'Built computer vision models achieving 95% accuracy for automated quality control systems',
      'Mentored team of 5 junior engineers and established ML engineering best practices'
    ],
    technologies: ['Python', 'TensorFlow', 'PyTorch', 'Apache Spark', 'Kubernetes', 'AWS', 'MLflow']
  },
  {
    title: 'Machine Learning Engineer',
    company: 'DataTech Solutions',
    location: 'Seattle, WA',
    period: '2020 - 2022',
    description: [
      'Developed and deployed NLP models for sentiment analysis and text classification',
      'Created automated MLOps pipelines reducing model deployment time by 70%',
      'Collaborated with product teams to integrate AI features into customer-facing applications',
      'Optimized model performance achieving 40% reduction in inference latency'
    ],
    technologies: ['Python', 'Scikit-learn', 'Docker', 'Jenkins', 'PostgreSQL', 'Redis', 'FastAPI']
  },
  {
    title: 'Data Engineer',
    company: 'StartupXYZ',
    location: 'Austin, TX',
    period: '2019 - 2020',
    description: [
      'Built ETL pipelines processing multi-source data for analytics and ML training',
      'Designed data warehouse architecture supporting business intelligence initiatives',
      'Implemented data quality monitoring and alerting systems',
      'Collaborated with data scientists to productionize ML models'
    ],
    technologies: ['Python', 'SQL', 'Apache Airflow', 'BigQuery', 'dbt', 'Tableau']
  }
];

const education: Education[] = [
  {
    degree: 'Master of Science in Computer Science',
    institution: 'Stanford University',
    location: 'Stanford, CA',
    period: '2017 - 2019',
    gpa: '3.8/4.0',
    achievements: [
      'Concentration in Artificial Intelligence and Machine Learning',
      'Teaching Assistant for CS229 (Machine Learning)',
      'Published research on deep learning optimization techniques'
    ]
  },
  {
    degree: 'Bachelor of Science in Computer Engineering',
    institution: 'University of California, Berkeley',
    location: 'Berkeley, CA',
    period: '2013 - 2017',
    gpa: '3.7/4.0',
    achievements: [
      'Magna Cum Laude graduate',
      'President of AI/ML Student Society',
      'Winner of university-wide hackathon (2016, 2017)'
    ]
  }
];

const skills: Skill[] = [
  // Programming Languages
  { name: 'Python', level: 95, category: 'programming' },
  { name: 'JavaScript/TypeScript', level: 85, category: 'programming' },
  { name: 'R', level: 80, category: 'programming' },
  { name: 'SQL', level: 90, category: 'programming' },
  { name: 'Java', level: 75, category: 'programming' },
  
  // Frameworks & Libraries
  { name: 'TensorFlow/Keras', level: 90, category: 'frameworks' },
  { name: 'PyTorch', level: 85, category: 'frameworks' },
  { name: 'Scikit-learn', level: 95, category: 'frameworks' },
  { name: 'React/Next.js', level: 80, category: 'frameworks' },
  { name: 'FastAPI/Flask', level: 85, category: 'frameworks' },
  
  // Tools & Platforms
  { name: 'Docker/Kubernetes', level: 85, category: 'tools' },
  { name: 'AWS/GCP', level: 80, category: 'tools' },
  { name: 'Apache Spark', level: 85, category: 'tools' },
  { name: 'MLflow/Kubeflow', level: 80, category: 'tools' },
  { name: 'Git/CI/CD', level: 90, category: 'tools' },
  
  // Databases
  { name: 'PostgreSQL', level: 85, category: 'databases' },
  { name: 'MongoDB', level: 80, category: 'databases' },
  { name: 'Redis', level: 75, category: 'databases' },
  { name: 'BigQuery', level: 80, category: 'databases' }
];

const certifications: Certification[] = [
  {
    name: 'AWS Certified Machine Learning - Specialty',
    issuer: 'Amazon Web Services',
    date: '2023',
    credentialId: 'AWS-MLS-2023-001',
    url: 'https://aws.amazon.com/certification/'
  },
  {
    name: 'Google Cloud Professional Data Engineer',
    issuer: 'Google Cloud',
    date: '2022',
    credentialId: 'GCP-PDE-2022-001',
    url: 'https://cloud.google.com/certification'
  },
  {
    name: 'TensorFlow Developer Certificate',
    issuer: 'TensorFlow',
    date: '2021',
    credentialId: 'TF-DEV-2021-001',
    url: 'https://www.tensorflow.org/certificate'
  }
];

export default function ResumePage() {
  const downloadResume = () => {
    // In a real application, this would trigger a PDF download
    console.log('Downloading resume...');
    alert('Resume download would start here. In production, this would generate/serve a PDF file.');
  };

  const skillsByCategory = skills.reduce((acc, skill) => {
    if (!acc[skill.category]) acc[skill.category] = [];
    acc[skill.category].push(skill);
    return acc;
  }, {} as Record<string, Skill[]>);

  const categoryIcons = {
    programming: Code,
    frameworks: Code,
    tools: Briefcase,
    databases: Database
  };

  const categoryLabels = {
    programming: 'Programming Languages',
    frameworks: 'Frameworks & Libraries',
    tools: 'Tools & Platforms',
    databases: 'Databases'
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="py-20 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-4 gradient-text">
              Resume
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Professional experience and qualifications
            </p>
            <Button onClick={downloadResume} size="lg" className="gap-2">
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
          </motion.div>
        </div>
      </section>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Personal Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="mb-8">
            <CardContent className="p-8">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <h2 className="text-3xl font-bold mb-2">{personalInfo.name}</h2>
                  <p className="text-xl text-muted-foreground mb-4">{personalInfo.title}</p>
                  <p className="text-muted-foreground leading-relaxed">{personalInfo.summary}</p>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{personalInfo.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{personalInfo.phone}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{personalInfo.location}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Linkedin className="h-4 w-4 text-muted-foreground" />
                    <a href={personalInfo.linkedin} className="text-primary hover:underline">
                      LinkedIn Profile
                    </a>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Github className="h-4 w-4 text-muted-foreground" />
                    <a href={personalInfo.github} className="text-primary hover:underline">
                      GitHub Profile
                    </a>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Experience */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-2xl">
                <Briefcase className="h-6 w-6" />
                Professional Experience
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {experiences.map((exp, index) => (
                <div key={index} className="relative">
                  {index !== experiences.length - 1 && (
                    <div className="absolute left-0 top-8 w-px h-full bg-border" />
                  )}
                  <div className="flex gap-4">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-2">
                        <div>
                          <h3 className="text-lg font-semibold">{exp.title}</h3>
                          <p className="text-primary font-medium">{exp.company}</p>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {exp.period}
                          </div>
                          <div className="flex items-center gap-1 mt-1">
                            <MapPin className="h-3 w-3" />
                            {exp.location}
                          </div>
                        </div>
                      </div>
                      <ul className="space-y-1 mb-4 text-muted-foreground">
                        {exp.description.map((desc, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <span className="w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0" />
                            {desc}
                          </li>
                        ))}
                      </ul>
                      <div className="flex flex-wrap gap-1">
                        {exp.technologies.map(tech => (
                          <Badge key={tech} variant="outline" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Education */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-2xl">
                <GraduationCap className="h-6 w-6" />
                Education
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {education.map((edu, index) => (
                <div key={index}>
                  <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-2">
                    <div>
                      <h3 className="text-lg font-semibold">{edu.degree}</h3>
                      <p className="text-primary font-medium">{edu.institution}</p>
                      {edu.gpa && (
                        <p className="text-sm text-muted-foreground">GPA: {edu.gpa}</p>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {edu.period}
                      </div>
                      <div className="flex items-center gap-1 mt-1">
                        <MapPin className="h-3 w-3" />
                        {edu.location}
                      </div>
                    </div>
                  </div>
                  <ul className="space-y-1 text-muted-foreground">
                    {edu.achievements.map((achievement, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0" />
                        {achievement}
                      </li>
                    ))}
                  </ul>
                  {index !== education.length - 1 && <Separator className="mt-6" />}
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Skills */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-2xl">
                <Code className="h-6 w-6" />
                Technical Skills
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-8">
                {Object.entries(skillsByCategory).map(([category, categorySkills]) => {
                  const IconComponent = categoryIcons[category as keyof typeof categoryIcons];
                  return (
                    <div key={category}>
                      <h3 className="flex items-center gap-2 font-semibold mb-4">
                        <IconComponent className="h-4 w-4" />
                        {categoryLabels[category as keyof typeof categoryLabels]}
                      </h3>
                      <div className="space-y-3">
                        {categorySkills.map(skill => (
                          <div key={skill.name}>
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-sm font-medium">{skill.name}</span>
                              <span className="text-xs text-muted-foreground">{skill.level}%</span>
                            </div>
                            <Progress value={skill.level} className="h-2" />
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Certifications */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-2xl">
                <Award className="h-6 w-6" />
                Certifications
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                {certifications.map((cert, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-1">{cert.name}</h3>
                    <p className="text-primary font-medium text-sm mb-1">{cert.issuer}</p>
                    <p className="text-xs text-muted-foreground mb-2">Issued: {cert.date}</p>
                    {cert.credentialId && (
                      <p className="text-xs text-muted-foreground mb-2">
                        Credential ID: {cert.credentialId}
                      </p>
                    )}
                    {cert.url && (
                      <a 
                        href={cert.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-primary hover:underline"
                      >
                        View Credential →
                      </a>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}