# Overview

This is a personal portfolio website for <PERSON><PERSON><PERSON>, a Data & AI Solutions Engineer. The application is built as a full-stack web application featuring a React frontend with a modern design system, an Express.js backend API, and PostgreSQL database integration. The portfolio showcases professional work through sections including an about page, featured projects, blog posts, and a contact form. The application emphasizes clean, modern UI/UX with animations, responsive design, and dark/light theme support.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture

The frontend is built with **React 18** using **TypeScript** and follows a component-based architecture:

- **UI Framework**: Utilizes shadcn/ui components built on top of Radix UI primitives for accessibility and consistency
- **Styling**: TailwindCSS with CSS custom properties for theming, supporting both light and dark modes
- **State Management**: React Query (TanStack Query) for server state management and caching
- **Routing**: Wouter for lightweight client-side routing
- **Animations**: Framer Motion for smooth animations and transitions
- **Form Handling**: React Hook Form with Zod validation for type-safe form management
- **Build Tool**: Vite for fast development and optimized production builds

The application uses a single-page layout with smooth scrolling navigation between sections (hero, about, projects, blog, contact). Components are organized with reusable UI primitives in the `components/ui/` directory and feature-specific components for each section.

## Backend Architecture

The backend is an **Express.js** server written in TypeScript:

- **API Design**: RESTful API endpoints for blog posts, projects, contact forms, and GitHub integration
- **Middleware**: Custom logging middleware for API request monitoring
- **Storage Layer**: Abstract storage interface with in-memory implementation for development (designed to be replaced with database storage)
- **Development Setup**: Vite middleware integration for seamless development experience
- **Email Integration**: Nodemailer setup for contact form submissions
- **Error Handling**: Centralized error handling with proper HTTP status codes

The server follows a modular structure with separate files for routes, storage abstraction, and Vite development configuration.

## Data Storage Solutions

The application is configured for **PostgreSQL** database integration:

- **ORM**: Drizzle ORM for type-safe database operations and migrations
- **Database Provider**: Neon Database (serverless PostgreSQL)
- **Schema Definition**: Centralized schema in TypeScript with Zod validation
- **Migrations**: Drizzle Kit for database schema migrations
- **Current State**: Uses in-memory storage for development with sample data initialization

The database schema includes tables for users, blog posts, projects, and contact submissions with proper relationships and constraints.

## Authentication and Authorization

Currently, the application does not implement authentication as it's a portfolio website. The schema includes a users table prepared for future authentication implementation if needed for admin functionality.

# External Dependencies

## UI and Design
- **shadcn/ui**: Component library built on Radix UI primitives
- **Radix UI**: Accessible, unstyled UI primitives for complex components
- **TailwindCSS**: Utility-first CSS framework for styling
- **Framer Motion**: Animation library for React
- **Lucide React**: Icon library
- **Inter Font**: Typography from Google Fonts
- **Font Awesome**: Additional icons for social media links

## Data Management
- **TanStack React Query**: Server state management and caching
- **Drizzle ORM**: Type-safe database ORM
- **Zod**: Schema validation library
- **React Hook Form**: Form state management and validation

## Development Tools
- **Vite**: Build tool and development server
- **TypeScript**: Type safety across the entire application
- **ESBuild**: Fast JavaScript bundler for production builds
- **TSX**: TypeScript execution for development

## Database and Infrastructure
- **Neon Database**: Serverless PostgreSQL database
- **Nodemailer**: Email sending functionality for contact forms

## External APIs
- **GitHub API**: Integration for displaying GitHub statistics and repositories
- **Unsplash**: External image service for placeholder images

The application is designed to be deployed as a Node.js application with environment variables for database connection and email configuration.