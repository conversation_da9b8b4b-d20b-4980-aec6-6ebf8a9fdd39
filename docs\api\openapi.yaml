openapi: 3.0.3
info:
  title: getintheq.space API
  description: |
    Comprehensive API documentation for the getintheq.space portfolio and AI playground platform.
    
    This API provides endpoints for:
    - User authentication and session management
    - Blog content management and retrieval
    - Project portfolio management
    - Contact form submissions
    - AI playground tools and analytics
    - GitHub integration and repository data
    - Admin dashboard functionality
    
    ## Authentication
    Most admin endpoints require JWT authentication via:
    - Bearer token in Authorization header
    - HTTP-only cookie (auth-token)
    
    ## Rate Limiting
    All endpoints implement rate limiting with different thresholds:
    - Auth endpoints: 5 requests per 15 minutes
    - API endpoints: 100 requests per 15 minutes
    - Contact form: 3 requests per hour
    - Admin endpoints: 50 requests per 15 minutes
    
    ## Error Handling
    All endpoints return consistent error responses with appropriate HTTP status codes.
  version: 1.0.0
  contact:
    name: Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://getintheq.space/api
    description: Production server
  - url: http://localhost:3000/api
    description: Development server

security:
  - BearerAuth: []
  - CookieAuth: []

paths:
  # Authentication Endpoints
  /auth/login:
    post:
      tags: [Authentication]
      summary: User login
      description: Authenticate user with email/username and password
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            examples:
              email_login:
                summary: Login with email
                value:
                  email: <EMAIL>
                  password: securePassword123!
                  rememberMe: true
      responses:
        '200':
          description: Login successful
          headers:
            Set-Cookie:
              description: Authentication cookie
              schema:
                type: string
                example: auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'
        '500':
          $ref: '#/components/responses/InternalError'
    
    get:
      tags: [Authentication]
      summary: Check authentication status
      description: Verify if user is currently authenticated
      security: []
      responses:
        '200':
          description: Authentication status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthStatusResponse'

  /auth/logout:
    post:
      tags: [Authentication]
      summary: User logout
      description: Invalidate user session and clear authentication cookie
      responses:
        '200':
          description: Logout successful
          headers:
            Set-Cookie:
              description: Cleared authentication cookie
              schema:
                type: string
                example: auth-token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Logged out successfully

  /auth/setup:
    post:
      tags: [Authentication]
      summary: Initial setup
      description: Create first admin user (only available when no users exist)
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetupRequest'
      responses:
        '201':
          description: Setup completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: Setup already completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Blog Endpoints
  /blog:
    get:
      tags: [Blog]
      summary: Get blog posts
      description: Retrieve paginated list of published blog posts
      security: []
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of posts per page
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 10
        - name: category
          in: query
          description: Filter by category
          schema:
            type: string
            maxLength: 50
        - name: search
          in: query
          description: Search in title, excerpt, and content
          schema:
            type: string
            maxLength: 100
      responses:
        '200':
          description: Blog posts retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlogPostsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

  /blog/posts:
    get:
      tags: [Blog]
      summary: Get all blog posts (alias)
      description: Alternative endpoint for blog posts retrieval
      security: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Blog posts retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlogPostsResponse'

  # Contact Endpoints
  /contact:
    post:
      tags: [Contact]
      summary: Submit contact form
      description: Submit a contact form message
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactRequest'
            examples:
              business_inquiry:
                summary: Business inquiry
                value:
                  firstName: John
                  lastName: Doe
                  email: <EMAIL>
                  subject: Business Partnership Inquiry
                  message: We are interested in discussing potential collaboration opportunities...
      responses:
        '200':
          description: Message submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '429':
          description: Rate limit exceeded for contact form
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: Too many contact form submissions. Please try again later.
        '500':
          $ref: '#/components/responses/InternalError'

  # GitHub Integration Endpoints
  /github/repos:
    get:
      tags: [GitHub]
      summary: Get GitHub repositories
      description: Retrieve public repositories from GitHub
      security: []
      parameters:
        - name: featured
          in: query
          description: Filter for featured repositories only
          schema:
            type: boolean
      responses:
        '200':
          description: Repositories retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  repositories:
                    type: array
                    items:
                      $ref: '#/components/schemas/GitHubRepository'

  /github/stats:
    get:
      tags: [GitHub]
      summary: Get GitHub statistics
      description: Retrieve GitHub profile statistics
      security: []
      responses:
        '200':
          description: Statistics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GitHubStats'

  /github/sync:
    post:
      tags: [GitHub]
      summary: Sync GitHub data
      description: Manually trigger sync of GitHub repositories and stats
      responses:
        '200':
          description: Sync completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: GitHub data synchronized successfully
                  syncedRepos:
                    type: integer
                    example: 25

  # Admin Endpoints
  /admin/dashboard:
    get:
      tags: [Admin]
      summary: Get dashboard data
      description: Retrieve admin dashboard overview data
      responses:
        '200':
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/blog:
    get:
      tags: [Admin, Blog]
      summary: Get all blog posts for admin
      description: Retrieve all blog posts including drafts (admin only)
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - name: status
          in: query
          description: Filter by post status
          schema:
            type: string
            enum: [draft, published, archived]
      responses:
        '200':
          description: Blog posts retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminBlogPostsResponse'
    
    post:
      tags: [Admin, Blog]
      summary: Create blog post
      description: Create a new blog post
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBlogPostRequest'
      responses:
        '201':
          description: Blog post created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlogPost'

  /admin/blog/{id}:
    get:
      tags: [Admin, Blog]
      summary: Get blog post by ID
      description: Retrieve specific blog post for editing
      parameters:
        - name: id
          in: path
          required: true
          description: Blog post ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Blog post retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlogPost'
        '404':
          $ref: '#/components/responses/NotFound'
    
    put:
      tags: [Admin, Blog]
      summary: Update blog post
      description: Update existing blog post
      parameters:
        - name: id
          in: path
          required: true
          description: Blog post ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBlogPostRequest'
      responses:
        '200':
          description: Blog post updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlogPost'
    
    delete:
      tags: [Admin, Blog]
      summary: Delete blog post
      description: Delete blog post permanently
      parameters:
        - name: id
          in: path
          required: true
          description: Blog post ID
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Blog post deleted successfully
        '404':
          $ref: '#/components/responses/NotFound'

  /admin/projects:
    get:
      tags: [Admin, Projects]
      summary: Get all projects for admin
      description: Retrieve all projects including drafts
      responses:
        '200':
          description: Projects retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  projects:
                    type: array
                    items:
                      $ref: '#/components/schemas/Project'
    
    post:
      tags: [Admin, Projects]
      summary: Create project
      description: Create a new portfolio project
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProjectRequest'
      responses:
        '201':
          description: Project created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'

  /admin/contact:
    get:
      tags: [Admin, Contact]
      summary: Get contact submissions
      description: Retrieve contact form submissions
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - name: status
          in: query
          description: Filter by submission status
          schema:
            type: string
            enum: [unread, read, replied, archived]
      responses:
        '200':
          description: Contact submissions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactSubmissionsResponse'

  /admin/analytics:
    get:
      tags: [Admin, Analytics]
      summary: Get analytics overview
      description: Retrieve analytics dashboard data
      parameters:
        - name: period
          in: query
          description: Time period for analytics
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y]
            default: 30d
      responses:
        '200':
          description: Analytics data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsResponse'

  /admin/analytics/events:
    post:
      tags: [Admin, Analytics]
      summary: Track analytics event
      description: Record custom analytics event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnalyticsEventRequest'
      responses:
        '201':
          description: Event tracked successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  eventId:
                    type: string
                    format: uuid

  /admin/settings:
    get:
      tags: [Admin, Settings]
      summary: Get system settings
      description: Retrieve application configuration settings
      responses:
        '200':
          description: Settings retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SettingsResponse'
    
    put:
      tags: [Admin, Settings]
      summary: Update system settings
      description: Update application configuration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettingsRequest'
      responses:
        '200':
          description: Settings updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SettingsResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token in Authorization header
    
    CookieAuth:
      type: apiKey
      in: cookie
      name: auth-token
      description: HTTP-only authentication cookie

  parameters:
    PageParam:
      name: page
      in: query
      description: Page number for pagination
      schema:
        type: integer
        minimum: 1
        default: 1
    
    LimitParam:
      name: limit
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        maximum: 50
        default: 10

  schemas:
    # Authentication Schemas
    LoginRequest:
      type: object
      required: [email, password]
      properties:
        email:
          type: string
          format: email
          maxLength: 100
          example: <EMAIL>
        password:
          type: string
          minLength: 6
          maxLength: 128
          example: securePassword123!
        rememberMe:
          type: boolean
          default: false
          example: true

    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        user:
          $ref: '#/components/schemas/User'

    AuthStatusResponse:
      type: object
      properties:
        authenticated:
          type: boolean
          example: true
        user:
          $ref: '#/components/schemas/User'

    SetupRequest:
      type: object
      required: [username, email, password]
      properties:
        username:
          type: string
          minLength: 3
          maxLength: 50
          example: admin
        email:
          type: string
          format: email
          maxLength: 100
          example: <EMAIL>
        password:
          type: string
          minLength: 8
          maxLength: 128
          example: securePassword123!

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 550e8400-e29b-41d4-a716-************
        username:
          type: string
          example: admin
        email:
          type: string
          format: email
          example: <EMAIL>
        role:
          type: string
          enum: [admin, user]
          example: admin

    # Blog Schemas
    BlogPost:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
          maxLength: 200
        content:
          type: string
        excerpt:
          type: string
          maxLength: 500
        category:
          type: string
          maxLength: 100
        tags:
          type: array
          items:
            type: string
        readTime:
          type: integer
          minimum: 1
        status:
          type: string
          enum: [draft, published, archived]
        featuredImage:
          type: string
          format: uri
        metaTitle:
          type: string
          maxLength: 60
        metaDescription:
          type: string
          maxLength: 160
        publishedAt:
          type: string
          format: date-time
        slug:
          type: string
          maxLength: 200
        views:
          type: integer
          minimum: 0
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    BlogPostsResponse:
      type: object
      properties:
        posts:
          type: array
          items:
            $ref: '#/components/schemas/BlogPost'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'
        filters:
          type: object
          properties:
            category:
              type: string
              nullable: true
            search:
              type: string
              nullable: true

    AdminBlogPostsResponse:
      allOf:
        - $ref: '#/components/schemas/BlogPostsResponse'
        - type: object
          properties:
            stats:
              type: object
              properties:
                total:
                  type: integer
                published:
                  type: integer
                drafts:
                  type: integer
                archived:
                  type: integer

    CreateBlogPostRequest:
      type: object
      required: [title, content, excerpt, category, readTime, slug]
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 200
        content:
          type: string
          minLength: 1
        excerpt:
          type: string
          minLength: 1
          maxLength: 500
        category:
          type: string
          minLength: 1
          maxLength: 100
        tags:
          type: array
          items:
            type: string
            maxLength: 50
        readTime:
          type: integer
          minimum: 1
        status:
          type: string
          enum: [draft, published, archived]
          default: draft
        featuredImage:
          type: string
          format: uri
        metaTitle:
          type: string
          maxLength: 60
        metaDescription:
          type: string
          maxLength: 160
        slug:
          type: string
          minLength: 1
          maxLength: 200

    UpdateBlogPostRequest:
      allOf:
        - $ref: '#/components/schemas/CreateBlogPostRequest'
        - type: object
          properties:
            publishedAt:
              type: string
              format: date-time

    # Contact Schemas
    ContactRequest:
      type: object
      required: [firstName, lastName, email, subject, message]
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 50
          example: John
        lastName:
          type: string
          minLength: 1
          maxLength: 50
          example: Doe
        email:
          type: string
          format: email
          maxLength: 100
          example: <EMAIL>
        subject:
          type: string
          minLength: 1
          maxLength: 200
          example: Business Partnership Inquiry
        message:
          type: string
          minLength: 10
          maxLength: 5000
          example: We are interested in discussing potential collaboration opportunities...

    ContactResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Thank you for your message! I'll get back to you soon.
        id:
          type: string
          format: uuid

    Contact:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email
        subject:
          type: string
        message:
          type: string
        status:
          type: string
          enum: [unread, read, replied, archived]
        ipAddress:
          type: string
        userAgent:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ContactSubmissionsResponse:
      type: object
      properties:
        submissions:
          type: array
          items:
            $ref: '#/components/schemas/Contact'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    # Project Schemas
    Project:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        longDescription:
          type: string
        technologies:
          type: array
          items:
            type: string
        category:
          type: string
        status:
          type: string
          enum: [active, archived, draft]
        githubUrl:
          type: string
          format: uri
        demoUrl:
          type: string
          format: uri
        imageUrl:
          type: string
          format: uri
        images:
          type: array
          items:
            type: string
            format: uri
        featured:
          type: boolean
        priority:
          type: integer
        githubData:
          type: object
        views:
          type: integer
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateProjectRequest:
      type: object
      required: [title, description, technologies, category]
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 200
        description:
          type: string
          minLength: 1
        longDescription:
          type: string
        technologies:
          type: array
          minItems: 1
          items:
            type: string
        category:
          type: string
          minLength: 1
        status:
          type: string
          enum: [active, archived, draft]
          default: active
        githubUrl:
          type: string
          format: uri
        demoUrl:
          type: string
          format: uri
        imageUrl:
          type: string
          format: uri
        images:
          type: array
          items:
            type: string
            format: uri
        featured:
          type: boolean
          default: false
        priority:
          type: integer
          default: 0

    # GitHub Schemas
    GitHubRepository:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        full_name:
          type: string
        description:
          type: string
        html_url:
          type: string
          format: uri
        clone_url:
          type: string
          format: uri
        language:
          type: string
        stargazers_count:
          type: integer
        forks_count:
          type: integer
        watchers_count:
          type: integer
        size:
          type: integer
        default_branch:
          type: string
        open_issues_count:
          type: integer
        topics:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        pushed_at:
          type: string
          format: date-time

    GitHubStats:
      type: object
      properties:
        public_repos:
          type: integer
        public_gists:
          type: integer
        followers:
          type: integer
        following:
          type: integer
        total_stars:
          type: integer
        total_forks:
          type: integer
        languages:
          type: object
          additionalProperties:
            type: integer

    # Analytics Schemas
    AnalyticsEventRequest:
      type: object
      required: [event, path]
      properties:
        event:
          type: string
          maxLength: 100
        path:
          type: string
          maxLength: 500
        title:
          type: string
          maxLength: 200
        referrer:
          type: string
          maxLength: 500
        sessionId:
          type: string
          maxLength: 100

    AnalyticsResponse:
      type: object
      properties:
        pageViews:
          type: object
          properties:
            total:
              type: integer
            unique:
              type: integer
            change:
              type: number
        topPages:
          type: array
          items:
            type: object
            properties:
              path:
                type: string
              views:
                type: integer
              title:
                type: string
        referrers:
          type: array
          items:
            type: object
            properties:
              source:
                type: string
              visits:
                type: integer
        devices:
          type: object
          properties:
            desktop:
              type: integer
            mobile:
              type: integer
            tablet:
              type: integer
        countries:
          type: array
          items:
            type: object
            properties:
              country:
                type: string
              visits:
                type: integer

    # Dashboard Schemas
    DashboardResponse:
      type: object
      properties:
        stats:
          type: object
          properties:
            totalPosts:
              type: integer
            publishedPosts:
              type: integer
            totalProjects:
              type: integer
            contactSubmissions:
              type: integer
            pageViews:
              type: integer
        recentActivity:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [post, project, contact, view]
              title:
                type: string
              timestamp:
                type: string
                format: date-time
        performance:
          type: object
          properties:
            avgResponseTime:
              type: number
            uptime:
              type: number
            errorRate:
              type: number

    # Settings Schemas
    SettingsResponse:
      type: object
      properties:
        site:
          type: object
          properties:
            title:
              type: string
            description:
              type: string
            url:
              type: string
              format: uri
        features:
          type: object
          properties:
            blog:
              type: boolean
            playground:
              type: boolean
            analytics:
              type: boolean
            contactForm:
              type: boolean
        api:
          type: object
          properties:
            rateLimit:
              type: object
              properties:
                requests:
                  type: integer
                window:
                  type: integer
        email:
          type: object
          properties:
            enabled:
              type: boolean
            fromAddress:
              type: string
              format: email

    UpdateSettingsRequest:
      type: object
      properties:
        site:
          type: object
          properties:
            title:
              type: string
              maxLength: 100
            description:
              type: string
              maxLength: 500
            url:
              type: string
              format: uri
        features:
          type: object
          properties:
            blog:
              type: boolean
            playground:
              type: boolean
            analytics:
              type: boolean
            contactForm:
              type: boolean

    # Utility Schemas
    PaginationInfo:
      type: object
      properties:
        current:
          type: integer
          minimum: 1
        total:
          type: integer
          minimum: 0
        hasNext:
          type: boolean
        hasPrev:
          type: boolean
        totalPosts:
          type: integer
          minimum: 0

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          example: An error occurred
        details:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string
        code:
          type: string
          example: VALIDATION_ERROR

  responses:
    BadRequest:
      description: Bad request - Invalid input data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: Validation failed
            details:
              - field: email
                message: Invalid email address
              - field: password
                message: Password must be at least 6 characters

    Unauthorized:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: Authentication required
            code: UNAUTHORIZED

    Forbidden:
      description: Forbidden - Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: Insufficient permissions
            code: FORBIDDEN

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: Resource not found
            code: NOT_FOUND

    RateLimited:
      description: Rate limit exceeded
      headers:
        Retry-After:
          description: Number of seconds to wait before retrying
          schema:
            type: integer
            example: 900
        X-RateLimit-Limit:
          description: Request limit per window
          schema:
            type: integer
            example: 5
        X-RateLimit-Remaining:
          description: Requests remaining in current window
          schema:
            type: integer
            example: 0
        X-RateLimit-Reset:
          description: Time when rate limit window resets
          schema:
            type: string
            format: date-time
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: Too many requests. Please try again later.
            code: RATE_LIMITED

    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: Internal server error
            code: INTERNAL_ERROR

tags:
  - name: Authentication
    description: User authentication and session management
  - name: Blog
    description: Blog posts and content management
  - name: Contact
    description: Contact form submissions
  - name: GitHub
    description: GitHub integration and repository data
  - name: Admin
    description: Administrative functions (requires authentication)
  - name: Projects
    description: Portfolio project management
  - name: Analytics
    description: Analytics and tracking
  - name: Settings
    description: System configuration and settings