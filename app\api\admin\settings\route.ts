import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { AuthService } from '@/lib/auth';

export const GET = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const settings = await DatabaseService.getAllSettings();
    
    // Convert to key-value object for easier frontend consumption
    const settingsObject = settings.reduce((acc, setting) => {
      let value = setting.value;
      
      // Parse based on type
      switch (setting.type) {
        case 'boolean':
          value = value === 'true';
          break;
        case 'number':
          value = parseFloat(value);
          break;
        case 'json':
          try {
            value = JSON.parse(value);
          } catch (e) {
            console.warn(`Failed to parse JSON setting ${setting.key}:`, e);
          }
          break;
      }
      
      acc[setting.key] = {
        value,
        type: setting.type,
        description: setting.description
      };
      
      return acc;
    }, {} as any);
    
    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
});

export const PUT = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const { key, value, type = 'string' } = body;
    
    if (!key || value === undefined) {
      return NextResponse.json(
        { error: 'Key and value are required' },
        { status: 400 }
      );
    }
    
    // Convert value to string for storage
    let stringValue = value;
    if (type === 'boolean') {
      stringValue = value ? 'true' : 'false';
    } else if (type === 'number') {
      stringValue = value.toString();
    } else if (type === 'json') {
      stringValue = JSON.stringify(value);
    }
    
    const setting = await DatabaseService.updateSetting(key, stringValue, type);
    
    return NextResponse.json(setting);
  } catch (error) {
    console.error('Error updating setting:', error);
    return NextResponse.json(
      { error: 'Failed to update setting' },
      { status: 500 }
    );
  }
});

export const POST = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const updates = body.settings || [];
    
    if (!Array.isArray(updates)) {
      return NextResponse.json(
        { error: 'Settings must be an array' },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const update of updates) {
      const { key, value, type = 'string' } = update;
      
      if (!key || value === undefined) {
        continue;
      }
      
      // Convert value to string for storage
      let stringValue = value;
      if (type === 'boolean') {
        stringValue = value ? 'true' : 'false';
      } else if (type === 'number') {
        stringValue = value.toString();
      } else if (type === 'json') {
        stringValue = JSON.stringify(value);
      }
      
      const setting = await DatabaseService.updateSetting(key, stringValue, type);
      results.push(setting);
    }
    
    return NextResponse.json({
      success: true,
      updated: results.length,
      settings: results
    });
  } catch (error) {
    console.error('Error bulk updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
});
