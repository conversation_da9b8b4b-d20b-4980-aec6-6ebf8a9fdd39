import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { ProjectCard3D } from "./project-card-3d";

interface GitHubStats {
  repos: number;
  followers: number;
  stars: number;
  commits: number;
}

interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  githubUrl?: string;
  demoUrl?: string;
  imageUrl?: string;
  stars?: number;
  forks?: number;
  language?: string;
  updated_at?: string;
}

export function ProjectsSection() {
  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ['/api/github/repos'],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: githubStats, isLoading: statsLoading } = useQuery<GitHubStats>({
    queryKey: ['/api/github/stats'],
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section id="projects" className="py-20 bg-gray-50 dark:bg-gray-900 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">Featured Projects</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Showcasing innovative solutions that blend data science, AI, and software engineering
          </p>
        </motion.div>
        
        {projectsLoading ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {[1, 2, 3].map((i) => (
              <div key={i} className="glass-effect overflow-hidden rounded-2xl p-6">
                <div className="w-full h-48 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-xl animate-pulse mb-4" />
                <div className="h-6 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded mb-4 animate-pulse" />
                <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded mb-2 animate-pulse" />
                <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded mb-4 animate-pulse" />
                <div className="flex gap-2 mb-4">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="h-6 w-16 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-full animate-pulse" />
                  ))}
                </div>
                <div className="h-10 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-xl animate-pulse" />
              </div>
            ))}
          </div>
        ) : (
          <motion.div 
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {projects?.map((project, index) => (
              <ProjectCard3D key={project.id} project={project} index={index} />
            ))}
          </motion.div>
        )}
        
        {/* GitHub Stats */}
        <motion.div 
          className="glass-effect rounded-2xl p-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold mb-6 gradient-text">GitHub Activity</h3>
          {statsLoading ? (
            <div className="grid md:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="p-4">
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse" />
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                </div>
              ))}
            </div>
          ) : (
            <div className="grid md:grid-cols-4 gap-6">
              <motion.div 
                className="p-4"
                whileHover={{ scale: 1.05 }}
                data-testid="github-repos"
              >
                <div className="text-3xl font-bold text-primary mb-2">{githubStats?.repos}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Repositories</div>
              </motion.div>
              <motion.div 
                className="p-4"
                whileHover={{ scale: 1.05 }}
                data-testid="github-commits"
              >
                <div className="text-3xl font-bold text-secondary mb-2">{githubStats?.commits}k</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Commits</div>
              </motion.div>
              <motion.div 
                className="p-4"
                whileHover={{ scale: 1.05 }}
                data-testid="github-stars"
              >
                <div className="text-3xl font-bold text-accent mb-2">{githubStats?.stars}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Stars Received</div>
              </motion.div>
              <motion.div 
                className="p-4"
                whileHover={{ scale: 1.05 }}
                data-testid="github-followers"
              >
                <div className="text-3xl font-bold text-primary mb-2">{githubStats?.followers}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Followers</div>
              </motion.div>
            </div>
          )}
        </motion.div>
      </div>
    </section>
  );
}
