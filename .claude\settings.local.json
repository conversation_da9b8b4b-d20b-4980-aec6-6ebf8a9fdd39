{"permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run check)", "Bash(npm run build)", "Bash(npm install)", "Bash(npm install --legacy-peer-deps)", "Bash(npm install --force)", "Bash(npx next build)", "Bash(rm -rf node_modules package-lock.json)", "Bash(npm install --no-package-lock --legacy-peer-deps)", "Bash(npm --version)", "Bash(node --version)", "Bash(npm cache clean --force)", "Bash(/dev/null)", "Bash(npm install next@14.2.15 react@18.3.1 react-dom@18.3.1 typescript@5.6.3 --save)", "Bash(npm install framer-motion lucide-react --save)", "Bash(npx next dev --turbopack)", "Bash(yarn dev)", "Bash(pkill -f \"tsx.*server\")", "Bash(pkill -f \"next.*dev\")", "Bash(npm run dev:server)", "Bash(PORT=5000 npm run dev:server)", "Bash(grep -n \"export.*storage\" /mnt/c/Users/<USER>/getintheq.space/server/storage.ts)", "Bash(pkill -f \"next.*3001\")", "<PERSON><PERSON>(curl -s http://localhost:5000/api/blog)", "<PERSON><PERSON>(curl -s -I http://localhost:3001)", "Bash(pkill -f \"node.*3001\")", "Bash(pkill -f \"node.*5000\")", "Bash(npm install critters)", "<PERSON><PERSON>(rm -rf .next)", "Bash(pkill -f \"yarn dev\")", "Bash(rm -rf node_modules/.cache)"], "deny": [], "ask": []}}