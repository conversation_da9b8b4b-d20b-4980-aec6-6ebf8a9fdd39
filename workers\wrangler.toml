name = "portfolio-api"
main = "src/index.ts"
compatibility_date = "2024-01-15"
compatibility_flags = ["nodejs_compat"]
account_id = "5adf62efd6cf179a8939c211b155e229"

# D1 Database configuration
[[d1_databases]]
binding = "DB"
database_name = "portfolio-db"
database_id = "21f2c71f-2ecc-49fd-b7d9-19ca1dda18f8"
migrations_dir = "./migrations"

# Custom domain configuration
[env.production]
name = "portfolio-api-production"
route = { pattern = "api.getintheq.space/*", zone_name = "getintheq.space" }

# D1 Database for production
[[env.production.d1_databases]]
binding = "DB"
database_name = "portfolio-db-prod"
database_id = "28c68b5a-680e-4855-99ab-b6d39031bcfb"
migrations_dir = "./migrations"

[env.staging]
name = "portfolio-api-staging"

# D1 Database for staging
[[env.staging.d1_databases]]
binding = "DB"
database_name = "portfolio-db-staging"
database_id = "3f5da5f3-c5ac-4825-8f8b-5299f2602734"
migrations_dir = "./migrations"

# Environment variables will be set via CLI
# wrangler secret put GITHUB_TOKEN
# wrangler secret put GITHUB_USERNAME
# wrangler secret put RESEND_API_KEY
# wrangler secret put CONTACT_EMAIL

# KV storage for caching (optional)
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# Rate limiting using Durable Objects
# [[durable_objects.bindings]]
# name = "RATE_LIMITER"
# class_name = "RateLimiter"

# [[migrations]]
# tag = "v1"
# new_classes = ["RateLimiter"]