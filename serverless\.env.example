# Environment Variables for Serverless Portfolio API

# GitHub Configuration
GITHUB_TOKEN=ghp_your_github_token_here
GITHUB_USERNAME=khiwniti

# Email Service (Resend)
RESEND_API_KEY=re_your_resend_api_key_here
CONTACT_EMAIL=<EMAIL># Alternative Email Services
# SendGrid
# SENDGRID_API_KEY=SG.your_sendgrid_api_key_here

# AWS SES (if using AWS)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1

# Database (if needed)
# DATABASE_URL=postgresql://user:password@host:port/database

# Security
JWT_SECRET=your_jwt_secret_here_very_long_random_string
ALLOWED_ORIGINS=http://localhost:3000,https://getintheq.space

# Deployment Environment
ENVIRONMENT=development

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Logging
LOG_LEVEL=INFO