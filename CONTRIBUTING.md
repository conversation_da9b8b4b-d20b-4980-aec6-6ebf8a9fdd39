# 🤝 Contributing to getintheq.space

Thank you for your interest in contributing to this project! This guide will help you get started with the development workflow and coding standards.

## 📋 Table of Contents

- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Git Workflow](#git-workflow)
- [Testing Guidelines](#testing-guidelines)
- [Code Review Process](#code-review-process)
- [Troubleshooting](#troubleshooting)

## 🚀 Getting Started

### Prerequisites

- **Node.js**: 18.0.0 or higher
- **npm**: 9.0.0 or higher
- **Git**: Latest version
- **PostgreSQL**: For database (or Neon account)

### Initial Setup

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/yourusername/getintheq.space.git
   cd getintheq.space
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Set up the database**
   ```bash
   npm run db:push
   ```

5. **Install Git hooks**
   ```bash
   npm run prepare
   ```

6. **Start development server**
   ```bash
   npm run dev
   ```

## 💻 Development Workflow

### Daily Development

1. **Pull latest changes**
   ```bash
   git checkout main
   git pull origin main
   ```

2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes**
   - Write code following our [coding standards](#coding-standards)
   - Add tests for new functionality
   - Update documentation if needed

4. **Run quality checks**
   ```bash
   npm run validate  # Runs type-check, lint, and format checks
   npm run test      # Run tests
   ```

5. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```
   > Pre-commit hooks will automatically run linting and formatting

6. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

### Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server |
| `npm run build` | Build for production |
| `npm run start` | Start production server |
| `npm run lint` | Run ESLint with auto-fix |
| `npm run lint:check` | Check linting without fixing |
| `npm run format` | Format code with Prettier |
| `npm run format:check` | Check formatting without fixing |
| `npm run type-check` | Run TypeScript type checking |
| `npm run validate` | Run all quality checks |
| `npm run test` | Run all tests |
| `npm run test:watch` | Run tests in watch mode |
| `npm run test:coverage` | Run tests with coverage report |
| `npm run db:push` | Push database schema changes |
| `npm run db:studio` | Open Drizzle Studio |

## 📝 Coding Standards

### TypeScript Guidelines

- **Strict Mode**: Always use strict TypeScript
- **No `any`**: Avoid `any` types, use proper typing
- **Interfaces**: Prefer interfaces over type aliases for object shapes
- **Type Imports**: Use `import type` for type-only imports

```typescript
// ✅ Good
import type { User } from '@shared/schema';
import { validateUser } from '@/lib/validation';

interface UserProps {
  user: User;
  onUpdate: (user: User) => void;
}

// ❌ Bad
import { User } from '@shared/schema'; // Should be type import
const userProps: any = { user, onUpdate }; // Avoid any
```

### React Component Guidelines

- **Use forwardRef**: For reusable UI components
- **Display names**: Always set displayName for debugging
- **Props interfaces**: Define clear prop interfaces
- **Event handlers**: Use proper event typing

```typescript
// ✅ Good
import type { ButtonHTMLAttributes } from 'react';
import { forwardRef } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';
```

### Import Organization

Follow this import order:

```typescript
// 1. React and Next.js
import React from 'react';
import { NextResponse } from 'next/server';

// 2. External libraries
import { z } from 'zod';
import { eq } from 'drizzle-orm';

// 3. Internal utilities and libs
import { cn } from '@/lib/utils';
import { db } from '@/lib/database';

// 4. Components
import { Button } from '@/components/ui/button';
import { UserCard } from '@/components/user-card';

// 5. Types (with type keyword)
import type { User } from '@shared/schema';
import type { ComponentProps } from 'react';
```

### File Naming Conventions

- **Files**: kebab-case (`user-profile.tsx`)
- **Components**: PascalCase (`UserProfile.tsx`)
- **Utilities**: kebab-case (`api-client.ts`)
- **Constants**: UPPER_SNAKE_CASE (`API_ENDPOINTS`)
- **Types**: PascalCase (`UserProfile`)

### Database and API Guidelines

- **Use Drizzle ORM**: Avoid raw SQL when possible
- **Validate inputs**: Use Zod schemas for all API inputs
- **Error handling**: Proper try/catch with meaningful errors
- **Type safety**: Infer types from schemas

```typescript
// ✅ Good API route
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { users, insertUserSchema } from '@shared/schema';

const createUserSchema = insertUserSchema.pick({
  username: true,
  email: true,
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createUserSchema.parse(body);
    
    const user = await db.insert(users).values(validatedData).returning();
    
    return NextResponse.json({ user: user[0] });
  } catch (error) {
    console.error('Failed to create user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
```

## 🌳 Git Workflow

### Branch Naming

- **Features**: `feature/feature-name`
- **Bug fixes**: `fix/bug-description`
- **Hotfixes**: `hotfix/critical-issue`
- **Documentation**: `docs/update-readme`
- **Refactoring**: `refactor/component-structure`

### Commit Messages

Follow [Conventional Commits](https://www.conventionalcommits.org/):

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

```bash
# Examples
git commit -m "feat: add user authentication system"
git commit -m "fix: resolve database connection timeout"
git commit -m "docs: update API documentation"
git commit -m "test: add unit tests for user service"
```

### Pull Request Guidelines

1. **Title**: Clear, descriptive title
2. **Description**: Explain what and why
3. **Testing**: Describe how to test changes
4. **Screenshots**: For UI changes
5. **Breaking changes**: Clearly mark any breaking changes

**PR Template:**
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots here

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No console.log statements
```

## 🧪 Testing Guidelines

### Writing Tests

- **Test files**: Place next to component (`button.test.tsx`)
- **Test utilities**: Use shared test utilities
- **Coverage**: Aim for 80%+ coverage on business logic
- **Mock external dependencies**: API calls, external libraries

### Test Structure

```typescript
// ✅ Good test structure
describe('Button Component', () => {
  it('should render with default props', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('should handle click events', async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should apply variant styles correctly', () => {
    render(<Button variant="secondary">Secondary</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-secondary');
  });
});
```

### Running Tests

```bash
# Run all tests
npm run test

# Watch mode during development
npm run test:watch

# Coverage report
npm run test:coverage

# Run specific test file
npm run test -- button.test.tsx
```

## 👀 Code Review Process

### For Reviewers

- **Functionality**: Does it work as intended?
- **Code quality**: Follows standards and best practices?
- **Performance**: Any performance implications?
- **Security**: Any security concerns?
- **Tests**: Adequate test coverage?
- **Documentation**: Updated if needed?

### For Authors

- **Self-review**: Review your own code first
- **Small PRs**: Keep changes focused and small
- **Clear description**: Explain what and why
- **Address feedback**: Respond to all review comments
- **Update documentation**: Keep docs up to date

## 🔧 Troubleshooting

### Common Issues

**ESLint/Prettier conflicts:**
```bash
npm run lint  # Auto-fix ESLint issues
npm run format  # Auto-fix Prettier issues
```

**Type errors:**
```bash
npm run type-check  # Check TypeScript errors
```

**Test failures:**
```bash
npm run test:watch  # Debug tests in watch mode
```

**Database issues:**
```bash
npm run db:push  # Reset database schema
npm run db:studio  # Open database studio
```

### Getting Help

- **GitHub Issues**: Report bugs and request features
- **Discussions**: Ask questions and share ideas
- **Documentation**: Check the README and wiki
- **Code Review**: Ask for help in PR comments

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Drizzle ORM Documentation](https://orm.drizzle.team/)
- [shadcn/ui Components](https://ui.shadcn.com/)

## 🎉 Thank You!

Your contributions make this project better for everyone. We appreciate your time and effort in following these guidelines and helping maintain high code quality.

---

**Questions?** Feel free to open an issue or start a discussion!