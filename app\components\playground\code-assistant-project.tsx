'use client'

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Code, Play, Lightbulb, Bug, Zap, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface CodeAssistantProjectProps {
  onBack: () => void;
  projectData: any;
}

const languages = [
  { id: 'javascript', name: 'JavaScript', color: 'bg-yellow-500' },
  { id: 'python', name: 'Python', color: 'bg-blue-500' },
  { id: 'typescript', name: 'TypeScript', color: 'bg-blue-600' },
  { id: 'react', name: 'React', color: 'bg-cyan-500' },
  { id: 'java', name: 'Java', color: 'bg-orange-500' },
  { id: 'cpp', name: 'C++', color: 'bg-purple-500' }
];

const assistantModes = [
  { id: 'explain', name: 'Explain Code', icon: Lightbulb, description: 'Get detailed explanations of code functionality' },
  { id: 'optimize', name: 'Optimize', icon: Zap, description: 'Improve performance and efficiency' },
  { id: 'debug', name: 'Debug', icon: Bug, description: 'Find and fix issues in your code' },
  { id: 'generate', name: 'Generate', icon: Code, description: 'Create new code from descriptions' }
];

export function CodeAssistantProject({ onBack, projectData }: CodeAssistantProjectProps) {
  const [inputCode, setInputCode] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('javascript');
  const [selectedMode, setSelectedMode] = useState('explain');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);

  const processCode = async () => {
    if (!inputCode.trim()) return;
    
    setIsProcessing(true);
    
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const mockResults = {
      explain: {
        explanation: "This code demonstrates a modern JavaScript function that implements a binary search algorithm. The function takes a sorted array and a target value, then efficiently searches for the target using the divide-and-conquer approach.",
        breakdown: [
          { line: "1-2", description: "Function declaration with parameters for array and target value" },
          { line: "3-4", description: "Initialize left and right pointers for binary search bounds" },
          { line: "5-12", description: "Main search loop that continues until bounds meet" },
          { line: "6-7", description: "Calculate middle index to divide search space" },
          { line: "8-11", description: "Compare middle element with target and adjust bounds" }
        ],
        complexity: "Time: O(log n), Space: O(1)"
      },
      optimize: {
        original: inputCode,
        optimized: `// Optimized version with early returns and better variable naming
function binarySearch(sortedArray, target) {
  let left = 0;
  let right = sortedArray.length - 1;
  
  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const midValue = sortedArray[mid];
    
    if (midValue === target) return mid;
    if (midValue < target) left = mid + 1;
    else right = mid - 1;
  }
  
  return -1;
}`,
        improvements: [
          "Added early return for found element",
          "Improved variable naming for clarity",
          "Reduced redundant comparisons",
          "Added proper JSDoc comments"
        ]
      },
      debug: {
        issues: [
          { type: "Logic Error", line: 8, description: "Missing return statement when element is found", severity: "high" },
          { type: "Edge Case", line: 3, description: "No validation for empty array", severity: "medium" },
          { type: "Performance", line: 6, description: "Inefficient middle calculation", severity: "low" }
        ],
        fixes: [
          "Add return statement: return mid;",
          "Add array length validation",
          "Use bit shift for faster division: (left + right) >>> 1"
        ]
      },
      generate: {
        code: `// Generated: Binary Search Implementation
function binarySearch(arr, target) {
  /**
   * Performs binary search on a sorted array
   * @param {number[]} arr - Sorted array to search
   * @param {number} target - Value to find
   * @returns {number} Index of target or -1 if not found
   */
  
  if (!arr || arr.length === 0) return -1;
  
  let left = 0;
  let right = arr.length - 1;
  
  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    
    if (arr[mid] === target) {
      return mid;
    } else if (arr[mid] < target) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }
  
  return -1;
}

// Example usage:
const numbers = [1, 3, 5, 7, 9, 11, 13];
const index = binarySearch(numbers, 7);
console.log(index); // Output: 3`,
        features: [
          "Complete error handling",
          "JSDoc documentation",
          "Example usage included",
          "Optimized performance"
        ]
      }
    };
    
    setResult(mockResults[selectedMode as keyof typeof mockResults]);
    setIsProcessing(false);
  };

  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900 dark:via-emerald-900 dark:to-teal-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Playground
          </Button>
          
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
              <Code className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold gradient-text">AI Code Assistant</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Get intelligent help with code explanation, optimization, and debugging
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="w-5 h-5" />
                  Code Input
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Assistant Mode */}
                <div>
                  <label className="text-sm font-medium mb-3 block">Assistant Mode</label>
                  <div className="grid grid-cols-2 gap-2">
                    {assistantModes.map(mode => {
                      const Icon = mode.icon;
                      return (
                        <button
                          key={mode.id}
                          onClick={() => setSelectedMode(mode.id)}
                          className={`p-3 rounded-lg border-2 transition-all ${
                            selectedMode === mode.id
                              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-green-300'
                          }`}
                        >
                          <Icon className="w-5 h-5 mx-auto mb-1" />
                          <div className="text-xs font-medium">{mode.name}</div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Language Selection */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Programming Language</label>
                  <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map(lang => (
                        <SelectItem key={lang.id} value={lang.id}>
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${lang.color}`} />
                            {lang.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Code Input */}
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    {selectedMode === 'generate' ? 'Describe what you want to build' : 'Paste your code'}
                  </label>
                  <Textarea
                    placeholder={selectedMode === 'generate' 
                      ? "Describe the function or algorithm you want to create..."
                      : "Paste your code here for analysis..."
                    }
                    value={inputCode}
                    onChange={(e) => setInputCode(e.target.value)}
                    className="min-h-[200px] font-mono text-sm"
                  />
                </div>

                {/* Process Button */}
                <Button
                  onClick={processCode}
                  disabled={!inputCode.trim() || isProcessing}
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                >
                  {isProcessing ? (
                    <>
                      <Play className="w-4 h-4 mr-2 animate-pulse" />
                      Processing...
                    </>
                  ) : (
                    <>
                      {(() => {
                        const mode = assistantModes.find(m => m.id === selectedMode);
                        if (mode) {
                          const Icon = mode.icon;
                          return <Icon className="w-4 h-4 mr-2" />;
                        }
                        return null;
                      })()}
                      {assistantModes.find(m => m.id === selectedMode)?.name}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Results Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="glass-effect h-full">
              <CardHeader>
                <CardTitle>AI Analysis Results</CardTitle>
              </CardHeader>
              <CardContent>
                {result ? (
                  <Tabs defaultValue="main" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="main">Analysis</TabsTrigger>
                      <TabsTrigger value="details">Details</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="main" className="space-y-4">
                      {selectedMode === 'explain' && (
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium mb-2">Explanation</h4>
                            <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                              {result.explanation}
                            </p>
                          </div>
                          <div>
                            <h4 className="font-medium mb-2">Complexity</h4>
                            <Badge variant="outline">{result.complexity}</Badge>
                          </div>
                        </div>
                      )}
                      
                      {selectedMode === 'optimize' && (
                        <div className="space-y-4">
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">Optimized Code</h4>
                              <Button variant="outline" size="sm" onClick={() => copyCode(result.optimized)}>
                                <Copy className="w-4 h-4" />
                              </Button>
                            </div>
                            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-xs overflow-x-auto">
                              <code>{result.optimized}</code>
                            </pre>
                          </div>
                        </div>
                      )}
                      
                      {selectedMode === 'debug' && (
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium mb-2">Issues Found</h4>
                            <div className="space-y-2">
                              {result.issues.map((issue: any, index: number) => (
                                <div key={index} className="p-3 border rounded-lg">
                                  <div className="flex items-center justify-between mb-1">
                                    <span className="font-medium text-sm">{issue.type}</span>
                                    <Badge variant={issue.severity === 'high' ? 'destructive' : 'secondary'}>
                                      {issue.severity}
                                    </Badge>
                                  </div>
                                  <p className="text-xs text-gray-600 dark:text-gray-400">
                                    Line {issue.line}: {issue.description}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {selectedMode === 'generate' && (
                        <div className="space-y-4">
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">Generated Code</h4>
                              <Button variant="outline" size="sm" onClick={() => copyCode(result.code)}>
                                <Copy className="w-4 h-4" />
                              </Button>
                            </div>
                            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-xs overflow-x-auto max-h-64">
                              <code>{result.code}</code>
                            </pre>
                          </div>
                        </div>
                      )}
                    </TabsContent>
                    
                    <TabsContent value="details" className="space-y-4">
                      {selectedMode === 'explain' && result.breakdown && (
                        <div>
                          <h4 className="font-medium mb-2">Line-by-Line Breakdown</h4>
                          <div className="space-y-2">
                            {result.breakdown.map((item: any, index: number) => (
                              <div key={index} className="p-2 border rounded text-sm">
                                <div className="font-medium">Lines {item.line}</div>
                                <div className="text-gray-600 dark:text-gray-400">{item.description}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {selectedMode === 'optimize' && result.improvements && (
                        <div>
                          <h4 className="font-medium mb-2">Improvements Made</h4>
                          <ul className="space-y-1">
                            {result.improvements.map((improvement: string, index: number) => (
                              <li key={index} className="text-sm flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                                {improvement}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {selectedMode === 'debug' && result.fixes && (
                        <div>
                          <h4 className="font-medium mb-2">Suggested Fixes</h4>
                          <ul className="space-y-1">
                            {result.fixes.map((fix: string, index: number) => (
                              <li key={index} className="text-sm flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                                {fix}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {selectedMode === 'generate' && result.features && (
                        <div>
                          <h4 className="font-medium mb-2">Features Included</h4>
                          <ul className="space-y-1">
                            {result.features.map((feature: string, index: number) => (
                              <li key={index} className="text-sm flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                    <div className="text-center">
                      <Code className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>Enter your code to see AI analysis</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
