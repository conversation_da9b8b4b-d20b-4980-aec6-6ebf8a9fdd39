'use client'

import { useState, useEffect } from "react";

import { useTheme } from "next-themes";
import Link from "next/link";

export function Navbar() {
  const { theme, setTheme } = useTheme();
  
  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setIsMobileMenuOpen(false);
    }
  };

  return (
    <nav 
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled 
          ? 'glass-effect bg-white/90 dark:bg-black/90 backdrop-blur-xl' 
          : 'glass-effect bg-white/10 dark:bg-black/30 backdrop-blur-lg'
      }`}
      data-testid="navbar"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex-shrink-0">
            <Link href="/" className="text-2xl font-bold gradient-text" data-testid="logo-button">
              KN
            </Link>
          </div>
          
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <button 
                onClick={() => scrollToSection('home')} 
                className="hover:text-accent transition-colors duration-300"
                data-testid="nav-home"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="hover:text-accent transition-colors duration-300"
                data-testid="nav-about"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection('experience')}
                className="hover:text-accent transition-colors duration-300"
                data-testid="nav-experience"
              >
                Experience
              </button>
              <button
                onClick={() => scrollToSection('projects')}
                className="hover:text-accent transition-colors duration-300"
                data-testid="nav-projects"
              >
                Projects
              </button>
              <Link
                href="/playground"
                className="hover:text-accent transition-colors duration-300 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium"
                data-testid="nav-playground"
              >
                AI Playground
              </Link>
              <Link
                href="/blog"
                className="hover:text-accent transition-colors duration-300"
                data-testid="nav-blog"
              >
                Blog
              </Link>
              <Link 
                href="/resume"
                className="hover:text-accent transition-colors duration-300"
                data-testid="nav-resume"
              >
                Resume
              </Link>
              <button 
                onClick={() => scrollToSection('contact')} 
                className="hover:text-accent transition-colors duration-300"
                data-testid="nav-contact"
              >
                Contact
              </button>
              <Link
                href="/admin"
                className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-1 rounded-full hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
                data-testid="nav-admin"
                title="Admin Panel"
              >
                Admin
              </Link>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button 
              onClick={toggleTheme}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300"
              data-testid="theme-toggle"
            >
              {theme === 'light' ? (
                <i className="fas fa-moon text-lg" />
              ) : (
                <i className="fas fa-sun text-lg text-yellow-400" />
              )}
            </button>
            
            <button 
              className="md:hidden p-2"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              data-testid="mobile-menu-toggle"
            >
              <i className="fas fa-bars text-lg" />
            </button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden absolute top-full left-0 w-full glass-effect bg-white/95 dark:bg-black/95 backdrop-blur-xl border-t border-white/20 dark:border-white/10">
            <div className="px-4 py-4 space-y-4">
              <button 
                onClick={() => scrollToSection('home')} 
                className="block w-full text-left hover:text-accent transition-colors duration-300"
                data-testid="mobile-nav-home"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="block w-full text-left hover:text-accent transition-colors duration-300"
                data-testid="mobile-nav-about"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection('experience')}
                className="block w-full text-left hover:text-accent transition-colors duration-300"
                data-testid="mobile-nav-experience"
              >
                Experience
              </button>
              <button
                onClick={() => scrollToSection('projects')}
                className="block w-full text-left hover:text-accent transition-colors duration-300"
                data-testid="mobile-nav-projects"
              >
                Projects
              </button>
              <Link
                href="/playground"
                className="block w-full text-left bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-2 rounded-lg font-medium"
                data-testid="mobile-nav-playground"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                🤖 AI Playground
              </Link>
              <Link
                href="/blog"
                className="block w-full text-left hover:text-accent transition-colors duration-300"
                data-testid="mobile-nav-blog"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Blog
              </Link>
              <Link 
                href="/resume"
                className="block w-full text-left hover:text-accent transition-colors duration-300"
                data-testid="mobile-nav-resume"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Resume
              </Link>
              <button 
                onClick={() => scrollToSection('contact')} 
                className="block w-full text-left hover:text-accent transition-colors duration-300"
                data-testid="mobile-nav-contact"
              >
                Contact
              </button>
              <Link
                href="/admin"
                className="block w-full text-left bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-2 rounded-lg font-medium"
                data-testid="mobile-nav-admin"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                ⚙️ Admin Panel
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
