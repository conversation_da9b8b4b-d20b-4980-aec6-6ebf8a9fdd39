#!/bin/bash

# Deployment script for api.getintheq.space with D1 database
# This script helps automate the deployment process

set -e

echo "🚀 Deploying Portfolio API to api.getintheq.space"
echo "=================================================="

# Check if we're in the workers directory
if [ ! -f "wrangler.toml" ]; then
    echo "❌ Error: Please run this script from the workers directory"
    exit 1
fi

# Check if CLOUDFLARE_API_TOKEN is set
if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
    echo "❌ Error: CLOUDFLARE_API_TOKEN environment variable is not set"
    echo "Please get your API token from: https://developers.cloudflare.com/fundamentals/api/get-started/create-token/"
    echo "Then set it with: export CLOUDFLARE_API_TOKEN=your_token_here"
    exit 1
fi

echo "✅ Environment check passed"

# Create D1 databases if they don't exist
echo "🗄️  Setting up D1 databases..."

echo "Creating development database..."
DEV_DB_OUTPUT=$(npx wrangler d1 create portfolio-db 2>/dev/null || echo "Database may already exist")
if [[ $DEV_DB_OUTPUT == *"database_id"* ]]; then
    DEV_DB_ID=$(echo "$DEV_DB_OUTPUT" | grep "database_id" | cut -d'"' -f4)
    echo "Development database ID: $DEV_DB_ID"
fi

echo "Creating staging database..."
STAGING_DB_OUTPUT=$(npx wrangler d1 create portfolio-db-staging 2>/dev/null || echo "Database may already exist")
if [[ $STAGING_DB_OUTPUT == *"database_id"* ]]; then
    STAGING_DB_ID=$(echo "$STAGING_DB_OUTPUT" | grep "database_id" | cut -d'"' -f4)
    echo "Staging database ID: $STAGING_DB_ID"
fi

echo "Creating production database..."
PROD_DB_OUTPUT=$(npx wrangler d1 create portfolio-db-prod 2>/dev/null || echo "Database may already exist")
if [[ $PROD_DB_OUTPUT == *"database_id"* ]]; then
    PROD_DB_ID=$(echo "$PROD_DB_OUTPUT" | grep "database_id" | cut -d'"' -f4)
    echo "Production database ID: $PROD_DB_ID"
fi

echo "📝 Please update wrangler.toml with the database IDs shown above"
echo "   Replace YOUR_DATABASE_ID_HERE with: $DEV_DB_ID"
echo "   Replace YOUR_STAGING_DATABASE_ID_HERE with: $STAGING_DB_ID"  
echo "   Replace YOUR_PROD_DATABASE_ID_HERE with: $PROD_DB_ID"
echo ""

# Run migrations
echo "🔄 Running database migrations..."
if [ -f "migrations/0001_initial.sql" ]; then
    echo "Running migrations on development database..."
    npx wrangler d1 migrations apply portfolio-db --local
    
    echo "Running migrations on staging database..."
    npx wrangler d1 migrations apply portfolio-db-staging --env staging
    
    echo "Running migrations on production database..."
    npx wrangler d1 migrations apply portfolio-db-prod --env production
else
    echo "⚠️  No migration files found. Skipping migrations."
fi

# Set up secrets (interactive)
echo "🔐 Setting up secrets..."
echo "Please set the following secrets when prompted:"
echo "- GITHUB_TOKEN: Your GitHub personal access token"
echo "- GITHUB_USERNAME: Your GitHub username"
echo "- RESEND_API_KEY: Your Resend API key for email"
echo "- CONTACT_EMAIL: Email address to receive contact form submissions"

read -p "Do you want to set up secrets now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    npx wrangler secret put GITHUB_TOKEN --env production
    npx wrangler secret put GITHUB_USERNAME --env production
    npx wrangler secret put RESEND_API_KEY --env production
    npx wrangler secret put CONTACT_EMAIL --env production
fi

# Deploy
echo "🚀 Deploying to production..."
npm run deploy:production

echo "✅ Deployment complete!"
echo "Your API should be available at: https://api.getintheq.space"
echo ""
echo "Next steps:"
echo "1. Test your API endpoints"
echo "2. Update your frontend to use the new API URL"
echo "3. Set up monitoring and alerts"