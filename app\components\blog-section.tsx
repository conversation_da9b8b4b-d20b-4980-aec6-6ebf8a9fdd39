'use client'

import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { APIClient } from "@/lib/api-client";
import { QUERY_KEYS, CACHE_TIMES } from "@/lib/constants";

export function BlogSection() {
  const { data: posts, isLoading, error } = useQuery({
    queryKey: [QUERY_KEYS.BLOG_POSTS],
    queryFn: APIClient.getBlogPosts,
    staleTime: CACHE_TIMES.BLOG_POSTS,
    retry: 2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const getCategoryImage = (tags: string[] = []) => {
    const category = tags[0]?.toLowerCase() || '';
    switch (category) {
      case 'machine learning':
      case 'ml':
        return "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400";
      case 'data engineering':
      case 'data':
        return "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400";
      case 'ai':
      case 'ethics':
        return "https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400";
      default:
        return "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTagColor = (tag: string) => {
    switch (tag.toLowerCase()) {
      case 'machine learning':
      case 'ml':
        return 'bg-primary/20 text-primary';
      case 'data engineering':
      case 'data':
        return 'bg-secondary/20 text-secondary';
      case 'ai':
      case 'ethics':
        return 'bg-accent/20 text-accent';
      default:
        return 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Fallback blog posts if API fails
  const fallbackPosts = [
    {
      id: '1',
      title: 'Building Scalable AI Pipelines',
      excerpt: 'Learn how to design and implement machine learning pipelines that scale with your business needs.',
      tags: ['Machine Learning', 'DevOps'],
      published_date: '2024-01-15',
      reading_time: 8,
    },
    {
      id: '2', 
      title: 'The Future of Data Engineering',
      excerpt: 'Exploring emerging trends and technologies shaping the data engineering landscape.',
      tags: ['Data Engineering', 'Technology'],
      published_date: '2024-01-10',
      reading_time: 6,
    },
    {
      id: '3',
      title: 'Ethics in AI Development',
      excerpt: 'Discussing the importance of ethical considerations in artificial intelligence development.',
      tags: ['AI', 'Ethics'],
      published_date: '2024-01-05',
      reading_time: 10,
    },
  ];

  const displayPosts = posts && posts.length > 0 ? posts.slice(0, 3) : fallbackPosts;

  return (
    <section id="blog" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">Latest Articles</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Insights, tutorials, and thoughts on data science, AI, and software engineering
          </p>
        </motion.div>
        
        {error && (
          <div className="text-center mb-8">
            <p className="text-yellow-600 dark:text-yellow-400">
              Unable to load latest articles. Showing featured content instead.
            </p>
          </div>
        )}
        
        {isLoading ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <div className="w-full h-48 bg-gray-200 dark:bg-gray-700 animate-pulse" />
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="h-6 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
                    <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  </div>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-3 animate-pulse" />
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse" />
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-4 animate-pulse" />
                  <div className="flex items-center justify-between">
                    <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                    <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <motion.div 
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {displayPosts.map((post) => (
              <motion.article 
                key={post.id} 
                variants={itemVariants}
                data-testid={`blog-post-${post.id}`}
              >
                <Card className="glass-effect overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 group cursor-pointer">
                  <div className="relative overflow-hidden">
                    <img 
                      src={getCategoryImage(post.tags)} 
                      alt={post.title} 
                      className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <Badge 
                        variant="secondary" 
                        className={getTagColor(post.tags?.[0] || '')}
                      >
                        {post.tags?.[0] || 'General'}
                      </Badge>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(post.published_date)}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold mb-3 hover:text-primary transition-colors group-hover:text-primary">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {post.reading_time} min read
                      </span>
                      <span className="text-primary hover:text-blue-600 font-medium group-hover:text-blue-600 transition-colors">
                        Read More →
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </motion.article>
            ))}
          </motion.div>
        )}
        
        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <motion.a
            href="/blog"
            className="inline-block px-8 py-4 border-2 border-primary text-primary hover:bg-primary hover:text-white rounded-full transition-all duration-300 transform hover:scale-105"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            data-testid="button-view-all-articles"
          >
            View All Articles
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
}
