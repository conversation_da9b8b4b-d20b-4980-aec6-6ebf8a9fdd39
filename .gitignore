# =================================================================
# Dependencies
# =================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# =================================================================
# Build outputs
# =================================================================
dist/
build/
out/
.next/
.nuxt/
.vercel/
.netlify/

# =================================================================
# Environment & Secrets
# =================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# =================================================================
# Operating System
# =================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.swp
*.swo
*~

# =================================================================
# IDE & Editors
# =================================================================
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.idea/
*.sublime-project
*.sublime-workspace
.history/
*.tmp

# =================================================================
# Testing
# =================================================================
coverage/
.nyc_output/
.jest/
*.lcov
test-results/
playwright-report/
test-results.xml

# =================================================================
# Logs
# =================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# =================================================================
# Runtime data
# =================================================================
pids/
*.pid
*.seed
*.pid.lock
lib-cov/

# =================================================================
# Dependency directories
# =================================================================
jspm_packages/
bower_components/

# =================================================================
# TypeScript
# =================================================================
*.tsbuildinfo
.tscache/

# =================================================================
# ESLint
# =================================================================
.eslintcache

# =================================================================
# Stylelint
# =================================================================
.stylelintcache

# =================================================================
# Microbundle cache
# =================================================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# =================================================================
# Optional npm cache directory
# =================================================================
.npm/

# =================================================================
# Optional eslint cache
# =================================================================
.eslintcache

# =================================================================
# Optional stylelint cache
# =================================================================
.stylelintcache

# =================================================================
# Yarn
# =================================================================
.yarn-integrity
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# =================================================================
# Serverless directories
# =================================================================
.serverless/

# =================================================================
# FuseBox cache
# =================================================================
.fusebox/

# =================================================================
# DynamoDB Local files
# =================================================================
.dynamodb/

# =================================================================
# TernJS port file
# =================================================================
.tern-port

# =================================================================
# Stores VSCode versions used for testing VSCode extensions
# =================================================================
.vscode-test/

# =================================================================
# Temporary folders
# =================================================================
tmp/
temp/

# =================================================================
# Database
# =================================================================
*.db
*.sqlite
*.sqlite3

# =================================================================
# Project specific
# =================================================================
server/public/
vite.config.ts.*
*.tar.gz

# =================================================================
# Security
# =================================================================
.env.backup
.env.*.backup
*.pem
*.key
*.crt

# =================================================================
# Debugging
# =================================================================
.vscode/launch.json
chrome-debug/

# =================================================================
# Bundle analysis
# =================================================================
bundle-analyzer-report.html
.bundle-analyzer/

# =================================================================
# Storybook
# =================================================================
storybook-static/

# =================================================================
# Turbo
# =================================================================
.turbo/

# =================================================================
# Sentry
# =================================================================
.sentryclirc

# =================================================================
# Local development
# =================================================================
.local/
.cache/