# AWS SAM template for Lambda deployment
AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Description: Portfolio API Serverless Backend

Globals:
  Function:
    Timeout: 30
    Runtime: python3.9
    Environment:
      Variables:
        ENVIRONMENT: production

Resources:
  PortfolioApi:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: .
      Handler: main.lambda_handler
      Runtime: python3.9
      Timeout: 30
      MemorySize: 512
      Environment:
        Variables:
          GITHUB_TOKEN: !Ref GitHubToken
          GITHUB_USERNAME: !Ref GitHubUsername
          RESEND_API_KEY: !Ref ResendApiKey
          CONTACT_EMAIL: !Ref ContactEmail
      Events:
        ApiGateway:
          Type: Api
          Properties:
            Path: /{proxy+}
            Method: any
            RestApiId: !Ref ApiGateway
        ApiGatewayRoot:
          Type: Api
          Properties:
            Path: /
            Method: any
            RestApiId: !Ref ApiGateway

  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: prod
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,Authorization'"
        AllowOrigin: "'*'"

Parameters:
  GitHubToken:
    Type: String
    NoEcho: true
    Description: GitHub Personal Access Token
  
  GitHubUsername:
    Type: String
    Default: khiwniti
    Description: GitHub Username
    
  ResendApiKey:
    Type: String
    NoEcho: true
    Description: Resend API Key for email
    
  ContactEmail:
    Type: String
    Description: Contact email for form submissions

Outputs:
  ApiGatewayUrl:
    Description: URL of the API Gateway
    Value: !Sub 'https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/prod/'