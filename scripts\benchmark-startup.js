#!/usr/bin/env node

import { spawn } from 'child_process';
import { performance } from 'perf_hooks';

console.log('🔍 Benchmarking development startup time...\n');

const startTime = performance.now();
let serverReady = false;
let clientReady = false;

const devProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'pipe',
  shell: true
});

devProcess.stdout.on('data', (data) => {
  const output = data.toString();
  
  // Check for server ready indicators
  if (output.includes('Server running') || output.includes('listening')) {
    if (!serverReady) {
      serverReady = true;
      const serverTime = performance.now() - startTime;
      console.log(`✅ Server ready in ${Math.round(serverTime)}ms`);
      checkBothReady();
    }
  }
  
  // Check for Vite ready indicators
  if (output.includes('Local:') || output.includes('ready in')) {
    if (!clientReady) {
      clientReady = true;
      const clientTime = performance.now() - startTime;
      console.log(`✅ Client ready in ${Math.round(clientTime)}ms`);
      checkBothReady();
    }
  }
});

devProcess.stderr.on('data', (data) => {
  // Also check stderr for ready messages
  const output = data.toString();
  if (output.includes('Local:') || output.includes('ready in')) {
    if (!clientReady) {
      clientReady = true;
      const clientTime = performance.now() - startTime;
      console.log(`✅ Client ready in ${Math.round(clientTime)}ms`);
      checkBothReady();
    }
  }
});

function checkBothReady() {
  if (serverReady && clientReady) {
    const totalTime = performance.now() - startTime;
    console.log(`\n🎉 Both services ready in ${Math.round(totalTime)}ms`);
    console.log(`📊 Total startup time: ${(totalTime / 1000).toFixed(2)} seconds\n`);
    
    // Kill the process and exit
    devProcess.kill('SIGTERM');
    process.exit(0);
  }
}

// Timeout after 60 seconds
setTimeout(() => {
  console.log('\n⏰ Timeout reached (60s). Killing process...');
  devProcess.kill('SIGTERM');
  process.exit(1);
}, 60000);

// Handle process termination
process.on('SIGINT', () => {
  devProcess.kill('SIGTERM');
  process.exit(0);
});