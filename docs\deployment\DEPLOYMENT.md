# Deployment Guide

Comprehensive deployment guide for the getintheq.space platform covering multiple deployment strategies, environment configurations, and operational procedures for production environments.

## Table of Contents

- [Deployment Overview](#deployment-overview)
- [Environment Setup](#environment-setup)
- [Vercel Deployment](#vercel-deployment)
- [Manual Deployment](#manual-deployment)
- [Docker Deployment](#docker-deployment)
- [Cloud Platform Deployment](#cloud-platform-deployment)
- [Environment Management](#environment-management)
- [Monitoring & Health Checks](#monitoring--health-checks)
- [Rollback Procedures](#rollback-procedures)
- [Performance Optimization](#performance-optimization)

## Deployment Overview

The getintheq.space platform supports multiple deployment strategies to accommodate different infrastructure requirements and operational preferences.

### Deployment Architecture

```mermaid
graph TB
    subgraph "Source Control"
        GitHub[GitHub Repository]
        Main[Main Branch]
        Feature[Feature Branches]
        Tags[Release Tags]
    end

    subgraph "CI/CD Pipeline"
        Actions[GitHub Actions]
        Tests[Automated Tests]
        Build[Production Build]
        Security[Security Scan]
    end

    subgraph "Deployment Targets"
        Vercel[Vercel Platform]
        Manual[Manual Deployment]
        Docker[Docker Containers]
        Cloud[Cloud Platforms]
    end

    subgraph "Infrastructure"
        Database[(PostgreSQL)]
        CDN[Content Delivery Network]
        Monitoring[Monitoring Services]
        Email[Email Services]
    end

    GitHub --> Actions
    Main --> Tests
    Feature --> Build
    Tags --> Security
    
    Actions --> Vercel
    Tests --> Manual
    Build --> Docker
    Security --> Cloud
    
    Vercel --> Database
    Manual --> CDN
    Docker --> Monitoring
    Cloud --> Email
```

### Deployment Strategies

| Strategy | Use Case | Complexity | Scalability | Cost |
|----------|----------|------------|-------------|------|
| **Vercel** | Quick deployment, serverless | Low | High | Medium |
| **Manual** | Custom infrastructure, control | Medium | Medium | Variable |
| **Docker** | Containerized environments | Medium | High | Low-Medium |
| **Cloud Platforms** | Enterprise, multi-region | High | Very High | High |

## Environment Setup

### Prerequisites

#### Development Environment
```bash
# Required software versions
Node.js >= 18.0.0
npm >= 9.0.0
Git >= 2.40.0
PostgreSQL >= 16.0
```

#### Production Requirements
```bash
# Server specifications (minimum)
CPU: 2 cores
RAM: 4GB
Storage: 20GB SSD
Network: 1Gbps

# Recommended specifications
CPU: 4+ cores
RAM: 8GB+
Storage: 50GB+ SSD
Network: 10Gbps
```

### Environment Variables

#### Required Variables
```bash
# Database Configuration
DATABASE_URL="postgresql://username:password@hostname:port/database"

# Authentication
JWT_SECRET="your-super-secret-jwt-key-32-characters-minimum"
SESSION_SECRET="your-session-secret-key-32-characters-minimum"

# Application Environment
NODE_ENV="production"
NEXTAUTH_URL="https://yourdomain.com"
```

#### Optional Variables
```bash
# GitHub Integration
GITHUB_TOKEN="ghp_your_github_personal_access_token"
GITHUB_USERNAME="your-github-username"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
ADMIN_EMAIL="<EMAIL>"

# External APIs
OPENAI_API_KEY="sk-your-openai-api-key"
ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key"

# Analytics & Monitoring
NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"
SENTRY_DSN="https://your-sentry-dsn"

# Performance & Security
RATE_LIMIT_MAX_REQUESTS="100"
RATE_LIMIT_WINDOW_MS="900000"
CORS_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
```

## Vercel Deployment

Vercel provides the simplest deployment strategy with built-in optimizations and global CDN.

### Initial Setup

#### 1. Vercel CLI Installation
```bash
npm install -g vercel
vercel login
```

#### 2. Project Configuration
```bash
# Initialize Vercel project
vercel

# Configure project settings
vercel --prod
```

#### 3. Environment Variables Setup
```bash
# Set production environment variables
vercel env add DATABASE_URL production
vercel env add JWT_SECRET production
vercel env add SESSION_SECRET production

# Set preview environment variables
vercel env add DATABASE_URL preview
vercel env add JWT_SECRET preview
vercel env add SESSION_SECRET preview
```

### Automated Deployment

#### GitHub Integration
```yaml
# .github/workflows/vercel-deploy.yml
name: Vercel Production Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
    branches: [main]

jobs:
  Deploy-Production:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Type check
        run: npm run type-check
        
      - name: Lint
        run: npm run lint:check
        
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
        
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
```

#### Preview Deployment
```yaml
# .github/workflows/vercel-preview.yml
name: Vercel Preview Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  pull_request:
    branches: [main]

jobs:
  Deploy-Preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
        
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}
```

### Vercel Configuration

#### vercel.json
```json
{
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "nextjs",
  "regions": ["iad1", "sfo1", "lhr1", "hnd1"],
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "no-store, no-cache, must-revalidate, private"
        }
      ]
    },
    {
      "source": "/((?!api).*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/admin",
      "has": [
        {
          "type": "cookie",
          "key": "auth-token"
        }
      ],
      "destination": "/admin/dashboard",
      "permanent": false
    }
  ],
  "rewrites": [
    {
      "source": "/api-docs",
      "destination": "/docs/api/openapi.yaml"
    }
  ]
}
```

## Manual Deployment

Manual deployment provides full control over the infrastructure and deployment process.

### Server Setup

#### 1. Server Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx for reverse proxy
sudo apt install nginx

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib
```

#### 2. Database Setup
```bash
# Create database user
sudo -u postgres createuser --interactive --pwprompt

# Create database
sudo -u postgres createdb getintheq_production

# Configure PostgreSQL
sudo nano /etc/postgresql/16/main/postgresql.conf
# Set: listen_addresses = '*'

sudo nano /etc/postgresql/16/main/pg_hba.conf
# Add: host all all 0.0.0.0/0 md5

sudo systemctl restart postgresql
```

#### 3. Application Deployment
```bash
# Create application directory
sudo mkdir -p /var/www/getintheq
sudo chown $USER:$USER /var/www/getintheq

# Clone repository
cd /var/www/getintheq
git clone https://github.com/yourusername/getintheq.space.git .

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Create environment file
cp .env.example .env.local
nano .env.local
# Configure all required environment variables
```

#### 4. Process Management
```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'getintheq-space',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/getintheq',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/getintheq/error.log',
    out_file: '/var/log/getintheq/out.log',
    log_file: '/var/log/getintheq/combined.log',
    time: true,
    max_memory_restart: '500M',
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.git']
  }]
};
EOF

# Create log directory
sudo mkdir -p /var/log/getintheq
sudo chown $USER:$USER /var/log/getintheq

# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 5. Nginx Configuration
```nginx
# /etc/nginx/sites-available/getintheq.space
server {
    listen 80;
    server_name getintheq.space www.getintheq.space;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name getintheq.space www.getintheq.space;

    ssl_certificate /etc/letsencrypt/live/getintheq.space/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/getintheq.space/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static file caching
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }

    location /favicon.ico {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }
}
```

#### 6. SSL Certificate Setup
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d getintheq.space -d www.getintheq.space

# Test automatic renewal
sudo certbot renew --dry-run

# Enable Nginx
sudo ln -s /etc/nginx/sites-available/getintheq.space /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Docker Deployment

Containerized deployment for consistent environments and easy scaling.

### Dockerfile
```dockerfile
# Multi-stage build for optimization
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci --only=production && npm cache clean --force

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Disable telemetry during the build
ENV NEXT_TELEMETRY_DISABLED 1

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Create nextjs user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Start the application
CMD ["node", "server.js"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:16-alpine
    environment:
      POSTGRES_DB: getintheq
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d getintheq"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Docker Commands
```bash
# Build and start services
docker-compose up -d --build

# View logs
docker-compose logs -f app

# Scale application
docker-compose up -d --scale app=3

# Update application
docker-compose pull
docker-compose up -d --build

# Backup database
docker-compose exec postgres pg_dump -U $POSTGRES_USER getintheq > backup.sql

# Restore database
docker-compose exec -T postgres psql -U $POSTGRES_USER getintheq < backup.sql

# Clean up
docker-compose down -v
docker system prune -a
```

## Cloud Platform Deployment

### AWS Deployment

#### 1. Infrastructure as Code (Terraform)
```hcl
# main.tf
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC Configuration
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "getintheq-vpc"
  }
}

# Application Load Balancer
resource "aws_lb" "main" {
  name               = "getintheq-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets           = aws_subnet.public[*].id

  enable_deletion_protection = false
}

# ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "getintheq-cluster"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# RDS PostgreSQL
resource "aws_db_instance" "postgres" {
  identifier = "getintheq-postgres"
  
  engine         = "postgres"
  engine_version = "16.1"
  instance_class = "db.t3.micro"
  
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type         = "gp2"
  storage_encrypted    = true
  
  db_name  = "getintheq"
  username = var.db_username
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = true
  deletion_protection = false
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "getintheq-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_replication_group" "redis" {
  replication_group_id       = "getintheq-redis"
  description                = "Redis cluster for getintheq"
  
  port                = 6379
  parameter_group_name = "default.redis7"
  node_type           = "cache.t3.micro"
  num_cache_clusters  = 1
  
  subnet_group_name  = aws_elasticache_subnet_group.main.name
  security_group_ids = [aws_security_group.redis.id]
  
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
}
```

#### 2. ECS Task Definition
```json
{
  "family": "getintheq-app",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "getintheq-app",
      "image": "your-account.dkr.ecr.region.amazonaws.com/getintheq:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:ssm:region:account:parameter/getintheq/database-url"
        },
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:ssm:region:account:parameter/getintheq/jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/getintheq",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

### Azure Deployment

#### 1. Azure Container Instances
```yaml
# azure-deploy.yml
apiVersion: 2019-12-01
location: eastus
name: getintheq-container-group
properties:
  containers:
  - name: getintheq-app
    properties:
      image: getintheqregistry.azurecr.io/getintheq:latest
      resources:
        requests:
          cpu: 1
          memoryInGb: 2
      ports:
      - port: 3000
        protocol: TCP
      environmentVariables:
      - name: NODE_ENV
        value: production
      - name: DATABASE_URL
        secureValue: "[parameters('databaseUrl')]"
      - name: JWT_SECRET
        secureValue: "[parameters('jwtSecret')]"
  - name: postgres
    properties:
      image: postgres:16-alpine
      resources:
        requests:
          cpu: 0.5
          memoryInGb: 1
      ports:
      - port: 5432
        protocol: TCP
      environmentVariables:
      - name: POSTGRES_DB
        value: getintheq
      - name: POSTGRES_USER
        value: getintheq
      - name: POSTGRES_PASSWORD
        secureValue: "[parameters('postgresPassword')]"
      volumeMounts:
      - name: postgres-storage
        mountPath: /var/lib/postgresql/data
  osType: Linux
  restartPolicy: Always
  ipAddress:
    type: Public
    ports:
    - protocol: TCP
      port: 3000
    dnsNameLabel: getintheq-app
  volumes:
  - name: postgres-storage
    azureFile:
      shareName: postgres-data
      storageAccountName: "[parameters('storageAccountName')]"
      storageAccountKey: "[parameters('storageAccountKey')]"
tags:
  environment: production
  project: getintheq
type: Microsoft.ContainerInstance/containerGroups
```

### Google Cloud Platform Deployment

#### 1. Cloud Run Deployment
```yaml
# cloudrun.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: getintheq-app
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "100"
        autoscaling.knative.dev/minScale: "1"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
    spec:
      containers:
      - image: gcr.io/PROJECT_ID/getintheq:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-url
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Environment Management

### Environment Configuration Matrix

| Environment | Database | Caching | Monitoring | Backup | SSL |
|-------------|----------|---------|------------|--------|-----|
| **Development** | Local PostgreSQL | None | Console logs | None | Self-signed |
| **Preview** | Neon branch | Memory | Basic | None | Let's Encrypt |
| **Staging** | Neon staging | Redis | Full | Daily | Let's Encrypt |
| **Production** | Neon production | Redis cluster | Enterprise | Hourly | Commercial SSL |

### Configuration Management

#### 1. Environment File Templates
```bash
# scripts/setup-env.sh
#!/bin/bash

ENVIRONMENT=${1:-development}

case $ENVIRONMENT in
  "development")
    cp .env.development .env.local
    echo "Development environment configured"
    ;;
  "staging")
    cp .env.staging .env.local
    echo "Staging environment configured"
    ;;
  "production")
    cp .env.production .env.local
    echo "Production environment configured"
    ;;
  *)
    echo "Unknown environment: $ENVIRONMENT"
    exit 1
    ;;
esac

# Validate required environment variables
node scripts/validate-env.js
```

#### 2. Environment Validation
```javascript
// scripts/validate-env.js
const requiredVars = [
  'DATABASE_URL',
  'JWT_SECRET',
  'SESSION_SECRET',
  'NODE_ENV'
];

const optionalVars = [
  'GITHUB_TOKEN',
  'SMTP_HOST',
  'NEXT_PUBLIC_GA_ID'
];

function validateEnvironment() {
  const missing = [];
  const warnings = [];

  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  });

  optionalVars.forEach(varName => {
    if (!process.env[varName]) {
      warnings.push(varName);
    }
  });

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => console.error(`  - ${varName}`));
    process.exit(1);
  }

  if (warnings.length > 0) {
    console.warn('⚠️  Optional environment variables not set:');
    warnings.forEach(varName => console.warn(`  - ${varName}`));
  }

  console.log('✅ Environment validation passed');
}

validateEnvironment();
```

## Monitoring & Health Checks

### Health Check Endpoint
```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { db } from '@/lib/database';

export async function GET() {
  const checks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV,
    checks: {
      database: 'unknown',
      memory: 'unknown',
      uptime: process.uptime()
    }
  };

  try {
    // Database health check
    await db.$queryRaw`SELECT 1`;
    checks.checks.database = 'healthy';
  } catch (error) {
    checks.checks.database = 'unhealthy';
    checks.status = 'degraded';
  }

  // Memory health check
  const memUsage = process.memoryUsage();
  const memUsageMB = Math.round(memUsage.heapUsed / 1024 / 1024);
  checks.checks.memory = memUsageMB > 400 ? 'high' : 'healthy';

  const statusCode = checks.status === 'healthy' ? 200 : 503;
  
  return NextResponse.json(checks, { status: statusCode });
}
```

### Monitoring Scripts
```bash
# scripts/monitor.sh
#!/bin/bash

HEALTH_URL="https://getintheq.space/api/health"
WEBHOOK_URL="your-webhook-url"

check_health() {
  local response=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL")
  
  if [ "$response" != "200" ]; then
    echo "Health check failed with status: $response"
    send_alert "Health check failed" "$response"
    return 1
  fi
  
  echo "Health check passed"
  return 0
}

send_alert() {
  local message="$1"
  local details="$2"
  
  curl -X POST "$WEBHOOK_URL" \
    -H "Content-Type: application/json" \
    -d "{
      \"text\": \"$message\",
      \"details\": \"$details\",
      \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
    }"
}

# Run health check
check_health
```

## Rollback Procedures

### Vercel Rollback
```bash
# List recent deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url] --token=$VERCEL_TOKEN
```

### Manual Rollback
```bash
# scripts/rollback.sh
#!/bin/bash

BACKUP_DIR="/var/backups/getintheq"
ROLLBACK_VERSION=${1:-previous}

echo "Starting rollback to version: $ROLLBACK_VERSION"

# Stop application
pm2 stop getintheq-space

# Backup current version
cp -r /var/www/getintheq "$BACKUP_DIR/rollback-backup-$(date +%Y%m%d-%H%M%S)"

# Restore previous version
if [ -d "$BACKUP_DIR/$ROLLBACK_VERSION" ]; then
  rm -rf /var/www/getintheq/*
  cp -r "$BACKUP_DIR/$ROLLBACK_VERSION"/* /var/www/getintheq/
  
  # Restore database if needed
  if [ -f "$BACKUP_DIR/$ROLLBACK_VERSION/database.sql" ]; then
    echo "Restoring database..."
    psql $DATABASE_URL < "$BACKUP_DIR/$ROLLBACK_VERSION/database.sql"
  fi
  
  # Restart application
  cd /var/www/getintheq
  npm ci --only=production
  pm2 restart getintheq-space
  
  echo "Rollback completed successfully"
else
  echo "Rollback version not found: $ROLLBACK_VERSION"
  pm2 start getintheq-space
  exit 1
fi
```

### Docker Rollback
```bash
# Tag current image as backup
docker tag getintheq:latest getintheq:backup-$(date +%Y%m%d)

# Pull previous version
docker pull getintheq:previous

# Update docker-compose to use previous version
sed -i 's/getintheq:latest/getintheq:previous/' docker-compose.yml

# Restart services
docker-compose up -d
```

## Performance Optimization

### Build Optimization
```javascript
// next.config.js optimizations
const nextConfig = {
  // Enable experimental features
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 31536000,
  },
  
  // Compression
  compress: true,
  
  // Bundle analyzer
  webpack: (config, { isServer, webpack }) => {
    // Optimize bundle size
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    };

    return config;
  },
};
```

### Cache Optimization
```nginx
# Nginx caching configuration
location /_next/static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header X-Cache-Status $upstream_cache_status;
}

location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1M;
    add_header Cache-Control "public, immutable";
    add_header X-Cache-Status $upstream_cache_status;
}

# API response caching
location /api/ {
    proxy_cache api_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
    add_header X-Cache-Status $upstream_cache_status;
}
```

---

**Deployment Guide Version**: 1.0.0  
**Last Updated**: 2024-08-14  
**Supported Platforms**: Vercel, AWS, Azure, GCP, Manual  
**Next Review**: 2024-11-14