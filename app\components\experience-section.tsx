'use client'

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import { Calendar, MapPin, Building, Award, TrendingUp } from "lucide-react";

interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  location: string;
  description: string;
  achievements: string[];
  technologies: string[];
  type: "full-time" | "contract" | "internship";
}

const experiences: Experience[] = [
  {
    id: "1",
    company: "Freshair",
    position: "Senior Data & AI Solutions Engineer",
    duration: "2022 - Present",
    location: "Bangkok, Thailand",
    description: "Leading the development of AI-powered solutions and data infrastructure for enterprise clients, focusing on machine learning model deployment and scalable data pipelines.",
    achievements: [
      "Architected and deployed ML models serving 1M+ daily predictions",
      "Reduced data processing time by 75% through optimized ETL pipelines",
      "Led a team of 5 engineers in developing real-time analytics platform",
      "Implemented MLOps practices reducing model deployment time by 60%"
    ],
    technologies: ["Python", "TensorFlow", "AWS", "Docker", "Kubernetes", "Apache Spark", "PostgreSQL"],
    type: "full-time"
  },
  {
    id: "2",
    company: "Tesco Lotus PLC",
    position: "Data Engineer & ML Specialist",
    duration: "2020 - 2022",
    location: "Bangkok, Thailand",
    description: "Developed and maintained data infrastructure for retail analytics, implementing machine learning solutions for inventory optimization and customer behavior analysis.",
    achievements: [
      "Built predictive models improving inventory accuracy by 35%",
      "Designed data warehouse handling 10TB+ daily transaction data",
      "Created customer segmentation models increasing marketing ROI by 28%",
      "Automated reporting processes saving 40+ hours weekly"
    ],
    technologies: ["Python", "SQL", "Azure", "Power BI", "Apache Airflow", "MongoDB", "Scikit-learn"],
    type: "full-time"
  },
  {
    id: "3",
    company: "Tech Startup",
    position: "Full Stack Developer",
    duration: "2019 - 2020",
    location: "Bangkok, Thailand",
    description: "Developed web applications and mobile solutions for various clients, focusing on modern JavaScript frameworks and cloud deployment strategies.",
    achievements: [
      "Built 15+ responsive web applications using React and Node.js",
      "Implemented CI/CD pipelines reducing deployment time by 50%",
      "Optimized application performance achieving 95+ Lighthouse scores",
      "Mentored junior developers in modern web development practices"
    ],
    technologies: ["React", "Node.js", "TypeScript", "MongoDB", "AWS", "Docker", "Git"],
    type: "full-time"
  }
];

export function ExperienceSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  return (
    <section id="experience" className="py-20 bg-gray-50 dark:bg-gray-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-primary to-secondary rounded-full filter blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-accent to-primary rounded-full filter blur-3xl" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">Professional Experience</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            A journey through innovative companies, building scalable solutions and leading technical teams
          </p>
        </motion.div>

        <motion.div 
          ref={ref}
          className="relative"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Timeline line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-secondary to-accent opacity-30" />

          <div className="space-y-12">
            {experiences.map((exp, index) => (
              <motion.div
                key={exp.id}
                variants={itemVariants}
                className="relative"
              >
                {/* Timeline dot */}
                <motion.div 
                  className="absolute left-6 w-4 h-4 bg-gradient-to-r from-primary to-secondary rounded-full border-4 border-white dark:border-gray-900 shadow-lg"
                  initial={{ scale: 0 }}
                  animate={isInView ? { scale: 1 } : { scale: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
                />

                {/* Experience card */}
                <motion.div 
                  className="ml-16 glass-effect rounded-2xl p-8 border border-primary/10"
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex flex-wrap items-start justify-between mb-4">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        {exp.position}
                      </h3>
                      <div className="flex items-center space-x-4 text-gray-600 dark:text-gray-300 mb-2">
                        <div className="flex items-center space-x-2">
                          <Building className="w-4 h-4" />
                          <span className="font-semibold">{exp.company}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4" />
                          <span>{exp.duration}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4" />
                          <span>{exp.location}</span>
                        </div>
                      </div>
                    </div>
                    
                    <motion.div 
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        exp.type === 'full-time' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        exp.type === 'contract' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                        'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                      }`}
                      whileHover={{ scale: 1.05 }}
                    >
                      {exp.type.charAt(0).toUpperCase() + exp.type.slice(1)}
                    </motion.div>
                  </div>

                  <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                    {exp.description}
                  </p>

                  {/* Key achievements */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                      <Award className="w-5 h-5 mr-2 text-primary" />
                      Key Achievements
                    </h4>
                    <div className="grid md:grid-cols-2 gap-3">
                      {exp.achievements.map((achievement, achIndex) => (
                        <motion.div
                          key={achIndex}
                          className="flex items-start space-x-3 p-3 rounded-lg bg-primary/5 border border-primary/10"
                          initial={{ opacity: 0, x: -20 }}
                          animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                          transition={{ duration: 0.5, delay: index * 0.2 + achIndex * 0.1 + 0.5 }}
                          whileHover={{ x: 5 }}
                        >
                          <TrendingUp className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{achievement}</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* Technologies */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Technologies Used
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {exp.technologies.map((tech, techIndex) => (
                        <motion.span
                          key={tech}
                          className="px-3 py-1 bg-gradient-to-r from-primary/10 to-secondary/10 text-primary dark:text-primary-light rounded-full text-sm font-medium border border-primary/20"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                          transition={{ duration: 0.3, delay: index * 0.2 + techIndex * 0.05 + 0.7 }}
                          whileHover={{ scale: 1.05, y: -2 }}
                        >
                          {tech}
                        </motion.span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
