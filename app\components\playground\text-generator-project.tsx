'use client'

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Sparkles, Wand2, Copy, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface TextGeneratorProjectProps {
  onBack: () => void;
  projectData: any;
}

const textTypes = [
  { id: 'story', name: 'Creative Story', prompt: 'Write a captivating short story about' },
  { id: 'blog', name: 'Blog Post', prompt: 'Create an engaging blog post about' },
  { id: 'email', name: 'Professional Email', prompt: 'Write a professional email regarding' },
  { id: 'poem', name: 'Poetry', prompt: 'Compose a beautiful poem about' },
  { id: 'code', name: 'Code Documentation', prompt: 'Write clear documentation for' },
  { id: 'marketing', name: 'Marketing Copy', prompt: 'Create compelling marketing copy for' }
];

const aiModels = [
  { id: 'gpt4', name: 'GPT-4 Turbo', description: 'Most advanced reasoning' },
  { id: 'claude', name: 'Claude 3', description: 'Excellent for creative writing' },
  { id: 'gemini', name: 'Gemini Pro', description: 'Great for technical content' }
];

export function TextGeneratorProject({ onBack, projectData }: TextGeneratorProjectProps) {
  const [prompt, setPrompt] = useState('');
  const [selectedType, setSelectedType] = useState('story');
  const [selectedModel, setSelectedModel] = useState('gpt4');
  const [generatedText, setGeneratedText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [wordCount, setWordCount] = useState(150);

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    try {
      // Import the API client
      const { PlaygroundAPI, handleApiError } = await import('@/lib/playground-api');
      
      // Make API call to generate text
      const response = await PlaygroundAPI.generateText({
        prompt: prompt.trim(),
        text_type: selectedType as any,
        model: selectedModel as any,
        word_count: wordCount
      });
      
      // Simulate typing effect with real generated text
      setGeneratedText('');
      let currentText = '';
      const words = response.generated_text.split(' ');
      
      for (let i = 0; i < words.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 30));
        currentText += (i > 0 ? ' ' : '') + words[i];
        setGeneratedText(currentText);
      }
      
    } catch (error) {
      console.error('Text generation error:', error);
      // Fallback to mock content if API fails
      const mockContent = generateMockContent(selectedType, prompt, wordCount);
      setGeneratedText(mockContent);
    } finally {
      setIsGenerating(false);
    }
  };

  const generateMockContent = (type: string, userPrompt: string, words: number) => {
    const templates = {
      story: `Once upon a time, in a world where ${userPrompt} was the norm, there lived a curious individual who dared to question everything. The morning sun cast long shadows across the landscape as they embarked on a journey that would change their perspective forever. Through trials and discoveries, they learned that sometimes the most extraordinary adventures begin with the simplest questions. The story unfolded with unexpected twists, revealing hidden truths about courage, friendship, and the power of believing in oneself.`,
      
      blog: `# Understanding ${userPrompt}: A Comprehensive Guide\n\nIn today's rapidly evolving world, ${userPrompt} has become increasingly important. This comprehensive guide will explore the key aspects, benefits, and practical applications that you need to know.\n\n## Key Benefits\n- Enhanced productivity and efficiency\n- Improved user experience\n- Cost-effective solutions\n- Scalable implementation\n\n## Conclusion\nBy implementing these strategies, you can successfully leverage ${userPrompt} to achieve your goals.`,
      
      email: `Subject: Regarding ${userPrompt}\n\nDear [Recipient],\n\nI hope this email finds you well. I am writing to discuss ${userPrompt} and would like to schedule a meeting to explore this opportunity further.\n\nBased on our previous conversations, I believe this initiative aligns perfectly with our strategic objectives and could deliver significant value to our organization.\n\nI would appreciate the opportunity to present a detailed proposal at your earliest convenience.\n\nBest regards,\n[Your Name]`,
      
      poem: `In the realm where ${userPrompt} dwells,\nWhispers of wonder softly tell\nStories of beauty, grace, and light,\nDancing through the endless night.\n\nEach moment holds a precious gleam,\nLike fragments of a vivid dream,\nWhere hope and joy forever flow,\nAnd gentle breezes softly blow.`,
      
      code: `# ${userPrompt} Documentation\n\n## Overview\nThis module provides comprehensive functionality for ${userPrompt}. It includes robust error handling, efficient algorithms, and extensive customization options.\n\n## Features\n- High-performance implementation\n- Comprehensive error handling\n- Extensive configuration options\n- Full TypeScript support\n\n## Usage Example\n\`\`\`typescript\nconst result = await processData({\n  input: data,\n  options: { optimize: true }\n});\n\`\`\``,
      
      marketing: `🚀 Discover the Power of ${userPrompt}!\n\nTransform your business with cutting-edge solutions that deliver real results. Our innovative approach to ${userPrompt} has helped thousands of companies achieve unprecedented growth.\n\n✨ Key Benefits:\n• 10x faster implementation\n• 50% cost reduction\n• 99.9% reliability\n• 24/7 expert support\n\nDon't let your competitors get ahead. Join the revolution today!\n\n[Get Started Now] [Learn More]`
    };
    
    return templates[type as keyof typeof templates] || templates.story;
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedText);
  };

  const downloadText = () => {
    const blob = new Blob([generatedText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `generated-${selectedType}.txt`;
    a.click();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 dark:from-purple-900 dark:via-pink-900 dark:to-indigo-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Playground
          </Button>
          
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold gradient-text">AI Text Generator</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Generate creative content with advanced AI models
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wand2 className="w-5 h-5" />
                  Content Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Content Type */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Content Type</label>
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {textTypes.map(type => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* AI Model */}
                <div>
                  <label className="text-sm font-medium mb-2 block">AI Model</label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {aiModels.map(model => (
                        <SelectItem key={model.id} value={model.id}>
                          <div>
                            <div className="font-medium">{model.name}</div>
                            <div className="text-xs text-gray-500">{model.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Prompt */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Your Prompt</label>
                  <Textarea
                    placeholder="Enter your topic or idea..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                {/* Word Count */}
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Target Length: {wordCount} words
                  </label>
                  <input
                    type="range"
                    min="50"
                    max="500"
                    value={wordCount}
                    onChange={(e) => setWordCount(Number(e.target.value))}
                    className="w-full"
                  />
                </div>

                {/* Generate Button */}
                <Button
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate Content
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Output Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="glass-effect h-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Generated Content</CardTitle>
                  {generatedText && (
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={copyToClipboard}>
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={downloadText}>
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {generatedText ? (
                  <div className="space-y-4">
                    <div className="prose dark:prose-invert max-w-none">
                      <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                        {generatedText}
                      </pre>
                    </div>
                    
                    <div className="flex gap-2 pt-4 border-t">
                      <Badge variant="secondary">
                        {generatedText.split(' ').length} words
                      </Badge>
                      <Badge variant="secondary">
                        {generatedText.length} characters
                      </Badge>
                      <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                        {aiModels.find(m => m.id === selectedModel)?.name}
                      </Badge>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                    <div className="text-center">
                      <Sparkles className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>Your generated content will appear here</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
