# System Architecture

Comprehensive overview of the getintheq.space system architecture, design decisions, technology stack, and architectural patterns. This document serves as the technical foundation for understanding the system's structure and evolution.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Technology Stack](#technology-stack)
- [System Components](#system-components)
- [Data Flow Architecture](#data-flow-architecture)
- [Security Architecture](#security-architecture)
- [Performance Architecture](#performance-architecture)
- [Deployment Architecture](#deployment-architecture)
- [Design Decisions](#design-decisions)
- [Scalability Considerations](#scalability-considerations)
- [Future Architecture](#future-architecture)

## Architecture Overview

The getintheq.space platform follows a **modern full-stack architecture** built on Next.js 14 with emphasis on:

- **Monolithic Simplicity**: Single codebase for rapid development and deployment
- **Type Safety**: End-to-end TypeScript with strict mode
- **Security First**: Comprehensive security layers and best practices
- **Performance Optimized**: Built-in optimizations and monitoring
- **Developer Experience**: Modern tooling and development workflow

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Browser[Web Browser]
        Mobile[Mobile App]
        API_Client[API Clients]
    end

    subgraph "CDN/Edge Layer"
        Vercel_Edge[Vercel Edge Network]
        Cache[Static Asset Cache]
    end

    subgraph "Application Layer"
        NextJS[Next.js 14 App Router]
        Middleware[Security Middleware]
        API[API Routes]
        SSR[Server-Side Rendering]
    end

    subgraph "Service Layer"
        Auth[Authentication Service]
        Blog[Blog Service]
        Contact[Contact Service]
        Analytics[Analytics Service]
        GitHub_API[GitHub Integration]
        Email[Email Service]
    end

    subgraph "Data Layer"
        PostgreSQL[(PostgreSQL Database)]
        Drizzle[Drizzle ORM]
        Redis[(Redis Cache)]
    end

    subgraph "External Services"
        GitHub[GitHub API]
        SMTP[SMTP Server]
        Monitoring[Monitoring Services]
    end

    Browser --> Vercel_Edge
    Mobile --> Vercel_Edge
    API_Client --> Vercel_Edge
    
    Vercel_Edge --> NextJS
    Cache --> NextJS
    
    NextJS --> Middleware
    Middleware --> API
    NextJS --> SSR
    
    API --> Auth
    API --> Blog
    API --> Contact
    API --> Analytics
    
    Auth --> Drizzle
    Blog --> Drizzle
    Contact --> Drizzle
    Analytics --> Drizzle
    
    Drizzle --> PostgreSQL
    
    GitHub_API --> GitHub
    Email --> SMTP
    Analytics --> Monitoring
    
    Redis -.-> Auth
    Redis -.-> Analytics
```

## Technology Stack

### Core Technologies

| Layer | Technology | Version | Purpose |
|-------|------------|---------|---------|
| **Framework** | Next.js | 14.x | Full-stack React framework |
| **Language** | TypeScript | 5.6.x | Type-safe development |
| **Database** | PostgreSQL | 16.x | Primary data storage |
| **ORM** | Drizzle ORM | 0.39.x | Type-safe database access |
| **Styling** | Tailwind CSS | 3.4.x | Utility-first CSS framework |
| **UI Components** | shadcn/ui | Latest | Accessible component library |
| **Authentication** | JWT + bcrypt | Latest | Secure authentication |
| **Validation** | Zod | 3.24.x | Schema validation |

### Development & Quality

| Category | Technology | Purpose |
|----------|------------|---------|
| **Package Manager** | npm | Dependency management |
| **Code Quality** | ESLint + Prettier | Code linting and formatting |
| **Git Hooks** | Husky + lint-staged | Pre-commit quality checks |
| **Testing** | Jest + React Testing Library | Unit and integration testing |
| **Type Checking** | TypeScript strict mode | Compile-time type safety |
| **Build Tool** | Next.js built-in | Optimized production builds |

### Infrastructure & Deployment

| Service | Technology | Purpose |
|---------|------------|---------|
| **Hosting** | Vercel | Application deployment and CDN |
| **Database** | Neon PostgreSQL | Managed PostgreSQL hosting |
| **Monitoring** | Built-in + External | Performance and error tracking |
| **Email** | SMTP/SendGrid | Transactional email delivery |
| **Version Control** | Git + GitHub | Source code management |

## System Components

### Frontend Components

```mermaid
graph TB
    subgraph "Next.js App Router"
        Pages[Page Components]
        Layouts[Layout Components]
        Components[UI Components]
        Hooks[Custom Hooks]
    end

    subgraph "UI Layer"
        shadcn[shadcn/ui Components]
        Tailwind[Tailwind CSS]
        Framer[Framer Motion]
    end

    subgraph "State Management"
        ReactQuery[@tanstack/react-query]
        ReactState[React State]
        Context[React Context]
    end

    Pages --> Layouts
    Layouts --> Components
    Components --> shadcn
    Components --> Hooks
    
    Hooks --> ReactQuery
    Hooks --> ReactState
    
    shadcn --> Tailwind
    Components --> Framer
    
    ReactQuery --> API_Layer[API Layer]
```

#### Component Architecture

- **Pages**: Route-based components using App Router
- **Layouts**: Shared layout components with nested routing
- **UI Components**: Reusable components following shadcn/ui patterns
- **Custom Hooks**: Business logic abstraction and state management

### Backend Components

```mermaid
graph TB
    subgraph "API Routes"
        Auth_API[Authentication APIs]
        Blog_API[Blog Management APIs]
        Contact_API[Contact Form APIs]
        Admin_API[Admin Dashboard APIs]
        GitHub_API[GitHub Integration APIs]
    end

    subgraph "Services"
        Auth_Service[AuthService]
        DB_Service[DatabaseService]
        Email_Service[EmailService]
        Analytics_Service[AnalyticsService]
    end

    subgraph "Middleware"
        Security[Security Middleware]
        RateLimit[Rate Limiting]
        Auth_Check[Authentication Check]
        CORS[CORS Handler]
    end

    subgraph "Data Access"
        Drizzle[Drizzle ORM]
        Schemas[Zod Schemas]
        Validation[Input Validation]
    end

    Auth_API --> Auth_Service
    Blog_API --> DB_Service
    Contact_API --> Email_Service
    Admin_API --> Analytics_Service
    
    Auth_Service --> Drizzle
    DB_Service --> Drizzle
    
    Security --> RateLimit
    RateLimit --> Auth_Check
    Auth_Check --> CORS
    
    Drizzle --> Schemas
    Schemas --> Validation
```

#### Service Layer Architecture

- **AuthService**: JWT token management, password hashing, user validation
- **DatabaseService**: Centralized database operations and query optimization
- **EmailService**: SMTP integration and email template management
- **AnalyticsService**: Performance tracking and usage analytics

### Database Architecture

```mermaid
erDiagram
    USERS {
        uuid id PK
        string username UK
        string password
        string email
        string role
        timestamp created_at
        timestamp updated_at
    }

    BLOG_POSTS {
        uuid id PK
        string title
        text content
        string excerpt
        string category
        string[] tags
        integer read_time
        string status
        string featured_image
        string meta_title
        string meta_description
        timestamp published_at
        string slug UK
        integer views
        timestamp created_at
        timestamp updated_at
    }

    PROJECTS {
        uuid id PK
        string title
        text description
        text long_description
        string[] technologies
        string category
        string status
        string github_url
        string demo_url
        string image_url
        string[] images
        boolean featured
        integer priority
        jsonb github_data
        integer views
        timestamp created_at
        timestamp updated_at
    }

    CONTACTS {
        uuid id PK
        string first_name
        string last_name
        string email
        string subject
        text message
        string status
        string ip_address
        string user_agent
        timestamp created_at
        timestamp updated_at
    }

    PLAYGROUND_TOOLS {
        uuid id PK
        string name
        text description
        string category
        string status
        string icon
        string endpoint
        string api_key
        jsonb config
        integer usage_count
        timestamp created_at
        timestamp updated_at
    }

    ANALYTICS {
        uuid id PK
        string path
        string title
        string referrer
        string user_agent
        string ip_address
        string country
        string device
        string browser
        string os
        string session_id
        timestamp timestamp
    }
```

#### Database Design Principles

- **UUID Primary Keys**: Globally unique identifiers for distributed systems
- **Soft Deletes**: Status fields instead of hard deletes for data integrity
- **Audit Trails**: Created/updated timestamps on all entities
- **Normalized Structure**: Proper relational design with foreign keys
- **Performance Indexes**: Strategic indexing on frequently queried fields

## Data Flow Architecture

### Request Processing Flow

```mermaid
sequenceDiagram
    participant Client
    participant CDN
    participant NextJS
    participant Middleware
    participant API
    participant Service
    participant Database

    Client->>CDN: HTTP Request
    CDN->>NextJS: Forward Request
    NextJS->>Middleware: Security Check
    Middleware->>Middleware: Rate Limiting
    Middleware->>Middleware: Authentication
    Middleware->>API: Route Request
    API->>API: Input Validation
    API->>Service: Business Logic
    Service->>Database: Data Operation
    Database-->>Service: Query Result
    Service-->>API: Processed Data
    API-->>NextJS: JSON Response
    NextJS-->>CDN: HTTP Response
    CDN-->>Client: Cached Response
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant NextJS
    participant AuthService
    participant Database

    User->>Browser: Login Credentials
    Browser->>NextJS: POST /api/auth/login
    NextJS->>AuthService: Validate Credentials
    AuthService->>Database: Query User
    Database-->>AuthService: User Data
    AuthService->>AuthService: Verify Password
    AuthService->>AuthService: Generate JWT
    AuthService-->>NextJS: JWT Token
    NextJS->>Browser: Set HttpOnly Cookie
    Browser-->>User: Login Success
    
    Note over Browser,NextJS: Subsequent requests include cookie
    Browser->>NextJS: API Request + Cookie
    NextJS->>AuthService: Verify JWT
    AuthService-->>NextJS: User Context
    NextJS-->>Browser: Authorized Response
```

### Content Management Flow

```mermaid
graph LR
    subgraph "Content Creation"
        Admin[Admin User]
        Editor[Rich Text Editor]
        Preview[Content Preview]
    end

    subgraph "Content Processing"
        Validation[Input Validation]
        Sanitization[Content Sanitization]
        SEO[SEO Processing]
    end

    subgraph "Content Storage"
        Database[(PostgreSQL)]
        Cache[Response Cache]
        CDN[Static Assets]
    end

    subgraph "Content Delivery"
        SSR[Server-Side Rendering]
        Client[Client Rendering]
        Search[Search Indexing]
    end

    Admin --> Editor
    Editor --> Preview
    Preview --> Validation
    Validation --> Sanitization
    Sanitization --> SEO
    SEO --> Database
    Database --> Cache
    Database --> CDN
    Cache --> SSR
    SSR --> Client
    Database --> Search
```

## Security Architecture

### Security Layers

```mermaid
graph TB
    subgraph "Network Security"
        HTTPS[HTTPS/TLS 1.3]
        CDN_Protection[CDN DDoS Protection]
        Rate_Limiting[Rate Limiting]
    end

    subgraph "Application Security"
        CSP[Content Security Policy]
        CORS[CORS Configuration]
        Security_Headers[Security Headers]
        Input_Validation[Input Validation]
    end

    subgraph "Authentication Security"
        JWT[JWT Tokens]
        Password_Hashing[bcrypt Hashing]
        Session_Management[Secure Sessions]
        RBAC[Role-Based Access]
    end

    subgraph "Data Security"
        Encryption[Data Encryption]
        SQL_Injection[SQL Injection Prevention]
        XSS_Protection[XSS Protection]
        CSRF_Protection[CSRF Protection]
    end

    HTTPS --> CSP
    CDN_Protection --> CORS
    Rate_Limiting --> Security_Headers
    
    CSP --> JWT
    CORS --> Password_Hashing
    Security_Headers --> Session_Management
    Input_Validation --> RBAC
    
    JWT --> Encryption
    Password_Hashing --> SQL_Injection
    Session_Management --> XSS_Protection
    RBAC --> CSRF_Protection
```

### Security Implementation

#### Content Security Policy
```typescript
const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-eval'", "'unsafe-inline'"],
  'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
  'font-src': ["'self'", 'https://fonts.gstatic.com'],
  'img-src': ["'self'", 'data:', 'blob:', 'https://opengraph.githubassets.com'],
  'connect-src': ["'self'", 'https://api.github.com'],
  'frame-ancestors': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"]
};
```

#### Rate Limiting Strategy
```typescript
const rateLimitConfig = {
  auth: { maxRequests: 5, windowMs: 15 * 60 * 1000 },
  api: { maxRequests: 100, windowMs: 15 * 60 * 1000 },
  contact: { maxRequests: 3, windowMs: 60 * 60 * 1000 },
  admin: { maxRequests: 50, windowMs: 15 * 60 * 1000 }
};
```

## Performance Architecture

### Performance Optimization Layers

```mermaid
graph TB
    subgraph "Frontend Performance"
        Code_Splitting[Code Splitting]
        Tree_Shaking[Tree Shaking]
        Image_Optimization[Image Optimization]
        Font_Optimization[Font Optimization]
    end

    subgraph "Caching Strategy"
        Browser_Cache[Browser Caching]
        CDN_Cache[CDN Caching]
        API_Cache[API Response Caching]
        Database_Cache[Query Caching]
    end

    subgraph "Monitoring"
        Core_Vitals[Core Web Vitals]
        Performance_Metrics[Performance Metrics]
        Error_Tracking[Error Tracking]
        Uptime_Monitoring[Uptime Monitoring]
    end

    Code_Splitting --> Browser_Cache
    Tree_Shaking --> CDN_Cache
    Image_Optimization --> API_Cache
    Font_Optimization --> Database_Cache
    
    Browser_Cache --> Core_Vitals
    CDN_Cache --> Performance_Metrics
    API_Cache --> Error_Tracking
    Database_Cache --> Uptime_Monitoring
```

### Performance Targets

| Metric | Target | Measurement |
|--------|---------|-------------|
| **First Contentful Paint** | < 1.8s | Core Web Vitals |
| **Largest Contentful Paint** | < 2.5s | Core Web Vitals |
| **Cumulative Layout Shift** | < 0.1 | Core Web Vitals |
| **First Input Delay** | < 100ms | Core Web Vitals |
| **API Response Time** | < 200ms | 95th percentile |
| **Database Query Time** | < 50ms | Average |
| **Uptime** | > 99.9% | Monthly SLA |

## Deployment Architecture

### Production Deployment

```mermaid
graph TB
    subgraph "Source Control"
        GitHub[GitHub Repository]
        Branches[Feature Branches]
        Main[Main Branch]
    end

    subgraph "CI/CD Pipeline"
        Actions[GitHub Actions]
        Tests[Automated Tests]
        Build[Production Build]
        Deploy[Deployment]
    end

    subgraph "Hosting Infrastructure"
        Vercel[Vercel Platform]
        Edge[Edge Functions]
        CDN[Global CDN]
        SSL[SSL Certificates]
    end

    subgraph "Database Infrastructure"
        Neon[Neon PostgreSQL]
        Backup[Automated Backups]
        Replication[Read Replicas]
    end

    subgraph "External Services"
        GitHub_API[GitHub API]
        Email_SMTP[Email SMTP]
        Monitoring_Services[Monitoring]
    end

    GitHub --> Actions
    Branches --> Main
    Main --> Tests
    Tests --> Build
    Build --> Deploy
    Deploy --> Vercel
    
    Vercel --> Edge
    Vercel --> CDN
    Vercel --> SSL
    
    Vercel --> Neon
    Neon --> Backup
    Neon --> Replication
    
    Vercel --> GitHub_API
    Vercel --> Email_SMTP
    Vercel --> Monitoring_Services
```

### Environment Strategy

| Environment | Purpose | Database | Deployment |
|-------------|---------|----------|------------|
| **Development** | Local development | Local PostgreSQL | `npm run dev` |
| **Preview** | Feature testing | Neon branch database | Vercel preview |
| **Production** | Live application | Neon production | Vercel production |

## Design Decisions

### Technology Choices

#### 1. Next.js 14 with App Router
**Decision**: Use Next.js 14 App Router instead of Pages Router or other frameworks

**Rationale**:
- Modern React patterns with Server Components
- Built-in performance optimizations
- Integrated deployment with Vercel
- Strong TypeScript support
- Comprehensive tooling ecosystem

**Trade-offs**:
- Learning curve for App Router patterns
- Some ecosystem libraries may lag behind
- Framework lock-in considerations

#### 2. Drizzle ORM over Prisma
**Decision**: Use Drizzle ORM for database access

**Rationale**:
- Better TypeScript integration and type inference
- More control over SQL queries
- Lightweight with minimal runtime overhead
- Excellent developer experience
- Better performance characteristics

**Trade-offs**:
- Smaller ecosystem compared to Prisma
- More manual schema management
- Less advanced introspection tooling

#### 3. Monolithic Architecture
**Decision**: Single Next.js application instead of microservices

**Rationale**:
- Simpler deployment and development workflow
- Lower operational complexity
- Better performance with co-located services
- Easier debugging and monitoring
- Cost-effective for current scale

**Trade-offs**:
- Less flexible scaling of individual components
- Larger deployment artifacts
- Technology coupling across features

#### 4. JWT over Session-Based Authentication
**Decision**: JWT tokens with HTTP-only cookies

**Rationale**:
- Stateless authentication for better scalability
- Works well with CDN and edge computing
- No server-side session storage required
- Better performance for distributed systems

**Trade-offs**:
- Cannot revoke tokens before expiration
- Larger request headers
- More complex token refresh logic

### Architectural Patterns

#### 1. Service Layer Pattern
**Implementation**: Centralized business logic in service classes

```typescript
// Service layer encapsulates business logic
export class AuthService {
  static async login(username: string, password: string) {
    // Business logic for authentication
  }
}
```

#### 2. Repository Pattern (via ORM)
**Implementation**: Drizzle ORM abstracts database access

```typescript
// Database access through typed ORM
const user = await db.select().from(users).where(eq(users.username, username));
```

#### 3. Middleware Pattern
**Implementation**: Request processing pipeline

```typescript
// Middleware chain for request processing
export async function middleware(request: NextRequest) {
  const response = await security(request);
  if (response) return response;
  
  return await rateLimit(request);
}
```

#### 4. Factory Pattern
**Implementation**: Configuration and service creation

```typescript
// Factory for creating configured services
export function createDatabaseService(config: DatabaseConfig) {
  return new DatabaseService(config);
}
```

## Scalability Considerations

### Current Scale Targets

| Metric | Current Target | Future Target |
|--------|----------------|---------------|
| **Concurrent Users** | 1,000 | 10,000 |
| **Daily Page Views** | 50,000 | 500,000 |
| **API Requests/min** | 1,000 | 10,000 |
| **Database Connections** | 20 | 100 |
| **Storage** | 10 GB | 100 GB |

### Scaling Strategies

#### 1. Horizontal Scaling
- **Current**: Single Vercel deployment
- **Future**: Multi-region deployment with edge functions
- **Database**: Read replicas for query scaling

#### 2. Caching Strategies
```mermaid
graph LR
    subgraph "Caching Layers"
        Browser[Browser Cache]
        CDN[CDN Cache]
        Application[Application Cache]
        Database[Database Cache]
    end

    Browser --> CDN
    CDN --> Application
    Application --> Database
    
    Browser -.->|Cache Hit| User[User Response]
    CDN -.->|Cache Hit| User
    Application -.->|Cache Hit| User
```

#### 3. Database Optimization
- Connection pooling with Neon
- Query optimization and indexing
- Read replica implementation
- Potential Redis integration for sessions

#### 4. Performance Monitoring
```typescript
// Performance monitoring integration
export class PerformanceMonitor {
  static recordMetrics(metrics: PerformanceMetrics) {
    // Send to monitoring service
    // Track Core Web Vitals
    // Monitor API response times
  }
}
```

## Future Architecture

### Potential Enhancements

#### 1. Microservices Evolution
```mermaid
graph TB
    subgraph "Current Monolith"
        NextJS[Next.js Application]
        API[Integrated APIs]
        Services[Shared Services]
    end

    subgraph "Future Microservices"
        Gateway[API Gateway]
        Auth_Service[Auth Microservice]
        Blog_Service[Blog Microservice]
        Analytics_Service[Analytics Microservice]
        Notification_Service[Notification Service]
    end

    NextJS -.->|Migration Path| Gateway
    API -.->|Extract| Auth_Service
    API -.->|Extract| Blog_Service
    Services -.->|Extract| Analytics_Service
    Services -.->|Extract| Notification_Service
```

#### 2. Enhanced Caching
- Redis integration for session management
- Advanced CDN configuration
- Database query result caching
- Real-time invalidation strategies

#### 3. Event-Driven Architecture
```typescript
// Event-driven patterns for future implementation
export interface DomainEvent {
  type: string;
  payload: unknown;
  timestamp: Date;
  aggregateId: string;
}

export class EventBus {
  static async publish(event: DomainEvent) {
    // Publish to event stream
  }
}
```

#### 4. Advanced Analytics
- Real-time analytics dashboard
- Machine learning insights
- User behavior tracking
- Performance prediction

### Migration Strategies

#### 1. Database Migration
- Schema versioning with Drizzle migrations
- Blue-green deployment for major changes
- Rollback procedures and data safety

#### 2. Service Extraction
- Gradual extraction of bounded contexts
- API versioning for backward compatibility
- Progressive feature flag deployment

#### 3. Infrastructure Evolution
- Container deployment options
- Kubernetes orchestration potential
- Multi-cloud deployment strategies

---

**Architecture Version**: 1.0.0  
**Last Updated**: 2024-08-14  
**Next Review**: 2024-11-14  
**Architecture Owner**: Development Team