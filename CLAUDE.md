# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start Next.js development server on port 3000
- `npm run build` - Build production application
- `npm run start` - Start production server
- `npm run lint` - Run ESLint for code quality
- `npm run check` - TypeScript type checking
- `npm run db:push` - Push database schema changes using Drizzle Kit

## Project Architecture

### Tech Stack
- **Frontend**: Next.js 14 with App Router, React 18, TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components, CSS variables for theming
- **Database**: PostgreSQL with Dr<PERSON>zle ORM, Neon Database (serverless)
- **State Management**: TanStack React Query for server state
- **Validation**: Zod schemas integrated with Drizzle
- **Authentication**: Prepared schema but not implemented (portfolio site)

### Directory Structure
```
app/                    # Next.js App Router pages and components
├── api/               # API routes for GitHub, blog, projects
├── components/        # React components
│   ├── ui/           # shadcn/ui base components
│   └── playground/   # Feature-specific components
├── lib/              # Utilities, constants, query client
└── hooks/            # Custom React hooks

shared/                # Shared TypeScript schemas and types
server/                # Express.js backend (legacy, being migrated)
```

### Key Architectural Patterns

**Component Architecture**: Uses shadcn/ui patterns with Radix UI primitives. All UI components use `forwardRef` and export both component and variant functions. Follow the existing component structure in `app/components/ui/`.

**Path Aliases**: 
- `@/*` → `./app/*`
- `@/components/*` → `./app/components/*`
- `@/lib/*` → `./app/lib/*`
- `@/hooks/*` → `./app/hooks/*`
- `@shared/*` → `./shared/*`

**Database Schema**: Centralized in `shared/schema.ts` using Drizzle ORM with Zod validation schemas. Export both insert schemas and types for each table.

**API Routes**: Next.js App Router API routes in `app/api/`. Return `NextResponse.json()` with appropriate status codes. Handle errors with try/catch blocks.

**Theming**: Uses CSS custom properties with light/dark mode support. Theme provider wraps the app in `layout.tsx`.

### Environment Variables Required
- `DATABASE_URL` - PostgreSQL connection string
- `GITHUB_TOKEN` - GitHub API token for stats/repos
- `GITHUB_USERNAME` - GitHub username for API calls

### Code Style Guidelines
- **TypeScript**: Strict mode enabled, use proper typing
- **Components**: PascalCase for components, kebab-case for files
- **Imports**: Use path aliases, group external before internal imports
- **Styling**: Use `cn()` utility from `@/lib/utils` for className merging
- **Database**: Use Drizzle ORM patterns, export schemas and types together
- **Forms**: React Hook Form with Zod validation
- **State**: React Query for server state, React hooks for component state

### Migration Status
The project is transitioning from a legacy Express.js backend to Next.js App Router. The `server/` directory contains legacy code that's being migrated to `app/api/` routes. Use Next.js patterns for new features.