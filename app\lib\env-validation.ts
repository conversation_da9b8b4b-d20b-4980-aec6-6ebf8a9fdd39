import { validateEnvironment } from './security';

/**
 * Environment validation setup
 * This should be called at application startup
 */
export function initializeEnvironmentValidation(): void {
  try {
    console.log('🔍 Validating environment variables...');
    
    const env = validateEnvironment();
    
    console.log('✅ Environment validation successful');
    
    // Log non-sensitive configuration in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📋 Environment Configuration:', {
        nodeEnv: env.NODE_ENV,
        hasDatabase: !!env.DATABASE_URL,
        hasJwtSecret: !!env.JWT_SECRET,
        hasSessionSecret: !!env.SESSION_SECRET,
        hasGithubIntegration: !!(env.GITHUB_TOKEN && env.GITHUB_USERNAME),
        hasEmailConfig: !!(env.SMTP_HOST && env.SMTP_USER),
        hasOpenAI: !!env.OPENAI_API_KEY,
        hasAnthropic: !!env.ANTHROPIC_API_KEY,
        rateLimitConfig: {
          maxRequests: env.RATE_LIMIT_MAX_REQUESTS || 100,
          windowMs: env.RATE_LIMIT_WINDOW_MS || 900000,
        },
      });
    }
    
    // Warn about missing optional configurations
    const warnings: string[] = [];
    
    if (!env.GITHUB_TOKEN || !env.GITHUB_USERNAME) {
      warnings.push('GitHub integration not configured - repository features will be disabled');
    }
    
    if (!env.SMTP_HOST || !env.SMTP_USER || !env.SMTP_PASS) {
      warnings.push('Email service not configured - contact form emails will be logged only');
    }
    
    if (!env.OPENAI_API_KEY && !env.ANTHROPIC_API_KEY) {
      warnings.push('No AI service API keys configured - AI playground features will be disabled');
    }
    
    if (!env.NEXT_PUBLIC_GA_ID) {
      warnings.push('Google Analytics not configured - analytics tracking disabled');
    }
    
    if (!env.SENTRY_DSN) {
      warnings.push('Sentry error tracking not configured');
    }
    
    if (warnings.length > 0) {
      console.warn('⚠️  Configuration warnings:');
      warnings.forEach(warning => console.warn(`   - ${warning}`));
    }
    
    // Production-specific validations
    if (env.NODE_ENV === 'production') {
      const productionErrors: string[] = [];
      
      if (env.JWT_SECRET === 'your-super-secret-jwt-key-change-in-production') {
        productionErrors.push('JWT_SECRET is using default value - this is a security risk!');
      }
      
      if (env.SESSION_SECRET === 'your-session-secret-key') {
        productionErrors.push('SESSION_SECRET is using default value - this is a security risk!');
      }
      
      if (env.JWT_SECRET.length < 32) {
        productionErrors.push('JWT_SECRET is too short for production use');
      }
      
      if (env.SESSION_SECRET.length < 32) {
        productionErrors.push('SESSION_SECRET is too short for production use');
      }
      
      if (!env.DATABASE_URL.startsWith('postgresql://')) {
        productionErrors.push('DATABASE_URL should use PostgreSQL in production');
      }
      
      if (productionErrors.length > 0) {
        console.error('🚨 Production security errors:');
        productionErrors.forEach(error => console.error(`   - ${error}`));
        throw new Error('Production environment validation failed - check security configuration');
      }
      
      console.log('🛡️  Production security validation passed');
    }
    
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    
    if (process.env.NODE_ENV === 'production') {
      // In production, fail fast if environment is invalid
      process.exit(1);
    } else {
      // In development, warn but continue
      console.warn('⚠️  Continuing with invalid environment (development mode)');
      console.warn('   Please check your .env.local file and ensure all required variables are set');
    }
  }
}

/**
 * Runtime environment checks
 */
export function checkRuntimeEnvironment(): {
  isProduction: boolean;
  hasRequiredSecrets: boolean;
  hasOptionalFeatures: string[];
  missingFeatures: string[];
} {
  const isProduction = process.env.NODE_ENV === 'production';
  
  const requiredSecrets = [
    'DATABASE_URL',
    'JWT_SECRET',
    'SESSION_SECRET'
  ];
  
  const hasRequiredSecrets = requiredSecrets.every(key => 
    process.env[key] && process.env[key] !== ''
  );
  
  const optionalFeatures = [
    { name: 'GitHub Integration', check: () => !!(process.env.GITHUB_TOKEN && process.env.GITHUB_USERNAME) },
    { name: 'Email Service', check: () => !!(process.env.SMTP_HOST && process.env.SMTP_USER) },
    { name: 'OpenAI Integration', check: () => !!process.env.OPENAI_API_KEY },
    { name: 'Anthropic Integration', check: () => !!process.env.ANTHROPIC_API_KEY },
    { name: 'Google Analytics', check: () => !!process.env.NEXT_PUBLIC_GA_ID },
    { name: 'Sentry Error Tracking', check: () => !!process.env.SENTRY_DSN },
  ];
  
  const hasOptionalFeatures = optionalFeatures
    .filter(feature => feature.check())
    .map(feature => feature.name);
    
  const missingFeatures = optionalFeatures
    .filter(feature => !feature.check())
    .map(feature => feature.name);
  
  return {
    isProduction,
    hasRequiredSecrets,
    hasOptionalFeatures,
    missingFeatures,
  };
}

/**
 * Get sanitized environment info for client-side use
 */
export function getPublicEnvironmentInfo(): {
  nodeEnv: string;
  hasGithubIntegration: boolean;
  hasAnalytics: boolean;
  apiUrl: string;
} {
  return {
    nodeEnv: process.env.NODE_ENV || 'development',
    hasGithubIntegration: !!(process.env.GITHUB_TOKEN && process.env.GITHUB_USERNAME),
    hasAnalytics: !!process.env.NEXT_PUBLIC_GA_ID,
    apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  };
}

/**
 * Development helper to check environment status
 */
export function logEnvironmentStatus(): void {
  if (process.env.NODE_ENV !== 'development') return;
  
  const status = checkRuntimeEnvironment();
  
  console.log('\n📊 Environment Status:');
  console.log(`   Production Mode: ${status.isProduction ? '✅' : '❌'}`);
  console.log(`   Required Secrets: ${status.hasRequiredSecrets ? '✅' : '❌'}`);
  
  if (status.hasOptionalFeatures.length > 0) {
    console.log('   Available Features:');
    status.hasOptionalFeatures.forEach(feature => {
      console.log(`     ✅ ${feature}`);
    });
  }
  
  if (status.missingFeatures.length > 0) {
    console.log('   Missing Features:');
    status.missingFeatures.forEach(feature => {
      console.log(`     ❌ ${feature}`);
    });
  }
  
  console.log('');
}