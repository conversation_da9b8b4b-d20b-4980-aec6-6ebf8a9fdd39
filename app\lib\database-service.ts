import { pool, DatabaseService as BaseService } from './database';

export class DatabaseService extends BaseService {
  // Enhanced project methods
  static async createProject(projectData: any) {
    const query = `
      INSERT INTO projects (
        title, description, long_description, github_url, demo_url, 
        image_url, technologies, category, status, featured, sort_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;
    
    const values = [
      projectData.title,
      projectData.description,
      projectData.long_description,
      projectData.github_url,
      projectData.demo_url,
      projectData.image_url,
      JSON.stringify(projectData.technologies || []),
      projectData.category,
      projectData.status || 'draft',
      projectData.featured || false,
      projectData.sort_order || 0
    ];
    
    const result = await this.query(query, values);
    return result.rows[0];
  }

  static async updateProject(id: number, projectData: any) {
    const query = `
      UPDATE projects 
      SET title = $1, description = $2, long_description = $3, github_url = $4, 
          demo_url = $5, image_url = $6, technologies = $7, category = $8, 
          status = $9, featured = $10, updated_at = CURRENT_TIMESTAMP
      WHERE id = $11
      RETURNING *
    `;
    
    const values = [
      projectData.title,
      projectData.description,
      projectData.long_description,
      projectData.github_url,
      projectData.demo_url,
      projectData.image_url,
      JSON.stringify(projectData.technologies || []),
      projectData.category,
      projectData.status,
      projectData.featured,
      id
    ];
    
    const result = await this.query(query, values);
    return result.rows[0];
  }

  static async deleteProject(id: number) {
    const query = 'DELETE FROM projects WHERE id = $1 RETURNING id';
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  // Enhanced blog methods
  static async createBlogPost(postData: any) {
    const slug = postData.slug || postData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    
    const wordCount = postData.content.split(/\s+/).length;
    const reading_time = Math.ceil(wordCount / 200);
    
    const query = `
      INSERT INTO blog_posts (
        title, slug, excerpt, content, featured_image, status, author_id,
        category_id, tags, meta_title, meta_description, reading_time
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `;
    
    const values = [
      postData.title,
      slug,
      postData.excerpt,
      postData.content,
      postData.featured_image,
      postData.status || 'draft',
      postData.author_id || 1,
      postData.category_id,
      JSON.stringify(postData.tags || []),
      postData.meta_title || postData.title,
      postData.meta_description || postData.excerpt,
      reading_time
    ];
    
    const result = await this.query(query, values);
    
    // Set published_at if publishing
    if (postData.status === 'published') {
      const updateQuery = `
        UPDATE blog_posts 
        SET published_at = CURRENT_TIMESTAMP 
        WHERE id = $1
        RETURNING *
      `;
      const updatedResult = await this.query(updateQuery, [result.rows[0].id]);
      return updatedResult.rows[0];
    }
    
    return result.rows[0];
  }

  static async updateBlogPost(id: number, postData: any) {
    const reading_time = postData.content 
      ? Math.ceil(postData.content.split(/\s+/).length / 200)
      : undefined;
    
    let query = `
      UPDATE blog_posts 
      SET title = $1, excerpt = $2, content = $3, featured_image = $4, 
          status = $5, category_id = $6, tags = $7, meta_title = $8, 
          meta_description = $9, updated_at = CURRENT_TIMESTAMP
    `;
    
    let values = [
      postData.title,
      postData.excerpt,
      postData.content,
      postData.featured_image,
      postData.status,
      postData.category_id,
      JSON.stringify(postData.tags || []),
      postData.meta_title,
      postData.meta_description
    ];
    
    if (reading_time) {
      query += `, reading_time = $${values.length + 1}`;
      values.push(reading_time);
    }
    
    if (postData.status === 'published') {
      query += `, published_at = COALESCE(published_at, CURRENT_TIMESTAMP)`;
    }
    
    query += ` WHERE id = $${values.length + 1} RETURNING *`;
    values.push(id);
    
    const result = await this.query(query, values);
    return result.rows[0];
  }

  static async deleteBlogPost(id: number) {
    const query = 'DELETE FROM blog_posts WHERE id = $1 RETURNING id';
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  // Playground project methods
  static async createPlaygroundProject(projectData: any) {
    const query = `
      INSERT INTO playground_projects (
        title, description, project_type, icon, gradient, tags, 
        configuration, status, featured, sort_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;
    
    const values = [
      projectData.title,
      projectData.description,
      projectData.project_type,
      projectData.icon,
      projectData.gradient,
      JSON.stringify(projectData.tags || []),
      JSON.stringify(projectData.configuration || {}),
      projectData.status || 'active',
      projectData.featured || false,
      projectData.sort_order || 0
    ];
    
    const result = await this.query(query, values);
    return result.rows[0];
  }

  static async updatePlaygroundProject(id: number, projectData: any) {
    const query = `
      UPDATE playground_projects 
      SET title = $1, description = $2, project_type = $3, icon = $4, 
          gradient = $5, tags = $6, configuration = $7, status = $8, 
          featured = $9, updated_at = CURRENT_TIMESTAMP
      WHERE id = $10
      RETURNING *
    `;
    
    const values = [
      projectData.title,
      projectData.description,
      projectData.project_type,
      projectData.icon,
      projectData.gradient,
      JSON.stringify(projectData.tags || []),
      JSON.stringify(projectData.configuration || {}),
      projectData.status,
      projectData.featured,
      id
    ];
    
    const result = await this.query(query, values);
    return result.rows[0];
  }

  static async incrementPlaygroundUsage(id: number) {
    const query = `
      UPDATE playground_projects 
      SET usage_count = usage_count + 1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING usage_count
    `;
    
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  // Contact methods
  static async createContactSubmission(contactData: any) {
    const query = `
      INSERT INTO contact_submissions (
        name, email, subject, message, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;
    
    const values = [
      contactData.name,
      contactData.email,
      contactData.subject,
      contactData.message,
      contactData.ip_address,
      contactData.user_agent
    ];
    
    const result = await this.query(query, values);
    return result.rows[0];
  }

  static async getContactSubmissions(filters?: { status?: string; limit?: number }) {
    let query = 'SELECT * FROM contact_submissions WHERE 1=1';
    const params: any[] = [];
    let paramCount = 0;

    if (filters?.status) {
      paramCount++;
      query += ` AND status = $${paramCount}`;
      params.push(filters.status);
    }

    query += ' ORDER BY created_at DESC';

    if (filters?.limit) {
      paramCount++;
      query += ` LIMIT $${paramCount}`;
      params.push(filters.limit);
    }

    const result = await this.query(query, params);
    return result.rows;
  }

  static async updateContactStatus(id: number, status: string) {
    const query = `
      UPDATE contact_submissions 
      SET status = $1 
      WHERE id = $2
      RETURNING *
    `;
    
    const result = await this.query(query, [status, id]);
    return result.rows[0];
  }

  // Settings methods
  static async getSetting(key: string) {
    const query = 'SELECT * FROM settings WHERE key = $1';
    const result = await this.query(query, [key]);
    return result.rows[0];
  }

  static async updateSetting(key: string, value: string, type: string = 'string') {
    const query = `
      INSERT INTO settings (key, value, type, updated_at) 
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      ON CONFLICT (key) 
      DO UPDATE SET value = $2, type = $3, updated_at = CURRENT_TIMESTAMP
      RETURNING *
    `;
    
    const result = await this.query(query, [key, value, type]);
    return result.rows[0];
  }

  static async getAllSettings() {
    const query = 'SELECT * FROM settings ORDER BY key';
    const result = await this.query(query);
    return result.rows;
  }

  // Dashboard stats
  static async getDashboardStats() {
    const queries = {
      projects: 'SELECT COUNT(*) as total, COUNT(*) FILTER (WHERE status = \'active\') as active, COUNT(*) FILTER (WHERE featured = true) as featured FROM projects',
      blog: 'SELECT COUNT(*) as total, COUNT(*) FILTER (WHERE status = \'published\') as published, COUNT(*) FILTER (WHERE status = \'draft\') as drafts FROM blog_posts',
      playground: 'SELECT COUNT(*) as total, COUNT(*) FILTER (WHERE status = \'active\') as active, SUM(usage_count) as total_usage FROM playground_projects',
      contact: 'SELECT COUNT(*) as total, COUNT(*) FILTER (WHERE status = \'new\') as unread FROM contact_submissions',
      analytics: `
        SELECT 
          COUNT(*) as views,
          COUNT(DISTINCT ip_address) as visitors
        FROM page_views 
        WHERE created_at >= NOW() - INTERVAL '30 days'
      `
    };

    const results = {};
    for (const [key, query] of Object.entries(queries)) {
      const result = await this.query(query);
      results[key] = result.rows[0];
    }

    return results;
  }
}
