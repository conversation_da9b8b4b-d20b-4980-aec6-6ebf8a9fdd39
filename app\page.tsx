'use client'

import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, useEffect, useState } from "react";
import { <PERSON>rkles, Zap, Rocket, Target, Globe, Cpu } from "lucide-react";

import { AboutSection } from "@/components/about-section";
import { BlogSection } from "@/components/blog-section";
import { ContactSection } from "@/components/contact-section";
import { ExperienceSection } from "@/components/experience-section";
import { Footer } from "@/components/footer";
import { HeroSection } from "@/components/hero-section";
import { MorphingBackground } from "@/components/morphing-background";
import { Navbar } from "@/components/navbar";
import { ParticleBackground } from "@/components/particle-background";
import { ProjectsSection } from "@/components/projects-section";
import TestimonialsSection from "@/components/testimonials-section";

// Enhanced floating elements component
const FloatingElements = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const elements = [
    { icon: Sparkles, x: '10%', y: '20%', delay: 0, color: 'text-purple-500' },
    { icon: Zap, x: '85%', y: '15%', delay: 1, color: 'text-yellow-500' },
    { icon: Rocket, x: '15%', y: '70%', delay: 2, color: 'text-blue-500' },
    { icon: Target, x: '90%', y: '80%', delay: 3, color: 'text-red-500' },
    { icon: Globe, x: '5%', y: '50%', delay: 4, color: 'text-green-500' },
    { icon: Cpu, x: '95%', y: '45%', delay: 5, color: 'text-indigo-500' },
  ];

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {elements.map((element, index) => {
        const Icon = element.icon;
        const offsetX = (mousePosition.x - window.innerWidth / 2) * 0.01;
        const offsetY = (mousePosition.y - window.innerHeight / 2) * 0.01;
        
        return (
          <motion.div
            key={index}
            className={`absolute ${element.color} opacity-20`}
            style={{
              left: element.x,
              top: element.y,
            }}
            initial={{ 
              opacity: 0, 
              scale: 0,
              rotate: 0 
            }}
            animate={{ 
              opacity: 0.2, 
              scale: [1, 1.2, 1],
              rotate: 360,
              x: offsetX * (index + 1),
              y: offsetY * (index + 1),
            }}
            transition={{
              duration: 2,
              delay: element.delay * 0.5,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          >
            <Icon className="w-8 h-8 md:w-12 md:h-12" />
          </motion.div>
        );
      })}
    </div>
  );
};

// Advanced scroll progress indicator
const ScrollProgress = () => {
  const { scrollYProgress } = useScroll();
  const scaleX = useTransform(scrollYProgress, [0, 1], [0, 1]);

  return (
    <motion.div
      className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 origin-left z-50"
      style={{ scaleX }}
    />
  );
};

// Animated section wrapper
const AnimatedSection = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [50, -50]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  return (
    <motion.div
      ref={ref}
      style={{ y, opacity }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Enhanced gradient background
const EnhancedBackground = () => {
  const { scrollYProgress } = useScroll();
  const backgroundColor = useTransform(
    scrollYProgress,
    [0, 0.2, 0.4, 0.6, 0.8, 1],
    [
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    ]
  );

  return (
    <motion.div
      className="fixed inset-0 opacity-10 z-0"
      style={{ background: backgroundColor }}
    />
  );
};

export default function Home() {
  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div ref={containerRef} className="min-h-screen relative">
      {/* Enhanced Backgrounds */}
      <EnhancedBackground />
      <ParticleBackground />
      <MorphingBackground />
      <FloatingElements />
      
      {/* Scroll Progress */}
      <ScrollProgress />
      
      {/* Main Content */}
      <motion.div 
        className="relative z-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5, ease: "easeOut" }}
      >
        <Navbar />
        
        <main className="relative">
          {/* Hero Section with enhanced entrance */}
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ 
              duration: 1.2, 
              ease: "easeOut",
              staggerChildren: 0.2 
            }}
          >
            <HeroSection />
          </motion.div>

          {/* Animated Sections */}
          <AnimatedSection>
            <AboutSection />
          </AnimatedSection>

          <AnimatedSection>
            <ExperienceSection />
          </AnimatedSection>

          <AnimatedSection>
            <ProjectsSection />
          </AnimatedSection>

          <AnimatedSection>
            <BlogSection />
          </AnimatedSection>

          <AnimatedSection>
            <TestimonialsSection />
          </AnimatedSection>

          <AnimatedSection>
            <ContactSection />
          </AnimatedSection>
        </main>
        
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Footer />
        </motion.div>
      </motion.div>
    </div>
  )
}
