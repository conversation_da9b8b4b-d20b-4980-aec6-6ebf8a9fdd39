import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { AuthService } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      page_path, 
      load_time, 
      dom_content_loaded, 
      first_paint, 
      largest_contentful_paint,
      session_id 
    } = body;

    if (!page_path) {
      return NextResponse.json(
        { error: 'Page path is required' },
        { status: 400 }
      );
    }

    // Get client IP
    const ip_address = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown';

    // Track performance metrics
    const query = `
      INSERT INTO performance_metrics (
        page_path, load_time, dom_content_loaded, first_paint, 
        largest_contentful_paint, ip_address, user_agent, session_id
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id
    `;

    const values = [
      page_path,
      load_time,
      dom_content_loaded,
      first_paint,
      largest_contentful_paint,
      ip_address,
      request.headers.get('user-agent'),
      session_id
    ];

    const result = await DatabaseService.query(query, values);

    return NextResponse.json({ success: true, id: result.rows[0].id });
  } catch (error) {
    console.error('Error tracking performance:', error);
    return NextResponse.json(
      { error: 'Failed to track performance' },
      { status: 500 }
    );
  }
}

export const GET = AuthService.requireAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const page_path = searchParams.get('page');

    let whereClause = `WHERE created_at >= NOW() - INTERVAL '${days} days'`;
    const params: any[] = [];

    if (page_path) {
      whereClause += ` AND page_path = $1`;
      params.push(page_path);
    }

    // Get performance summary
    const summaryQuery = `
      SELECT 
        page_path,
        COUNT(*) as sample_count,
        AVG(load_time) as avg_load_time,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY load_time) as median_load_time,
        PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY load_time) as p95_load_time,
        AVG(dom_content_loaded) as avg_dom_content_loaded,
        AVG(first_paint) as avg_first_paint,
        AVG(largest_contentful_paint) as avg_lcp
      FROM performance_metrics 
      ${whereClause}
      GROUP BY page_path
      ORDER BY sample_count DESC
    `;

    const summaryResult = await DatabaseService.query(summaryQuery, params);

    // Get daily performance trends
    const trendsQuery = `
      SELECT 
        DATE(created_at) as date,
        AVG(load_time) as avg_load_time,
        AVG(dom_content_loaded) as avg_dom_content_loaded,
        AVG(first_paint) as avg_first_paint,
        AVG(largest_contentful_paint) as avg_lcp,
        COUNT(*) as sample_count
      FROM performance_metrics 
      ${whereClause}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    const trendsResult = await DatabaseService.query(trendsQuery, params);

    // Get performance distribution
    const distributionQuery = `
      SELECT 
        CASE 
          WHEN load_time < 1000 THEN 'Fast (< 1s)'
          WHEN load_time < 3000 THEN 'Average (1-3s)'
          WHEN load_time < 5000 THEN 'Slow (3-5s)'
          ELSE 'Very Slow (> 5s)'
        END as performance_bucket,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
      FROM performance_metrics 
      ${whereClause}
      GROUP BY performance_bucket
      ORDER BY 
        CASE performance_bucket
          WHEN 'Fast (< 1s)' THEN 1
          WHEN 'Average (1-3s)' THEN 2
          WHEN 'Slow (3-5s)' THEN 3
          WHEN 'Very Slow (> 5s)' THEN 4
        END
    `;

    const distributionResult = await DatabaseService.query(distributionQuery, params);

    return NextResponse.json({
      summary: summaryResult.rows.map(row => ({
        ...row,
        sample_count: parseInt(row.sample_count),
        avg_load_time: parseFloat(row.avg_load_time) || 0,
        median_load_time: parseFloat(row.median_load_time) || 0,
        p95_load_time: parseFloat(row.p95_load_time) || 0,
        avg_dom_content_loaded: parseFloat(row.avg_dom_content_loaded) || 0,
        avg_first_paint: parseFloat(row.avg_first_paint) || 0,
        avg_lcp: parseFloat(row.avg_lcp) || 0
      })),
      trends: trendsResult.rows.map(row => ({
        ...row,
        sample_count: parseInt(row.sample_count),
        avg_load_time: parseFloat(row.avg_load_time) || 0,
        avg_dom_content_loaded: parseFloat(row.avg_dom_content_loaded) || 0,
        avg_first_paint: parseFloat(row.avg_first_paint) || 0,
        avg_lcp: parseFloat(row.avg_lcp) || 0
      })),
      distribution: distributionResult.rows.map(row => ({
        ...row,
        count: parseInt(row.count),
        percentage: parseFloat(row.percentage)
      }))
    });
  } catch (error) {
    console.error('Error fetching performance analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch performance analytics' },
      { status: 500 }
    );
  }
});
