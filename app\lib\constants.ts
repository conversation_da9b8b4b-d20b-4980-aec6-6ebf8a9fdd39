/**
 * Application constants and configuration
 */

// API Configuration
export const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api.getintheq.space'
  : 'http://localhost:8787';

export const API_ENDPOINTS = {
  GITHUB_REPOS: '/api/github/repos',
  GITHUB_STATS: '/api/github/stats',
  CONTACT: '/api/contact',
  BLOG: '/api/blog/posts',
  // AI Playground endpoints
  PLAYGROUND_CHAT: '/api/playground/chat',
  PLAYGROUND_GENERATE_TEXT: '/api/playground/generate-text',
  PLAYGROUND_SENTIMENT: '/api/playground/analyze-sentiment',
  PLAYGROUND_CODE_ASSIST: '/api/playground/code-assist',
  PLAYGROUND_IMAGE_ANALYSIS: '/api/playground/analyze-image',
} as const;

// Query Keys
export const QUERY_KEYS = {
  GITHUB_REPOS: 'github-repos',
  GITHUB_STATS: 'github-stats',
  BLOG_POSTS: 'blog-posts',
  PLAYGROUND_CHAT: 'playground-chat',
  PLAYGROUND_GENERATE_TEXT: 'playground-generate-text',
  PLAYGROUND_SENTIMENT: 'playground-sentiment',
  PLAYGROUND_CODE_ASSIST: 'playground-code-assist',
  PLAYGROUND_IMAGE_ANALYSIS: 'playground-image-analysis',
} as const;

// Cache Times (in milliseconds)
export const CACHE_TIMES = {
  GITHUB_DATA: 5 * 60 * 1000, // 5 minutes
  GITHUB_STATS: 10 * 60 * 1000, // 10 minutes
  BLOG_POSTS: 15 * 60 * 1000, // 15 minutes
} as const;

// Animation Variants
export const ANIMATION_VARIANTS = {
  container: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  },
  item: {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  },
  fadeInUp: {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }
} as const;

// Social Links
export const SOCIAL_LINKS = {
  GITHUB: 'https://github.com/khiwniti',
  LINKEDIN: 'https://linkedin.com/in/khiwniti',
  EMAIL: 'mailto:<EMAIL>',
} as const;

// Skills Categories
export const SKILL_CATEGORIES = {
  PROGRAMMING: 'Programming',
  AI_ML: 'AI/ML',
  FRONTEND: 'Frontend',
  CLOUD: 'Cloud',
  DATA: 'Data',
  DEVOPS: 'DevOps',
} as const;
