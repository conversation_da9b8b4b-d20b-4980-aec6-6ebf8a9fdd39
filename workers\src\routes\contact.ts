/**
 * Contact form routes for Cloudflare Workers
 */

import { Hono } from 'hono';
import { z } from 'zod';
import type { Env } from '../index';

const contactRouter = new Hono<{ Bindings: Env }>();

// Contact form validation schema
const ContactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200),
  message: z.string().min(10, 'Message must be at least 10 characters').max(2000)
});

type ContactFormData = z.infer<typeof ContactFormSchema>;

// Send contact form email
async function sendContactEmail(data: ContactFormData, env: Env): Promise<boolean> {
  try {
    if (!env.RESEND_API_KEY) {
      console.warn('Resend API key not configured, skipping email');
      return false;
    }

    const emailPayload = {
      from: 'Portfolio Contact <<EMAIL>>',
      to: [env.CONTACT_EMAIL || '<EMAIL>'],
      subject: `Portfolio Contact: ${data.subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">New Contact Form Submission</h2>
          
          <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>Name:</strong> ${data.name}</p>
            <p><strong>Email:</strong> ${data.email}</p>
            <p><strong>Subject:</strong> ${data.subject}</p>
          </div>
          
          <div style="background: #fff; border-left: 4px solid #007bff; padding: 20px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Message:</h3>
            <p style="white-space: pre-wrap; line-height: 1.6;">${data.message}</p>
          </div>
          
          <div style="color: #666; font-size: 12px; margin-top: 30px;">
            <p>This email was sent from the portfolio contact form at getintheq.space</p>
            <p>Time: ${new Date().toISOString()}</p>
          </div>
        </div>
      `,
      reply_to: data.email
    };

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailPayload)
    });

    if (!response.ok) {
      console.error('Resend API error:', await response.text());
      return false;
    }

    return true;
  } catch (error) {
    console.error('Email sending error:', error);
    return false;
  }
}

// Submit contact form
contactRouter.post('/submit', async (c) => {
  try {
    const body = await c.req.json();
    
    // Validate input
    const validationResult = ContactFormSchema.safeParse(body);
    if (!validationResult.success) {
      return c.json({
        success: false,
        error: 'Validation failed',
        details: validationResult.error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message
        }))
      }, 400);
    }

    const formData = validationResult.data;
    
    // Send email
    const emailSent = await sendContactEmail(formData, c.env);
    
    if (emailSent) {
      return c.json({
        success: true,
        message: 'Message sent successfully! I\'ll get back to you soon.',
        data: {
          timestamp: new Date().toISOString(),
          email_sent: true
        }
      });
    } else {
      // Still return success to user, but log the email failure
      console.warn('Email delivery failed, but form submission recorded');
      return c.json({
        success: true,
        message: 'Message received! I\'ll get back to you soon.',
        data: {
          timestamp: new Date().toISOString(),
          email_sent: false
        }
      });
    }

  } catch (error) {
    console.error('Contact form error:', error);
    return c.json({
      success: false,
      error: 'Failed to process contact form',
      message: 'There was an error processing your message. Please try again later.'
    }, 500);
  }
});

// Get contact information
contactRouter.get('/info', (c) => {
  return c.json({
    success: true,
    data: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/khiwniti',
      github: 'https://github.com/khiwniti',
      location: 'Bangkok, Thailand',
      availability: 'Available for freelance and full-time opportunities',
      response_time: 'Usually responds within 24 hours'
    }
  });
});

export { contactRouter };