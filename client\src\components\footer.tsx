import { motion } from "framer-motion";

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-12 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="flex flex-col md:flex-row justify-between items-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="mb-6 md:mb-0">
            <div className="text-2xl font-bold gradient-text mb-2">Khiw Nitithachot</div>
            <p className="text-gray-600 dark:text-gray-400">Data & AI Solutions Engineer</p>
          </div>
          
          <div className="flex space-x-6">
            <motion.a 
              href="https://github.com" 
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-primary transition-colors text-xl"
              whileHover={{ scale: 1.2 }}
              data-testid="footer-github-link"
            >
              <i className="fab fa-github" />
            </motion.a>
            <motion.a 
              href="https://linkedin.com" 
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-primary transition-colors text-xl"
              whileHover={{ scale: 1.2 }}
              data-testid="footer-linkedin-link"
            >
              <i className="fab fa-linkedin" />
            </motion.a>
            <motion.a 
              href="https://twitter.com" 
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-primary transition-colors text-xl"
              whileHover={{ scale: 1.2 }}
              data-testid="footer-twitter-link"
            >
              <i className="fab fa-twitter" />
            </motion.a>
            <motion.a 
              href="mailto:<EMAIL>"
              className="text-gray-400 hover:text-primary transition-colors text-xl"
              whileHover={{ scale: 1.2 }}
              data-testid="footer-email-link"
            >
              <i className="fas fa-envelope" />
            </motion.a>
          </div>
        </motion.div>
        
        <motion.div 
          className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700 text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <p className="text-gray-600 dark:text-gray-400">
            &copy; {currentYear} Khiw Nitithachot. All rights reserved. Built with passion for data and AI.
          </p>
        </motion.div>
      </div>
    </footer>
  );
}
