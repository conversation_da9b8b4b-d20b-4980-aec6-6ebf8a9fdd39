'use client'

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Navbar } from '@/components/navbar';
import { AIProjectCard } from '@/components/playground/ai-project-card';
import { TextGeneratorProject } from '@/components/playground/text-generator-project';
import { ImageAnalysisProject } from '@/components/playground/image-analysis-project';
import { CodeAssistantProject } from '@/components/playground/code-assistant-project';
import { SentimentAnalysisProject } from '@/components/playground/sentiment-analysis-project';
import { ChatBotProject } from '@/components/playground/chatbot-project';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { Brain, Sparkles, Code, Image, MessageSquare, BarChart3 } from 'lucide-react';

const aiProjects = [
  {
    id: 'text-generator',
    title: 'AI Text Generator',
    description: 'Generate creative content, stories, and professional text using advanced language models',
    icon: Sparkles,
    gradient: 'from-purple-500 to-pink-500',
    tags: ['GPT', 'Creative Writing', 'Content Generation'],
    component: TextGeneratorProject
  },
  {
    id: 'image-analysis',
    title: 'AI Image Analysis',
    description: 'Upload images for intelligent analysis, object detection, and detailed descriptions',
    icon: Image,
    gradient: 'from-blue-500 to-cyan-500',
    tags: ['Computer Vision', 'Object Detection', 'Image Processing'],
    component: ImageAnalysisProject
  },
  {
    id: 'code-assistant',
    title: 'AI Code Assistant',
    description: 'Get help with code explanation, optimization, debugging, and generation',
    icon: Code,
    gradient: 'from-green-500 to-emerald-500',
    tags: ['Code Generation', 'Debugging', 'Optimization'],
    component: CodeAssistantProject
  },
  {
    id: 'sentiment-analysis',
    title: 'Sentiment Analysis',
    description: 'Analyze emotions and sentiment in text, reviews, and social media content',
    icon: BarChart3,
    gradient: 'from-orange-500 to-red-500',
    tags: ['NLP', 'Emotion Detection', 'Text Analysis'],
    component: SentimentAnalysisProject
  },
  {
    id: 'chatbot',
    title: 'AI Chatbot',
    description: 'Intelligent conversational AI with personality and domain expertise',
    icon: MessageSquare,
    gradient: 'from-indigo-500 to-purple-500',
    tags: ['Conversational AI', 'Context Awareness', 'Personality'],
    component: ChatBotProject
  }
];

export default function PlaygroundPage() {
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const handleProjectSelect = (projectId: string) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedProject(projectId);
      setIsTransitioning(false);
    }, 300);
  };

  const handleBackToGrid = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedProject(null);
      setIsTransitioning(false);
    }, 300);
  };

  const selectedProjectData = aiProjects.find(p => p.id === selectedProject);
  const SelectedComponent = selectedProjectData?.component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900">
      <Navbar />
      
      <div className="pt-20">
        <AnimatePresence mode="wait">
          {!selectedProject ? (
            <motion.div
              key="grid"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"
            >
              {/* Header */}
              <div className="text-center mb-16">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6 }}
                  className="inline-flex items-center gap-3 mb-6"
                >
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                    <Brain className="w-8 h-8 text-white" />
                  </div>
                  <h1 className="text-5xl md:text-6xl font-bold gradient-text">
                    AI Playground
                  </h1>
                </motion.div>
                
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto"
                >
                  Explore cutting-edge AI technologies through interactive demonstrations. 
                  Each project showcases different aspects of artificial intelligence and machine learning.
                </motion.p>
              </div>

              {/* Projects Grid */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {aiProjects.map((project, index) => (
                  <AIProjectCard
                    key={project.id}
                    project={project}
                    index={index}
                    onSelect={() => handleProjectSelect(project.id)}
                  />
                ))}
              </motion.div>

              {/* Stats Section */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="mt-20 text-center"
              >
                <div className="glass-effect rounded-2xl p-8">
                  <h3 className="text-2xl font-bold mb-6 gradient-text">AI Capabilities Showcase</h3>
                  <div className="grid md:grid-cols-4 gap-6">
                    <div className="p-4">
                      <div className="text-3xl font-bold text-purple-500 mb-2">5</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">AI Projects</div>
                    </div>
                    <div className="p-4">
                      <div className="text-3xl font-bold text-blue-500 mb-2">10+</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">AI Models</div>
                    </div>
                    <div className="p-4">
                      <div className="text-3xl font-bold text-green-500 mb-2">∞</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Possibilities</div>
                    </div>
                    <div className="p-4">
                      <div className="text-3xl font-bold text-orange-500 mb-2">100%</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Interactive</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          ) : (
            <motion.div
              key="project"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.05 }}
              transition={{ duration: 0.5 }}
              className="min-h-screen"
            >
              {SelectedComponent && (
                <ErrorBoundary>
                  <SelectedComponent 
                    onBack={handleBackToGrid}
                    projectData={selectedProjectData}
                  />
                </ErrorBoundary>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
