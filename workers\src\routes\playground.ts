/**
 * AI Playground routes for Cloudflare Workers
 */

import { Hono } from 'hono';
import { z } from 'zod';
import type { Env } from '../index';

const playgroundRouter = new Hono<{ Bindings: Env }>();

// Validation schemas
const ChatMessageSchema = z.object({
  message: z.string().min(1).max(1000),
  personality: z.enum(['friendly', 'professional', 'creative', 'technical']).default('friendly')
});

const TextGenerationSchema = z.object({
  prompt: z.string().min(1).max(500),
  max_length: z.number().min(10).max(500).default(100),
  temperature: z.number().min(0).max(2).default(0.7)
});

const SentimentAnalysisSchema = z.object({
  text: z.string().min(1).max(1000)
});

const CodeGenerationSchema = z.object({
  description: z.string().min(1).max(500),
  language: z.enum(['javascript', 'typescript', 'python', 'java', 'go', 'rust']).default('javascript'),
  framework: z.string().optional()
});

const ImageAnalysisSchema = z.object({
  image_url: z.string().url(),
  analysis_type: z.enum(['describe', 'objects', 'text', 'emotions']).default('describe')
});

// Types
type ChatMessage = z.infer<typeof ChatMessageSchema>;
type TextGeneration = z.infer<typeof TextGenerationSchema>;
type SentimentAnalysis = z.infer<typeof SentimentAnalysisSchema>;
type CodeGeneration = z.infer<typeof CodeGenerationSchema>;
type ImageAnalysis = z.infer<typeof ImageAnalysisSchema>;

// AI response generators (mock implementations)
function generateChatResponse(message: string, personality: string): string {
  const responses = {
    friendly: [
      "That's a great question! Let me think about that for you.",
      "I'd be happy to help you with that! Here's what I think...",
      "Thanks for asking! That's really interesting.",
      "Great point! I love discussing topics like this."
    ],
    professional: [
      "Based on the information provided, I can offer the following analysis:",
      "From a professional standpoint, this requires careful consideration.",
      "I'll provide a comprehensive response to your inquiry.",
      "Let me address your question systematically."
    ],
    creative: [
      "What an imaginative question! Let me explore this creatively...",
      "Oh, this sparks so many interesting ideas! Here's my take...",
      "I love the creativity in your question! Let me think outside the box...",
      "What a wonderfully unique perspective! Here's how I see it..."
    ],
    technical: [
      "From a technical perspective, this involves several key considerations:",
      "Let me break down the technical aspects of your question:",
      "Analyzing this from an engineering standpoint:",
      "The technical implementation would require the following approach:"
    ]
  };

  const personalityResponses = responses[personality as keyof typeof responses];
  const randomResponse = personalityResponses[Math.floor(Math.random() * personalityResponses.length)];
  
  return `${randomResponse}\n\nRegarding "${message}" - this is a simulated AI response for demonstration purposes. In a production environment, this would connect to an actual AI service like OpenAI, Anthropic, or Google AI to provide intelligent responses.`;
}

function generateText(prompt: string, maxLength: number, temperature: number): string {
  // Mock text generation
  const samples = [
    "In the digital age, technology continues to evolve at an unprecedented pace, transforming how we live, work, and interact with the world around us.",
    "The future of artificial intelligence holds immense promise for solving complex problems and enhancing human capabilities across various domains.",
    "Sustainable development requires a delicate balance between economic growth, environmental protection, and social equity for future generations.",
    "Innovation in software development is driven by the constant need to create more efficient, scalable, and user-friendly applications.",
    "The intersection of creativity and technology opens up new possibilities for artistic expression and digital storytelling."
  ];

  const baseSample = samples[Math.floor(Math.random() * samples.length)];
  const words = baseSample.split(' ');
  const targetWords = Math.min(maxLength / 6, words.length); // Approximate words based on max length
  
  return words.slice(0, Math.max(10, targetWords)).join(' ') + 
    ` (Generated with temperature: ${temperature}. This is a mock response - in production, this would use an actual AI text generation service.)`;
}

function analyzeSentiment(text: string): { sentiment: string; confidence: number; explanation: string } {
  // Simple mock sentiment analysis
  const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'pleased'];
  const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'frustrated', 'disappointed'];
  
  const words = text.toLowerCase().split(/\s+/);
  let positiveCount = 0;
  let negativeCount = 0;
  
  words.forEach(word => {
    if (positiveWords.some(pw => word.includes(pw))) positiveCount++;
    if (negativeWords.some(nw => word.includes(nw))) negativeCount++;
  });
  
  let sentiment = 'neutral';
  let confidence = 0.5;
  
  if (positiveCount > negativeCount) {
    sentiment = 'positive';
    confidence = Math.min(0.95, 0.6 + (positiveCount - negativeCount) * 0.1);
  } else if (negativeCount > positiveCount) {
    sentiment = 'negative';
    confidence = Math.min(0.95, 0.6 + (negativeCount - positiveCount) * 0.1);
  }
  
  return {
    sentiment,
    confidence: Math.round(confidence * 100) / 100,
    explanation: `Analysis based on ${positiveCount} positive and ${negativeCount} negative indicators. This is a mock analysis - production would use advanced NLP models.`
  };
}

function generateCode(description: string, language: string, framework?: string): string {
  const codeTemplates = {
    javascript: `
// ${description}
function sampleFunction() {
  // Implementation would go here
  console.log('Generated JavaScript code for: ${description}');
  return 'This is a mock code generation example';
}

export default sampleFunction;`,
    
    typescript: `
// ${description}
interface SampleInterface {
  id: string;
  name: string;
}

function sampleFunction(data: SampleInterface): string {
  // Implementation would go here
  console.log('Generated TypeScript code for:', data.name);
  return 'This is a mock code generation example';
}

export { sampleFunction, type SampleInterface };`,
    
    python: `
# ${description}
def sample_function(data: dict) -> str:
    """
    Generated Python code for: ${description}
    """
    print(f"Processing: {data}")
    return "This is a mock code generation example"

if __name__ == "__main__":
    result = sample_function({"name": "example"})
    print(result)`,
    
    java: `
// ${description}
public class SampleClass {
    public static String sampleMethod(String input) {
        // Implementation would go here
        System.out.println("Generated Java code for: " + input);
        return "This is a mock code generation example";
    }
}`,
    
    go: `
// ${description}
package main

import "fmt"

func sampleFunction(input string) string {
    // Implementation would go here
    fmt.Printf("Generated Go code for: %s\\n", input)
    return "This is a mock code generation example"
}

func main() {
    result := sampleFunction("${description}")
    fmt.Println(result)
}`,
    
    rust: `
// ${description}
fn sample_function(input: &str) -> String {
    // Implementation would go here
    println!("Generated Rust code for: {}", input);
    "This is a mock code generation example".to_string()
}

fn main() {
    let result = sample_function("${description}");
    println!("{}", result);
}`
  };

  const template = codeTemplates[language as keyof typeof codeTemplates] || codeTemplates.javascript;
  return framework ? `// Framework: ${framework}\n${template}` : template;
}

// Chat endpoint
playgroundRouter.post('/chat', async (c) => {
  try {
    const body = await c.req.json();
    const validation = ChatMessageSchema.safeParse(body);
    
    if (!validation.success) {
      return c.json({
        success: false,
        error: 'Invalid input',
        details: validation.error.issues
      }, 400);
    }

    const { message, personality } = validation.data;
    const response = generateChatResponse(message, personality);

    return c.json({
      success: true,
      data: {
        response,
        personality,
        timestamp: new Date().toISOString(),
        tokens_used: response.length // Mock token usage
      }
    });

  } catch (error) {
    console.error('Chat error:', error);
    return c.json({
      success: false,
      error: 'Failed to process chat message'
    }, 500);
  }
});

// Text generation endpoint
playgroundRouter.post('/generate-text', async (c) => {
  try {
    const body = await c.req.json();
    const validation = TextGenerationSchema.safeParse(body);
    
    if (!validation.success) {
      return c.json({
        success: false,
        error: 'Invalid input',
        details: validation.error.issues
      }, 400);
    }

    const { prompt, max_length, temperature } = validation.data;
    const generatedText = generateText(prompt, max_length, temperature);

    return c.json({
      success: true,
      data: {
        generated_text: generatedText,
        prompt,
        max_length,
        temperature,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Text generation error:', error);
    return c.json({
      success: false,
      error: 'Failed to generate text'
    }, 500);
  }
});

// Sentiment analysis endpoint
playgroundRouter.post('/analyze-sentiment', async (c) => {
  try {
    const body = await c.req.json();
    const validation = SentimentAnalysisSchema.safeParse(body);
    
    if (!validation.success) {
      return c.json({
        success: false,
        error: 'Invalid input',
        details: validation.error.issues
      }, 400);
    }

    const { text } = validation.data;
    const analysis = analyzeSentiment(text);

    return c.json({
      success: true,
      data: {
        text,
        ...analysis,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Sentiment analysis error:', error);
    return c.json({
      success: false,
      error: 'Failed to analyze sentiment'
    }, 500);
  }
});

// Code generation endpoint
playgroundRouter.post('/generate-code', async (c) => {
  try {
    const body = await c.req.json();
    const validation = CodeGenerationSchema.safeParse(body);
    
    if (!validation.success) {
      return c.json({
        success: false,
        error: 'Invalid input',
        details: validation.error.issues
      }, 400);
    }

    const { description, language, framework } = validation.data;
    const generatedCode = generateCode(description, language, framework);

    return c.json({
      success: true,
      data: {
        code: generatedCode,
        description,
        language,
        framework,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Code generation error:', error);
    return c.json({
      success: false,
      error: 'Failed to generate code'
    }, 500);
  }
});

// Image analysis endpoint
playgroundRouter.post('/analyze-image', async (c) => {
  try {
    const body = await c.req.json();
    const validation = ImageAnalysisSchema.safeParse(body);
    
    if (!validation.success) {
      return c.json({
        success: false,
        error: 'Invalid input',
        details: validation.error.issues
      }, 400);
    }

    const { image_url, analysis_type } = validation.data;
    
    // Mock image analysis
    const mockAnalysis = {
      describe: "This is a mock image description. In production, this would use computer vision AI to analyze and describe the image content.",
      objects: ["person", "car", "building", "tree"],
      text: "No text detected (mock response)",
      emotions: { happy: 0.7, neutral: 0.2, sad: 0.1 }
    };

    return c.json({
      success: true,
      data: {
        image_url,
        analysis_type,
        result: mockAnalysis[analysis_type],
        timestamp: new Date().toISOString(),
        note: "This is a mock response. Production would use actual computer vision services."
      }
    });

  } catch (error) {
    console.error('Image analysis error:', error);
    return c.json({
      success: false,
      error: 'Failed to analyze image'
    }, 500);
  }
});

// Get playground capabilities
playgroundRouter.get('/capabilities', (c) => {
  return c.json({
    success: true,
    data: {
      chat: {
        description: "Interactive AI chat with personality options",
        personalities: ["friendly", "professional", "creative", "technical"],
        max_message_length: 1000
      },
      text_generation: {
        description: "Generate text based on prompts",
        max_length: 500,
        temperature_range: [0, 2]
      },
      sentiment_analysis: {
        description: "Analyze emotional tone of text",
        supported_languages: ["en"],
        confidence_scoring: true
      },
      code_generation: {
        description: "Generate code in various programming languages",
        supported_languages: ["javascript", "typescript", "python", "java", "go", "rust"],
        framework_support: true
      },
      image_analysis: {
        description: "Analyze images for content, objects, text, and emotions",
        analysis_types: ["describe", "objects", "text", "emotions"],
        supported_formats: ["jpg", "png", "webp"]
      }
    }
  });
});

export { playgroundRouter };