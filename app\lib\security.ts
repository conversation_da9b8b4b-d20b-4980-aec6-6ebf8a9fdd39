import { z } from 'zod';
import { headers } from 'next/headers';

// Environment variable validation schema
export const envSchema = z.object({
  DATABASE_URL: z.string().url('Invalid database URL'),
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  SESSION_SECRET: z.string().min(32, 'Session secret must be at least 32 characters'),
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  
  // Optional environment variables
  GITHUB_TOKEN: z.string().optional(),
  GITHUB_USERNAME: z.string().optional(),
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  FROM_EMAIL: z.string().email().optional(),
  ADMIN_EMAIL: z.string().email().optional(),
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  NEXTAUTH_URL: z.string().url().optional(),
  NEXTAUTH_SECRET: z.string().optional(),
  NEXT_PUBLIC_API_URL: z.string().url().optional(),
  NEXT_PUBLIC_GA_ID: z.string().optional(),
  SENTRY_DSN: z.string().url().optional(),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).optional(),
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).optional(),
  CORS_ORIGINS: z.string().optional(),
});

export type Environment = z.infer<typeof envSchema>;

/**
 * Validates environment variables and throws an error if any required variables are missing
 */
export function validateEnvironment(): Environment {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join('\n');
      
      throw new Error(`Environment validation failed:\n${missingVars}`);
    }
    throw error;
  }
}

/**
 * Content Security Policy configuration
 */
export const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-eval'", // Required for Next.js development
    "'unsafe-inline'", // Required for inline scripts in development
    'https://www.googletagmanager.com',
    'https://www.google-analytics.com',
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'", // Required for Tailwind CSS
    'https://fonts.googleapis.com',
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https://opengraph.githubassets.com',
    'https://avatars.githubusercontent.com',
    'https://www.google-analytics.com',
  ],
  'connect-src': [
    "'self'",
    'https://api.github.com',
    'https://www.google-analytics.com',
    ...(process.env.NODE_ENV === 'development' ? ['ws://localhost:*', 'http://localhost:*'] : []),
  ],
  'frame-ancestors': ["'none'"],
  'frame-src': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'upgrade-insecure-requests': process.env.NODE_ENV === 'production' ? [] : undefined,
};

/**
 * Generates Content Security Policy header value
 */
export function generateCSP(): string {
  return Object.entries(CSP_DIRECTIVES)
    .filter(([_, value]) => value !== undefined)
    .map(([key, value]) => {
      if (Array.isArray(value)) {
        return `${key} ${value.join(' ')}`;
      }
      return key;
    })
    .join('; ');
}

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize HTML content to prevent XSS attacks
   */
  static sanitizeHtml(input: string): string {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize SQL input to prevent SQL injection (basic protection)
   */
  static sanitizeSql(input: string): string {
    return input
      .replace(/['";\\]/g, '')
      .replace(/--/g, '')
      .replace(/\/\*/g, '')
      .replace(/\*\//g, '');
  }

  /**
   * Validate and sanitize email addresses
   */
  static sanitizeEmail(email: string): string {
    return email.toLowerCase().trim();
  }

  /**
   * Remove dangerous characters from filenames
   */
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '')
      .replace(/\.{2,}/g, '.')
      .substring(0, 255);
  }
}

/**
 * Security headers utility
 */
export class SecurityHeaders {
  /**
   * Get security headers for API responses
   */
  static getApiHeaders(): Record<string, string> {
    return {
      'Cache-Control': 'no-store, no-cache, must-revalidate, private',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    };
  }

  /**
   * Get security headers for static content
   */
  static getStaticHeaders(): Record<string, string> {
    return {
      'Cache-Control': 'public, max-age=31536000, immutable',
      'X-Content-Type-Options': 'nosniff',
    };
  }
}

/**
 * IP address validation and extraction
 */
export class IPUtils {
  /**
   * Extract real IP address from request headers
   */
  static getRealIP(headersList?: Headers): string {
    const h = headersList || headers();
    
    // Check various headers for the real IP
    const possibleHeaders = [
      'x-forwarded-for',
      'x-real-ip',
      'x-client-ip',
      'cf-connecting-ip', // Cloudflare
      'x-forwarded',
      'forwarded-for',
      'forwarded',
    ];

    for (const header of possibleHeaders) {
      const value = h.get(header);
      if (value) {
        // x-forwarded-for can contain multiple IPs, take the first one
        const ip = value.split(',')[0].trim();
        if (this.isValidIP(ip)) {
          return ip;
        }
      }
    }

    return 'unknown';
  }

  /**
   * Validate IP address format
   */
  static isValidIP(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * Check if IP is from a private network
   */
  static isPrivateIP(ip: string): boolean {
    if (!this.isValidIP(ip)) return false;

    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^127\./,
      /^169\.254\./, // Link-local
      /^::1$/, // IPv6 loopback
      /^fc00:/, // IPv6 private
      /^fe80:/, // IPv6 link-local
    ];

    return privateRanges.some(range => range.test(ip));
  }
}

/**
 * Request validation utilities
 */
export class RequestValidator {
  /**
   * Validate request origin for CORS
   */
  static isValidOrigin(origin: string | null): boolean {
    if (!origin) return false;

    const allowedOrigins = process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'];
    
    return allowedOrigins.some(allowed => {
      if (allowed === '*') return true;
      if (allowed.endsWith('*')) {
        const baseOrigin = allowed.slice(0, -1);
        return origin.startsWith(baseOrigin);
      }
      return origin === allowed;
    });
  }

  /**
   * Validate User-Agent to detect potential bots or attacks
   */
  static isValidUserAgent(userAgent: string | null): boolean {
    if (!userAgent) return false;

    // Block known malicious patterns
    const maliciousPatterns = [
      /sqlmap/i,
      /nikto/i,
      /nmap/i,
      /masscan/i,
      /nessus/i,
      /openvas/i,
      /acunetix/i,
      /burpsuite/i,
    ];

    return !maliciousPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Check if request size is within limits
   */
  static isValidRequestSize(contentLength: string | null, maxSize: number = 1024 * 1024): boolean {
    if (!contentLength) return true;

    const size = parseInt(contentLength, 10);
    return !isNaN(size) && size <= maxSize;
  }
}

/**
 * Password strength validation
 */
export class PasswordValidator {
  static readonly MIN_LENGTH = 8;
  static readonly MAX_LENGTH = 128;

  /**
   * Validate password strength
   */
  static validate(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < this.MIN_LENGTH) {
      errors.push(`Password must be at least ${this.MIN_LENGTH} characters long`);
    }

    if (password.length > this.MAX_LENGTH) {
      errors.push(`Password must not exceed ${this.MAX_LENGTH} characters`);
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common weak patterns
    const weakPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /abcdef/i,
    ];

    if (weakPatterns.some(pattern => pattern.test(password))) {
      errors.push('Password contains common weak patterns');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

/**
 * Rate limiting key generators
 */
export class RateLimitKeys {
  /**
   * Generate rate limit key for API endpoints
   */
  static forAPI(ip: string, endpoint: string): string {
    return `api:${endpoint}:${ip}`;
  }

  /**
   * Generate rate limit key for authentication attempts
   */
  static forAuth(ip: string, identifier: string): string {
    return `auth:${identifier}:${ip}`;
  }

  /**
   * Generate rate limit key for contact form submissions
   */
  static forContact(ip: string): string {
    return `contact:${ip}`;
  }

  /**
   * Generate rate limit key for general endpoints
   */
  static forEndpoint(endpoint: string, ip: string): string {
    return `endpoint:${endpoint}:${ip}`;
  }
}