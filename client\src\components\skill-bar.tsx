import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface SkillBarProps {
  name: string;
  level: number;
  gradient: string;
  delay?: number;
}

export function SkillBar({ name, level, gradient, delay = 0 }: SkillBarProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById(`skill-${name.replace(/\s+/g, '-').toLowerCase()}`);
    if (element) {
      observer.observe(element);
    }

    return () => observer.disconnect();
  }, [name]);

  return (
    <div 
      id={`skill-${name.replace(/\s+/g, '-').toLowerCase()}`} 
      className="skill-item"
      data-testid={`skill-${name.replace(/\s+/g, '-').toLowerCase()}`}
    >
      <div className="flex justify-between mb-2">
        <span className="font-medium">{name}</span>
        <span className="text-sm text-gray-500 dark:text-gray-400">{level}%</span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <motion.div 
          className={`skill-bar h-2 rounded-full ${gradient}`}
          initial={{ width: "0%" }}
          animate={{ width: isVisible ? `${level}%` : "0%" }}
          transition={{ duration: 2, delay: delay, ease: "easeOut" }}
        />
      </div>
    </div>
  );
}
