{"include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "shared/**/*"], "exclude": ["node_modules", "server/**/*", "workers/**/*", "serverless/**/*"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "plugins": [{"name": "next"}], "baseUrl": ".", "types": ["node"], "paths": {"@/*": ["./app/*"], "@/components/*": ["./app/components/*"], "@/lib/*": ["./app/lib/*"], "@/hooks/*": ["./app/hooks/*"], "@shared/*": ["./shared/*"]}, "allowJs": true, "resolveJsonModule": true, "isolatedModules": true}}