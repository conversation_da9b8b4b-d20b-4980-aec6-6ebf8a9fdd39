'use client'

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Brain, 
  Sparkles, 
  Image, 
  Code, 
  MessageSquare, 
  BarChart3,
  Save,
  Eye,
  Settings,
  Upload,
  Zap,
  Globe,
  Shield,
  Database,
  Server,
  Cpu
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import Link from 'next/link';

const projectTemplates = [
  {
    id: 'text-generator',
    name: 'AI Text Generator',
    description: 'Create content, stories, and professional text',
    icon: Sparkles,
    gradient: 'from-purple-500 to-pink-500',
    tags: ['GPT', 'Content', 'Writing'],
    complexity: 'Medium',
    estimatedTime: '2-3 hours'
  },
  {
    id: 'image-analysis',
    name: 'AI Image Analysis',
    description: 'Object detection and image understanding',
    icon: Image,
    gradient: 'from-blue-500 to-cyan-500',
    tags: ['Vision', 'Detection', 'Analysis'],
    complexity: 'Advanced',
    estimatedTime: '4-6 hours'
  },
  {
    id: 'code-assistant',
    name: 'AI Code Assistant',
    description: 'Code generation, debugging, and optimization',
    icon: Code,
    gradient: 'from-green-500 to-emerald-500',
    tags: ['Coding', 'Debug', 'Optimize'],
    complexity: 'Advanced',
    estimatedTime: '3-5 hours'
  },
  {
    id: 'chatbot',
    name: 'AI Chatbot',
    description: 'Conversational AI with personality',
    icon: MessageSquare,
    gradient: 'from-indigo-500 to-purple-500',
    tags: ['Chat', 'NLP', 'Conversation'],
    complexity: 'Expert',
    estimatedTime: '6-8 hours'
  },
  {
    id: 'sentiment-analysis',
    name: 'Sentiment Analysis',
    description: 'Emotion detection and text analysis',
    icon: BarChart3,
    gradient: 'from-orange-500 to-red-500',
    tags: ['NLP', 'Emotion', 'Analytics'],
    complexity: 'Medium',
    estimatedTime: '2-4 hours'
  },
  {
    id: 'custom',
    name: 'Custom AI Project',
    description: 'Build your own unique AI solution',
    icon: Brain,
    gradient: 'from-gray-500 to-gray-700',
    tags: ['Custom', 'Flexible', 'Advanced'],
    complexity: 'Expert',
    estimatedTime: 'Variable'
  }
];

const aiModels = [
  { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI', type: 'Language', cost: 'High' },
  { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI', type: 'Language', cost: 'Medium' },
  { id: 'claude-3', name: 'Claude 3', provider: 'Anthropic', type: 'Language', cost: 'High' },
  { id: 'gemini-pro', name: 'Gemini Pro', provider: 'Google', type: 'Multimodal', cost: 'Medium' },
  { id: 'dalle-3', name: 'DALL-E 3', provider: 'OpenAI', type: 'Image', cost: 'High' },
  { id: 'stable-diffusion', name: 'Stable Diffusion', provider: 'Stability AI', type: 'Image', cost: 'Low' }
];

export default function NewAIProject() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    // Basic Info
    name: '',
    description: '',
    longDescription: '',
    category: '',
    tags: [] as string[],
    
    // Configuration
    aiModel: '',
    apiEndpoint: '',
    apiKey: '',
    maxTokens: 2000,
    temperature: 0.7,
    
    // Appearance
    icon: '',
    gradient: 'from-purple-500 to-pink-500',
    primaryColor: '#8B5CF6',
    
    // Settings
    status: 'active',
    featured: false,
    rateLimit: 100,
    requireAuth: false,
    
    // Advanced
    customPrompts: '',
    fallbackModel: '',
    cacheEnabled: true,
    loggingEnabled: true
  });

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = projectTemplates.find(t => t.id === templateId);
    if (template) {
      setFormData(prev => ({
        ...prev,
        name: template.name,
        description: template.description,
        category: templateId,
        gradient: template.gradient,
        tags: template.tags
      }));
    }
    setCurrentStep(2);
  };

  const addTag = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Easy': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'Advanced': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'Expert': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const handleSubmit = async () => {
    try {
      // Here you would normally save to your database
      console.log('Creating AI project:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Redirect to playground management
      router.push('/admin/playground');
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  const steps = [
    { id: 1, name: 'Template', description: 'Choose project type' },
    { id: 2, name: 'Basic Info', description: 'Name and description' },
    { id: 3, name: 'Configuration', description: 'AI model settings' },
    { id: 4, name: 'Appearance', description: 'Visual customization' },
    { id: 5, name: 'Advanced', description: 'Expert settings' },
    { id: 6, name: 'Review', description: 'Final review' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-pink-900/20">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/admin/playground">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Playground
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold gradient-text">Create AI Project</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Build and deploy your next AI-powered application
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button variant="outline" size="sm">
              <Save className="w-4 h-4 mr-2" />
              Save Draft
            </Button>
          </div>
        </div>

        {/* Progress Steps */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                    currentStep >= step.id 
                      ? 'bg-purple-500 border-purple-500 text-white' 
                      : 'border-gray-300 text-gray-400'
                  }`}>
                    {currentStep > step.id ? (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="w-5 h-5 bg-white rounded-full flex items-center justify-center"
                      >
                        <div className="w-2 h-2 bg-purple-500 rounded-full" />
                      </motion.div>
                    ) : (
                      step.id
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`h-1 w-16 mx-2 transition-colors ${
                      currentStep > step.id ? 'bg-purple-500' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-4">
              {steps.map((step) => (
                <div key={step.id} className="flex flex-col items-center w-20">
                  <span className="text-sm font-medium">{step.name}</span>
                  <span className="text-xs text-gray-500">{step.description}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        <AnimatePresence mode="wait">
          {currentStep === 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-6 h-6" />
                    Choose Your AI Project Template
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {projectTemplates.map((template, index) => {
                      const Icon = template.icon;
                      return (
                        <motion.div
                          key={template.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="group cursor-pointer"
                          onClick={() => handleTemplateSelect(template.id)}
                        >
                          <Card className="hover:shadow-xl transition-all duration-300 border-2 hover:border-purple-300">
                            <div className={`h-24 bg-gradient-to-r ${template.gradient} rounded-t-lg relative overflow-hidden`}>
                              <div className="absolute inset-0 bg-black/10" />
                              <div className="absolute bottom-3 left-3">
                                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                                  <Icon className="w-6 h-6 text-white" />
                                </div>
                              </div>
                              <div className="absolute top-3 right-3">
                                <Badge className={getComplexityColor(template.complexity)}>
                                  {template.complexity}
                                </Badge>
                              </div>
                            </div>
                            <CardContent className="p-4">
                              <h3 className="font-semibold text-lg mb-2">{template.name}</h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                {template.description}
                              </p>
                              <div className="flex flex-wrap gap-1 mb-4">
                                {template.tags.map(tag => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                              <div className="flex items-center justify-between text-sm text-gray-500">
                                <span>{template.estimatedTime}</span>
                                <span>{template.complexity}</span>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {currentStep === 2 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">Project Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter project name"
                        className="text-lg"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text-generation">Text Generation</SelectItem>
                          <SelectItem value="image-analysis">Image Analysis</SelectItem>
                          <SelectItem value="code-assistant">Code Assistant</SelectItem>
                          <SelectItem value="chatbot">Chatbot</SelectItem>
                          <SelectItem value="analytics">Analytics</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Short Description *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description for the project card"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="longDescription">Detailed Description</Label>
                    <Textarea
                      id="longDescription"
                      value={formData.longDescription}
                      onChange={(e) => setFormData(prev => ({ ...prev, longDescription: e.target.value }))}
                      placeholder="Detailed description with features and capabilities"
                      rows={5}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {formData.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                    <Input
                      placeholder="Add tags (press Enter)"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag(e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {currentStep === 3 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    AI Model Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>AI Model *</Label>
                      <Select value={formData.aiModel} onValueChange={(value) => setFormData(prev => ({ ...prev, aiModel: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose AI model" />
                        </SelectTrigger>
                        <SelectContent>
                          {aiModels.map(model => (
                            <SelectItem key={model.id} value={model.id}>
                              <div className="flex items-center justify-between w-full">
                                <span>{model.name}</span>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="text-xs">{model.type}</Badge>
                                  <Badge variant="outline" className="text-xs">{model.cost}</Badge>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="apiEndpoint">API Endpoint</Label>
                      <Input
                        id="apiEndpoint"
                        value={formData.apiEndpoint}
                        onChange={(e) => setFormData(prev => ({ ...prev, apiEndpoint: e.target.value }))}
                        placeholder="https://api.openai.com/v1/chat/completions"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="apiKey">API Key (will be encrypted)</Label>
                    <Input
                      id="apiKey"
                      type="password"
                      value={formData.apiKey}
                      onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                      placeholder="Enter API key"
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>Max Tokens: {formData.maxTokens}</Label>
                      <input
                        type="range"
                        min="100"
                        max="8000"
                        step="100"
                        value={formData.maxTokens}
                        onChange={(e) => setFormData(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Temperature: {formData.temperature}</Label>
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={formData.temperature}
                        onChange={(e) => setFormData(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fallbackModel">Fallback Model (optional)</Label>
                    <Select value={formData.fallbackModel} onValueChange={(value) => setFormData(prev => ({ ...prev, fallbackModel: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose fallback model" />
                      </SelectTrigger>
                      <SelectContent>
                        {aiModels.map(model => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {currentStep === 6 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Review & Deploy</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-8">
                    {/* Project Preview */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Project Preview</h3>
                      <div className={`h-32 bg-gradient-to-r ${formData.gradient} rounded-lg p-4 text-white relative overflow-hidden`}>
                        <div className="absolute inset-0 bg-black/10" />
                        <div className="relative z-10">
                          <h4 className="text-xl font-bold">{formData.name || 'Project Name'}</h4>
                          <p className="text-sm opacity-90">{formData.description || 'Project description...'}</p>
                          {formData.featured && (
                            <Badge className="mt-2 bg-white/20 text-white border-white/30">Featured</Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {formData.tags.map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">{tag}</Badge>
                        ))}
                      </div>
                    </div>

                    {/* Configuration Summary */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Configuration</h3>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">AI Model:</span>
                          <span className="font-medium">{formData.aiModel || 'Not selected'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Max Tokens:</span>
                          <span className="font-medium">{formData.maxTokens}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Temperature:</span>
                          <span className="font-medium">{formData.temperature}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Rate Limit:</span>
                          <span className="font-medium">{formData.rateLimit}/hour</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Status:</span>
                          <Badge className={formData.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {formData.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-6 border-t">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-green-600">Security validated</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Zap className="w-4 h-4 text-blue-500" />
                        <span className="text-sm text-blue-600">Performance optimized</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button variant="outline" onClick={() => setCurrentStep(currentStep - 1)}>
                        Back
                      </Button>
                      <Button 
                        onClick={handleSubmit}
                        className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                      >
                        <Zap className="w-4 h-4 mr-2" />
                        Deploy Project
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        {currentStep > 1 && currentStep < 6 && (
          <div className="flex items-center justify-between mt-8">
            <Button variant="outline" onClick={() => setCurrentStep(currentStep - 1)}>
              Previous
            </Button>
            <Button onClick={() => setCurrentStep(currentStep + 1)}>
              {currentStep === 5 ? 'Review' : 'Next'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}