module.exports = {
  // TypeScript and JavaScript files
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
    'git add',
  ],
  
  // JSON files
  '*.{json,json5}': [
    'prettier --write',
    'git add',
  ],
  
  // Markdown files
  '*.{md,mdx}': [
    'prettier --write',
    'git add',
  ],
  
  // CSS and styling files
  '*.{css,scss,sass}': [
    'prettier --write',
    'git add',
  ],
  
  // YAML files
  '*.{yml,yaml}': [
    'prettier --write',
    'git add',
  ],
  
  // Package.json (ensure proper formatting)
  'package.json': [
    'prettier --write',
    'git add',
  ],
  
  // TypeScript files - run type check
  '*.{ts,tsx}': [
    () => 'npm run type-check',
  ],
  
  // Test files - run related tests
  '*.{test,spec}.{js,jsx,ts,tsx}': [
    'npm run test -- --passWithNoTests --findRelatedTests',
  ],
};