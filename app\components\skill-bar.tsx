'use client'

import { motion, useInView } from "framer-motion";
import { useEffect, useState, useRef } from "react";

interface SkillBarProps {
  name: string;
  level: number;
  gradient: string;
  delay?: number;
  icon?: string;
  category?: string;
}

export function SkillBar({ name, level, gradient, delay = 0, icon, category }: SkillBarProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [animatedLevel, setAnimatedLevel] = useState(0);

  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        setAnimatedLevel(level);
      }, delay * 1000);
      return () => clearTimeout(timer);
    }
  }, [isInView, level, delay]);

  return (
    <motion.div
      ref={ref}
      className="skill-item glass-effect p-4 rounded-xl"
      data-testid={`skill-${name.replace(/\s+/g, '-').toLowerCase()}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 20 }}
      transition={{ duration: 0.6, delay: delay }}
      whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {icon && (
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
              <span className="text-lg">{icon}</span>
            </div>
          )}
          <div>
            <span className="font-semibold text-gray-900 dark:text-white">{name}</span>
            {category && (
              <div className="text-xs text-gray-500 dark:text-gray-400">{category}</div>
            )}
          </div>
        </div>
        <motion.span
          className="text-sm font-bold text-primary"
          initial={{ opacity: 0 }}
          animate={{ opacity: isInView ? 1 : 0 }}
          transition={{ duration: 0.5, delay: delay + 0.5 }}
        >
          {Math.round(animatedLevel)}%
        </motion.span>
      </div>

      <div className="relative">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
          <motion.div
            className={`skill-bar h-full rounded-full ${gradient} relative overflow-hidden`}
            initial={{ width: "0%" }}
            animate={{ width: isInView ? `${level}%` : "0%" }}
            transition={{ duration: 1.5, delay: delay + 0.2, ease: "easeOut" }}
          >
            {/* Shimmer effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12"
              initial={{ x: "-100%" }}
              animate={{ x: isInView ? "100%" : "-100%" }}
              transition={{
                duration: 1.5,
                delay: delay + 1,
                ease: "easeOut",
                repeat: isInView ? Infinity : 0,
                repeatDelay: 3
              }}
            />
          </motion.div>
        </div>

        {/* Glow effect */}
        <motion.div
          className={`absolute inset-0 rounded-full ${gradient} blur-sm opacity-30`}
          initial={{ width: "0%" }}
          animate={{ width: isInView ? `${level}%` : "0%" }}
          transition={{ duration: 1.5, delay: delay + 0.2, ease: "easeOut" }}
        />
      </div>
    </motion.div>
  );
}
