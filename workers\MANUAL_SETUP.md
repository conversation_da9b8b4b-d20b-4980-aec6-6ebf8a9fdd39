# Manual D1 Database Setup Guide

Since the API token doesn't have D1 permissions, let's set up the databases manually through the Cloudflare Dashboard.

## Step 1: Create D1 Databases via Dashboard

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Navigate to "Workers & Pages" > "D1 SQL Database"
3. Click "Create database"
4. Create these databases:
   - `portfolio-db` (for development)
   - `portfolio-db-staging` (for staging)
   - `portfolio-db-prod` (for production)

## Step 2: Get Database IDs

After creating each database:
1. Click on the database name
2. Copy the "Database ID" from the right sidebar
3. Note these IDs down

## Step 3: Update wrangler.toml

Replace the placeholder IDs in `wrangler.toml`:

```toml
# Replace YOUR_DATABASE_ID_HERE with the dev database ID
# Replace YOUR_STAGING_DATABASE_ID_HERE with the staging database ID  
# Replace YOUR_PROD_DATABASE_ID_HERE with the production database ID
```

## Step 4: Apply Database Schema

Once you have the database IDs:

1. Update `wrangler.toml` with the actual database IDs
2. Run the migration commands:

```bash
export CLOUDFLARE_API_TOKEN=****************************************

# Apply schema to production database
npx wrangler d1 execute portfolio-db-prod --file=./migrations/0001_initial.sql --env production
```

## Step 5: Deploy Worker

```bash
# Set environment secrets
npx wrangler secret put GITHUB_TOKEN --env production
npx wrangler secret put GITHUB_USERNAME --env production
npx wrangler secret put RESEND_API_KEY --env production
npx wrangler secret put CONTACT_EMAIL --env production

# Deploy to production
npm run deploy:production
```

## Alternative: Quick Deploy Script

I'll create a deployment script that handles the missing D1 permissions: