/**
 * Blog routes for Cloudflare Workers
 */

import { Hono } from 'hono';
import type { Env } from '../index';

const blogRouter = new Hono<{ Bindings: Env }>();

// Blog post interface
interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  published_date: string;
  updated_date?: string;
  tags: string[];
  featured_image?: string;
  reading_time: number;
  status: 'draft' | 'published';
}

// Sample blog posts data (in production, this could come from a CMS or database)
const sampleBlogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Building Modern Web Applications with Next.js 14',
    slug: 'building-modern-web-applications-nextjs-14',
    excerpt: 'Explore the latest features in Next.js 14 and how to build performant, scalable web applications with the App Router.',
    content: `
# Building Modern Web Applications with Next.js 14

Next.js 14 introduces several groundbreaking features that revolutionize how we build web applications. In this comprehensive guide, we'll explore the App Router, Server Components, and the new streaming capabilities.

## The App Router Revolution

The App Router represents a paradigm shift in Next.js development. Unlike the traditional Pages Router, the App Router is built on React's latest features, including Server Components and Suspense.

### Key Benefits:
- **Improved Performance**: Server Components reduce bundle size
- **Better SEO**: Enhanced server-side rendering capabilities
- **Simplified Data Fetching**: Native async/await support in components
- **Nested Layouts**: More flexible layout composition

## Server Components vs Client Components

Understanding when to use Server Components versus Client Components is crucial for optimal performance:

\`\`\`typescript
// Server Component (default)
async function ProductList() {
  const products = await fetch('/api/products');
  return (
    <div>
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
}

// Client Component (interactive)
'use client';
import { useState } from 'react';

function ProductFilter() {
  const [filter, setFilter] = useState('');
  return (
    <input 
      value={filter}
      onChange={(e) => setFilter(e.target.value)}
      placeholder="Filter products..."
    />
  );
}
\`\`\`

## Streaming and Suspense

Next.js 14's streaming capabilities allow for progressive loading of content:

\`\`\`typescript
import { Suspense } from 'react';

export default function Page() {
  return (
    <div>
      <h1>My Dashboard</h1>
      <Suspense fallback={<ProductsSkeleton />}>
        <Products />
      </Suspense>
      <Suspense fallback={<AnalyticsSkeleton />}>
        <Analytics />
      </Suspense>
    </div>
  );
}
\`\`\`

## Performance Optimizations

1. **Image Optimization**: Use Next.js Image component for automatic optimization
2. **Font Optimization**: Leverage next/font for optimal font loading
3. **Bundle Analysis**: Regular bundle size monitoring and optimization

## Conclusion

Next.js 14 represents the future of React development, offering unparalleled performance and developer experience. By embracing these new patterns, we can build applications that are both fast and maintainable.

Ready to upgrade your Next.js application? Start with the App Router migration guide and gradually adopt these new patterns.
    `,
    author: 'Khiw Nitithachot',
    published_date: '2024-01-15T10:00:00Z',
    updated_date: '2024-01-20T15:30:00Z',
    tags: ['Next.js', 'React', 'Web Development', 'Performance', 'TypeScript'],
    featured_image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
    reading_time: 8,
    status: 'published'
  },
  {
    id: '2',
    title: 'The Future of AI in Web Development',
    slug: 'future-ai-web-development',
    excerpt: 'How artificial intelligence is transforming the way we build, test, and deploy web applications.',
    content: `
# The Future of AI in Web Development

Artificial Intelligence is rapidly transforming every aspect of web development, from code generation to automated testing and deployment optimization.

## AI-Powered Development Tools

### Code Generation
Modern AI tools can generate entire components, functions, and even full applications:

\`\`\`typescript
// AI-generated React component
interface UserProfileProps {
  user: User;
  onEdit: (user: User) => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onEdit }) => {
  return (
    <div className="user-profile">
      <img src={user.avatar} alt={user.name} />
      <h2>{user.name}</h2>
      <p>{user.email}</p>
      <button onClick={() => onEdit(user)}>Edit Profile</button>
    </div>
  );
};
\`\`\`

### Automated Testing
AI can generate comprehensive test suites:

\`\`\`typescript
describe('UserProfile Component', () => {
  it('renders user information correctly', () => {
    const mockUser = { name: 'John Doe', email: '<EMAIL>' };
    render(<UserProfile user={mockUser} onEdit={jest.fn()} />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});
\`\`\`

## The Developer Experience Revolution

AI is not replacing developers—it's augmenting our capabilities:

1. **Faster Prototyping**: Rapid MVP development
2. **Enhanced Productivity**: Automated repetitive tasks
3. **Better Code Quality**: AI-powered code reviews
4. **Learning Acceleration**: Contextual documentation and tutorials

## Challenges and Considerations

While AI offers tremendous benefits, we must consider:

- **Code Quality**: Ensuring AI-generated code meets standards
- **Security**: Validating AI suggestions for vulnerabilities
- **Dependence**: Maintaining core development skills
- **Ethics**: Responsible AI usage in development workflows

## The Road Ahead

The future of web development lies in human-AI collaboration. By embracing these tools while maintaining our fundamental skills, we can build better applications faster than ever before.
    `,
    author: 'Khiw Nitithachot',
    published_date: '2024-01-10T14:00:00Z',
    tags: ['AI', 'Machine Learning', 'Web Development', 'Future Tech', 'Productivity'],
    featured_image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=400&fit=crop',
    reading_time: 6,
    status: 'published'
  },
  {
    id: '3',
    title: 'TypeScript Best Practices for Large-Scale Applications',
    slug: 'typescript-best-practices-large-scale',
    excerpt: 'Essential TypeScript patterns and practices for building maintainable enterprise applications.',
    content: `
# TypeScript Best Practices for Large-Scale Applications

As applications grow in complexity, TypeScript becomes essential for maintaining code quality and developer productivity. Here are proven patterns for enterprise-scale development.

## Type-Safe Architecture

### Domain-Driven Design with Types

\`\`\`typescript
// Domain types
interface User {
  readonly id: UserId;
  readonly email: Email;
  readonly profile: UserProfile;
  readonly permissions: Permission[];
}

// Value objects with validation
class Email {
  constructor(private readonly value: string) {
    if (!this.isValid(value)) {
      throw new Error('Invalid email format');
    }
  }

  private isValid(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }

  toString(): string {
    return this.value;
  }
}
\`\`\`

### API Response Types

\`\`\`typescript
// Generic API response pattern
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
  };
}

// Usage with specific types
type UserListResponse = ApiResponse<User[]>;
type UserDetailResponse = ApiResponse<User>;
\`\`\`

## Advanced Type Patterns

### Conditional Types for Flexibility

\`\`\`typescript
type ApiEndpoint<T extends 'list' | 'detail'> = T extends 'list'
  ? { users: User[]; pagination: Pagination }
  : { user: User };

// Type-safe API calls
async function fetchUsers<T extends 'list' | 'detail'>(
  type: T
): Promise<ApiEndpoint<T>> {
  // Implementation
}
\`\`\`

### Utility Types for Transformation

\`\`\`typescript
// Create form types from domain types
type CreateUserForm = Pick<User, 'email'> & {
  password: string;
  confirmPassword: string;
};

type UpdateUserForm = Partial<Pick<User, 'profile'>> & {
  id: UserId;
};
\`\`\`

## Error Handling Patterns

### Result Type Pattern

\`\`\`typescript
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

async function safeApiCall<T>(
  apiCall: () => Promise<T>
): Promise<Result<T>> {
  try {
    const data = await apiCall();
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error('Unknown error')
    };
  }
}
\`\`\`

## Performance Considerations

1. **Incremental Compilation**: Use project references for faster builds
2. **Strict Mode**: Enable all strict checks for better error detection
3. **Bundle Analysis**: Monitor TypeScript's impact on bundle size
4. **Type-Only Imports**: Use \`import type\` for type-only dependencies

## Testing TypeScript Applications

\`\`\`typescript
// Type-safe test utilities
function createMockUser(overrides: Partial<User> = {}): User {
  return {
    id: 'user-123' as UserId,
    email: new Email('<EMAIL>'),
    profile: { name: 'Test User' },
    permissions: [],
    ...overrides
  };
}
\`\`\`

## Conclusion

TypeScript's power lies not just in type safety, but in enabling better architecture and developer experience. By following these patterns, large teams can maintain velocity while ensuring code quality.
    `,
    author: 'Khiw Nitithachot',
    published_date: '2024-01-05T09:00:00Z',
    tags: ['TypeScript', 'Architecture', 'Best Practices', 'Enterprise', 'Code Quality'],
    featured_image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=400&fit=crop',
    reading_time: 10,
    status: 'published'
  }
];

// Get all published blog posts
blogRouter.get('/posts', (c) => {
  const publishedPosts = sampleBlogPosts
    .filter(post => post.status === 'published')
    .map(post => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      author: post.author,
      published_date: post.published_date,
      updated_date: post.updated_date,
      tags: post.tags,
      featured_image: post.featured_image,
      reading_time: post.reading_time
    }))
    .sort((a, b) => new Date(b.published_date).getTime() - new Date(a.published_date).getTime());

  return c.json({
    success: true,
    data: publishedPosts,
    meta: {
      total: publishedPosts.length,
      page: 1,
      limit: publishedPosts.length
    }
  });
});

// Get featured blog posts
blogRouter.get('/featured', (c) => {
  const featuredPosts = sampleBlogPosts
    .filter(post => post.status === 'published')
    .slice(0, 3)
    .map(post => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      featured_image: post.featured_image,
      published_date: post.published_date,
      reading_time: post.reading_time,
      tags: post.tags.slice(0, 3)
    }));

  return c.json({
    success: true,
    data: featuredPosts
  });
});

// Get single blog post by slug
blogRouter.get('/posts/:slug', (c) => {
  const slug = c.req.param('slug');
  const post = sampleBlogPosts.find(p => p.slug === slug && p.status === 'published');

  if (!post) {
    return c.json({
      success: false,
      error: 'Blog post not found'
    }, 404);
  }

  return c.json({
    success: true,
    data: post
  });
});

// Get blog tags
blogRouter.get('/tags', (c) => {
  const allTags = sampleBlogPosts
    .filter(post => post.status === 'published')
    .flatMap(post => post.tags);

  const tagCounts = allTags.reduce((acc, tag) => {
    acc[tag] = (acc[tag] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const tags = Object.entries(tagCounts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count);

  return c.json({
    success: true,
    data: tags
  });
});

export { blogRouter };