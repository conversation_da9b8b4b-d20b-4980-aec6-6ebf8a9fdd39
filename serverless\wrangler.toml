# Cloudflare Workers configuration
name = "portfolio-api"
main = "src/worker.py"
compatibility_date = "2024-01-15"

[env.production]
name = "portfolio-api"

[env.staging]
name = "portfolio-api-staging"

# Environment variables (set via CLI: wrangler secret put GITHUB_TOKEN)
# wrangler secret put GITHUB_TOKEN
# wrangler secret put GITHUB_USERNAME  
# wrangler secret put RESEND_API_KEY
# wrangler secret put CONTACT_EMAIL

# For Python Workers, you need to use Pyodide
[build]
command = "python -m pip install -r requirements.txt"

# KV storage for caching (optional)
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"

# Rate limiting using Durable Objects (advanced)
# [[durable_objects.bindings]]
# name = "RATE_LIMITER"
# class_name = "RateLimiter"