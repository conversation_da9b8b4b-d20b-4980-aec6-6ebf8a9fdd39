import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { AuthService } from './lib/auth';
import { applyRateLimit } from './lib/rate-limiter';
import { SecurityHeaders, RequestValidator, IPUtils, generateCSP } from './lib/security';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const response = NextResponse.next();

  // Security validations
  const userAgent = request.headers.get('user-agent');
  const origin = request.headers.get('origin');
  const contentLength = request.headers.get('content-length');
  const clientIP = IPUtils.getRealIP(request.headers);

  // Block malicious user agents
  if (!RequestValidator.isValidUserAgent(userAgent)) {
    console.warn(`Blocked malicious user agent: ${userAgent} from IP: ${clientIP}`);
    return new NextResponse('Forbidden', { status: 403 });
  }

  // Validate request size
  if (!RequestValidator.isValidRequestSize(contentLength, 10 * 1024 * 1024)) { // 10MB limit
    console.warn(`Request too large: ${contentLength} bytes from IP: ${clientIP}`);
    return new NextResponse('Request Entity Too Large', { status: 413 });
  }

  // CORS validation for API routes
  if (pathname.startsWith('/api') && origin && !RequestValidator.isValidOrigin(origin)) {
    console.warn(`Invalid origin: ${origin} for API request from IP: ${clientIP}`);
    return new NextResponse('Forbidden', { status: 403 });
  }

  // Apply security headers to all responses
  const securityHeaders = SecurityHeaders.getApiHeaders();
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Add Content Security Policy
  response.headers.set('Content-Security-Policy', generateCSP());

  // Skip further middleware for excluded routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname === '/favicon.ico' ||
    pathname === '/robots.txt' ||
    pathname === '/sitemap.xml'
  ) {
    return response;
  }

  // Apply rate limiting based on route type
  let rateLimitConfig: 'auth' | 'api' | 'contact' | 'admin' | 'public' = 'public';
  
  if (pathname.startsWith('/api/auth')) {
    rateLimitConfig = 'auth';
  } else if (pathname.startsWith('/api/contact')) {
    rateLimitConfig = 'contact';
  } else if (pathname.startsWith('/api/admin')) {
    rateLimitConfig = 'admin';
  } else if (pathname.startsWith('/api')) {
    rateLimitConfig = 'api';
  } else if (pathname.startsWith('/admin')) {
    rateLimitConfig = 'admin';
  }

  // Apply rate limiting
  const { allowed, headers: rateLimitHeaders } = await applyRateLimit(request, rateLimitConfig);
  
  if (!allowed) {
    console.warn(`Rate limit exceeded for IP: ${clientIP} on route: ${pathname}`);
    const rateLimitResponse = new NextResponse('Too Many Requests', { status: 429 });
    
    // Add rate limit headers
    Object.entries(rateLimitHeaders).forEach(([key, value]) => {
      rateLimitResponse.headers.set(key, value);
    });
    
    // Add security headers
    Object.entries(securityHeaders).forEach(([key, value]) => {
      rateLimitResponse.headers.set(key, value);
    });
    
    return rateLimitResponse;
  }

  // Add rate limit headers to successful responses
  Object.entries(rateLimitHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Authentication check for admin routes (skip auth endpoints)
  if (pathname.startsWith('/admin') && !pathname.startsWith('/admin/login')) {
    try {
      const user = await AuthService.getUserFromRequest(request);
      
      if (!user) {
        // Redirect to login page
        const loginUrl = new URL('/admin/login', request.url);
        loginUrl.searchParams.set('redirect', pathname);
        const redirectResponse = NextResponse.redirect(loginUrl);
        
        // Add security headers to redirect
        Object.entries(securityHeaders).forEach(([key, value]) => {
          redirectResponse.headers.set(key, value);
        });
        
        return redirectResponse;
      }

      // Check if user has admin role
      if (user.role !== 'admin') {
        console.warn(`Unauthorized admin access attempt by user: ${user.id} from IP: ${clientIP}`);
        const forbiddenResponse = new NextResponse('Forbidden', { status: 403 });
        
        // Add security headers
        Object.entries(securityHeaders).forEach(([key, value]) => {
          forbiddenResponse.headers.set(key, value);
        });
        
        return forbiddenResponse;
      }
    } catch (error) {
      console.error('Auth check error in middleware:', error);
      
      // Redirect to login on auth error
      const loginUrl = new URL('/admin/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      const redirectResponse = NextResponse.redirect(loginUrl);
      
      // Add security headers
      Object.entries(securityHeaders).forEach(([key, value]) => {
        redirectResponse.headers.set(key, value);
      });
      
      return redirectResponse;
    }
  }

  // API-specific security for non-auth routes
  if (pathname.startsWith('/api') && !pathname.startsWith('/api/auth')) {
    // Add API-specific headers
    response.headers.set('X-API-Version', '1.0');
    response.headers.set('X-Request-ID', crypto.randomUUID());
    
    // Log API access
    console.log(`API access: ${request.method} ${pathname} from IP: ${clientIP}`);
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Include all API routes and admin routes for security
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
