import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const type = searchParams.get('type') || 'overview';

    switch (type) {
      case 'overview':
        return await getOverviewAnalytics(days);
      case 'pages':
        return await getPageAnalytics(days);
      case 'traffic':
        return await getTrafficAnalytics(days);
      default:
        return await getOverviewAnalytics(days);
    }
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}

async function getOverviewAnalytics(days: number) {
  try {
    // Get basic stats
    const statsQuery = `
      SELECT 
        COUNT(*) as total_views,
        COUNT(DISTINCT ip_address) as unique_visitors,
        COUNT(DISTINCT session_id) as sessions
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
    `;
    
    const statsResult = await DatabaseService.query(statsQuery);
    const stats = statsResult.rows[0];

    // Get daily analytics
    const dailyQuery = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as views,
        COUNT(DISTINCT ip_address) as unique_visitors,
        COUNT(DISTINCT session_id) as sessions
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    const dailyResult = await DatabaseService.query(dailyQuery);
    const dailyData = dailyResult.rows;

    // Get top pages
    const pagesQuery = `
      SELECT 
        page_path,
        COUNT(*) as views,
        COUNT(DISTINCT ip_address) as unique_visitors
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY page_path
      ORDER BY views DESC
      LIMIT 10
    `;
    
    const pagesResult = await DatabaseService.query(pagesQuery);
    const topPages = pagesResult.rows;

    // Get referrers
    const referrersQuery = `
      SELECT 
        CASE 
          WHEN referrer IS NULL OR referrer = '' THEN 'Direct'
          WHEN referrer LIKE '%google%' THEN 'Google'
          WHEN referrer LIKE '%github%' THEN 'GitHub'
          WHEN referrer LIKE '%linkedin%' THEN 'LinkedIn'
          WHEN referrer LIKE '%twitter%' THEN 'Twitter'
          ELSE 'Other'
        END as source,
        COUNT(*) as views
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY source
      ORDER BY views DESC
    `;
    
    const referrersResult = await DatabaseService.query(referrersQuery);
    const referrers = referrersResult.rows;

    // Calculate growth rate
    const previousPeriodQuery = `
      SELECT COUNT(*) as previous_views
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days * 2} days'
        AND created_at < NOW() - INTERVAL '${days} days'
    `;
    
    const previousResult = await DatabaseService.query(previousPeriodQuery);
    const previousViews = parseInt(previousResult.rows[0].previous_views);
    const currentViews = parseInt(stats.total_views);
    const growthRate = previousViews > 0 
      ? ((currentViews - previousViews) / previousViews * 100).toFixed(1)
      : '0';

    return NextResponse.json({
      overview: {
        total_views: parseInt(stats.total_views),
        unique_visitors: parseInt(stats.unique_visitors),
        sessions: parseInt(stats.sessions),
        growth_rate: parseFloat(growthRate)
      },
      daily_data: dailyData,
      top_pages: topPages,
      referrers: referrers
    });
  } catch (error) {
    console.error('Error in getOverviewAnalytics:', error);
    throw error;
  }
}

async function getPageAnalytics(days: number) {
  try {
    const query = `
      SELECT 
        page_path,
        COUNT(*) as views,
        COUNT(DISTINCT ip_address) as unique_visitors,
        COUNT(DISTINCT session_id) as sessions,
        AVG(EXTRACT(EPOCH FROM (
          LEAD(created_at) OVER (PARTITION BY session_id ORDER BY created_at) - created_at
        ))) as avg_time_on_page
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY page_path
      ORDER BY views DESC
    `;
    
    const result = await DatabaseService.query(query);
    
    return NextResponse.json({
      pages: result.rows.map(row => ({
        ...row,
        avg_time_on_page: row.avg_time_on_page ? Math.round(row.avg_time_on_page) : null
      }))
    });
  } catch (error) {
    console.error('Error in getPageAnalytics:', error);
    throw error;
  }
}

async function getTrafficAnalytics(days: number) {
  try {
    // Hourly traffic pattern
    const hourlyQuery = `
      SELECT 
        EXTRACT(HOUR FROM created_at) as hour,
        COUNT(*) as views
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY EXTRACT(HOUR FROM created_at)
      ORDER BY hour
    `;
    
    const hourlyResult = await DatabaseService.query(hourlyQuery);
    
    // Device/Browser info (simplified)
    const deviceQuery = `
      SELECT 
        CASE 
          WHEN user_agent LIKE '%Mobile%' THEN 'Mobile'
          WHEN user_agent LIKE '%Tablet%' THEN 'Tablet'
          ELSE 'Desktop'
        END as device_type,
        COUNT(*) as views
      FROM page_views 
      WHERE created_at >= NOW() - INTERVAL '${days} days'
        AND user_agent IS NOT NULL
      GROUP BY device_type
      ORDER BY views DESC
    `;
    
    const deviceResult = await DatabaseService.query(deviceQuery);
    
    return NextResponse.json({
      hourly_traffic: hourlyResult.rows,
      device_breakdown: deviceResult.rows
    });
  } catch (error) {
    console.error('Error in getTrafficAnalytics:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Get client IP
    const ip_address = request.headers.get('x-forwarded-for') ||
                      request.headers.get('x-real-ip') ||
                      body.ip_address ||
                      'unknown';

    // Track page view
    const query = `
      INSERT INTO page_views (
        page_path, user_agent, ip_address, referrer, session_id
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `;

    const values = [
      body.page_path,
      body.user_agent || request.headers.get('user-agent'),
      ip_address,
      body.referrer,
      body.session_id
    ];

    const result = await DatabaseService.query(query, values);

    return NextResponse.json({ success: true, id: result.rows[0].id });
  } catch (error) {
    console.error('Error tracking page view:', error);
    return NextResponse.json(
      { error: 'Failed to track page view' },
      { status: 500 }
    );
  }
}
