'use client'

import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Image, Upload, Eye, Scan, Zap, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface ImageAnalysisProjectProps {
  onBack: () => void;
  projectData: any;
}

interface AnalysisResult {
  description: string;
  objects: Array<{ name: string; confidence: number; color: string }>;
  emotions: Array<{ emotion: string; confidence: number }>;
  colors: Array<{ name: string; hex: string; percentage: number }>;
  tags: string[];
  technical: {
    dimensions: string;
    format: string;
    size: string;
    quality: string;
  };
}

export function ImageAnalysisProject({ onBack, projectData }: ImageAnalysisProjectProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
        setAnalysisResult(null);
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeImage = async () => {
    if (!selectedImage) return;
    
    setIsAnalyzing(true);
    setAnalysisProgress(0);
    
    // Simulate analysis progress
    const steps = [
      { progress: 20, message: 'Loading image...' },
      { progress: 40, message: 'Detecting objects...' },
      { progress: 60, message: 'Analyzing colors...' },
      { progress: 80, message: 'Processing emotions...' },
      { progress: 100, message: 'Generating description...' }
    ];
    
    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 800));
      setAnalysisProgress(step.progress);
    }
    
    // Mock analysis results
    const mockResult: AnalysisResult = {
      description: "This image shows a vibrant and dynamic scene with excellent composition. The lighting creates a warm, inviting atmosphere that draws the viewer's attention to the central elements. The color palette is well-balanced, featuring rich tones that complement each other beautifully.",
      objects: [
        { name: 'Person', confidence: 95, color: 'bg-blue-500' },
        { name: 'Building', confidence: 87, color: 'bg-green-500' },
        { name: 'Sky', confidence: 92, color: 'bg-cyan-500' },
        { name: 'Tree', confidence: 78, color: 'bg-emerald-500' },
        { name: 'Car', confidence: 65, color: 'bg-orange-500' }
      ],
      emotions: [
        { emotion: 'Joy', confidence: 85 },
        { emotion: 'Serenity', confidence: 72 },
        { emotion: 'Wonder', confidence: 68 }
      ],
      colors: [
        { name: 'Deep Blue', hex: '#1e40af', percentage: 35 },
        { name: 'Warm Orange', hex: '#ea580c', percentage: 25 },
        { name: 'Forest Green', hex: '#166534', percentage: 20 },
        { name: 'Golden Yellow', hex: '#ca8a04', percentage: 15 },
        { name: 'Pure White', hex: '#ffffff', percentage: 5 }
      ],
      tags: ['landscape', 'urban', 'daylight', 'architecture', 'nature', 'vibrant', 'modern'],
      technical: {
        dimensions: '1920 × 1080',
        format: 'JPEG',
        size: '2.4 MB',
        quality: 'High'
      }
    };
    
    setAnalysisResult(mockResult);
    setIsAnalyzing(false);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-indigo-50 dark:from-blue-900 dark:via-cyan-900 dark:to-indigo-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Playground
          </Button>
          
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
              <Image className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold gradient-text">AI Image Analysis</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Upload images for intelligent analysis and insights
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Image Upload Panel */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Upload Area */}
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Image Upload
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  onClick={triggerFileInput}
                  className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 transition-colors"
                >
                  {selectedImage ? (
                    <div className="space-y-4">
                      <img
                        src={selectedImage}
                        alt="Uploaded"
                        className="max-w-full max-h-64 mx-auto rounded-lg shadow-lg"
                      />
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Click to change image
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                        <Upload className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <p className="text-lg font-medium">Upload an image</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Drag and drop or click to select
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                
                {selectedImage && (
                  <Button
                    onClick={analyzeImage}
                    disabled={isAnalyzing}
                    className="w-full mt-4 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
                  >
                    {isAnalyzing ? (
                      <>
                        <Scan className="w-4 h-4 mr-2 animate-pulse" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Eye className="w-4 h-4 mr-2" />
                        Analyze Image
                      </>
                    )}
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Analysis Progress */}
            {isAnalyzing && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card className="glass-effect">
                  <CardContent className="pt-6">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Analysis Progress</span>
                        <span className="text-sm text-gray-600">{analysisProgress}%</span>
                      </div>
                      <Progress value={analysisProgress} className="h-2" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </motion.div>

          {/* Analysis Results Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-6"
          >
            {analysisResult ? (
              <>
                {/* Description */}
                <Card className="glass-effect">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="w-5 h-5" />
                      AI Description
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm leading-relaxed">{analysisResult.description}</p>
                  </CardContent>
                </Card>

                {/* Detected Objects */}
                <Card className="glass-effect">
                  <CardHeader>
                    <CardTitle>Detected Objects</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysisResult.objects.map((obj, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${obj.color}`} />
                            <span className="font-medium">{obj.name}</span>
                          </div>
                          <Badge variant="secondary">{obj.confidence}%</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Color Palette */}
                <Card className="glass-effect">
                  <CardHeader>
                    <CardTitle>Color Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysisResult.colors.map((color, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-6 h-6 rounded-full border-2 border-gray-200"
                              style={{ backgroundColor: color.hex }}
                            />
                            <span className="font-medium">{color.name}</span>
                          </div>
                          <Badge variant="secondary">{color.percentage}%</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Tags */}
                <Card className="glass-effect">
                  <CardHeader>
                    <CardTitle>Tags</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {analysisResult.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-0">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card className="glass-effect h-96">
                <CardContent className="flex items-center justify-center h-full">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <Scan className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Upload an image to see AI analysis results</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
