import React from 'react';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from '@/components/theme-provider';
import { QueryProvider } from '@/components/query-provider';
import { ErrorBoundary } from '@/components/error-boundary';
import { initializeEnvironmentValidation } from '@/lib/env-validation';
import { trackWebVitals } from '@/lib/performance';
import { ClientInitialization } from '@/components/client-initialization';

const inter = Inter({ subsets: ['latin'] });

// Initialize environment validation at module load
if (typeof window === 'undefined') {
  // Server-side initialization
  initializeEnvironmentValidation();
}

export const metadata: Metadata = {
  title: {
    default: 'Khiw Nitithachot - Data & AI Solutions Engineer',
    template: '%s | Khiw Nitithachot',
  },
  description:
    'Professional portfolio of <PERSON>hiw Nitithachot, Data & AI Solutions Engineer. Expertise in machine learning, data engineering, and full-stack development. Building intelligent solutions with cutting-edge AI technologies.',
  keywords: [
    'Khiw Nitithachot',
    'Data Engineer',
    'AI Engineer',
    'Machine Learning',
    'Data Science',
    'Software Engineer',
    'Python',
    'TensorFlow',
    'PyTorch',
    'React',
    'Next.js',
    'Portfolio',
  ],
  authors: [{ name: 'Khiw Nitithachot', url: 'https://getintheq.space' }],
  creator: 'Khiw Nitithachot',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://getintheq.space',
    title: 'Khiw Nitithachot - Data & AI Solutions Engineer',
    description:
      'Professional portfolio showcasing AI/ML projects, data engineering solutions, and software development expertise.',
    siteName: 'Khiw Nitithachot Portfolio',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Khiw Nitithachot - Data & AI Solutions Engineer',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Khiw Nitithachot - Data & AI Solutions Engineer',
    description:
      'Professional portfolio showcasing AI/ML projects, data engineering solutions, and software development expertise.',
    images: ['/og-image.jpg'],
    creator: '@khiwniti',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
  alternates: {
    canonical: 'https://getintheq.space',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        {/* Preload critical resources */}
        <link
          rel='preload'
          href='https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap'
          as='style'
        />
        <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='' />

        {/* Security headers via meta tags */}
        <meta httpEquiv='X-Content-Type-Options' content='nosniff' />
        <meta httpEquiv='X-Frame-Options' content='DENY' />
        <meta httpEquiv='X-XSS-Protection' content='1; mode=block' />
        <meta name='referrer' content='strict-origin-when-cross-origin' />

        {/* Performance hints */}
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <meta name='theme-color' content='#000000' />
      </head>
      <body className={inter.className}>
        <ErrorBoundary>
          <ThemeProvider
            attribute='class'
            defaultTheme='system'
            enableSystem
            disableTransitionOnChange
          >
            <QueryProvider>
              <ClientInitialization />
              {children}
            </QueryProvider>
          </ThemeProvider>
        </ErrorBoundary>

        {/* Performance monitoring script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Early performance monitoring
              if (typeof window !== 'undefined') {
                window.addEventListener('load', function() {
                  // Report initial page load performance
                  if (window.performance && window.performance.getEntriesByType) {
                    const perfData = window.performance.getEntriesByType('navigation')[0];
                    if (perfData && perfData.loadEventEnd) {
                      const loadTime = perfData.loadEventEnd - perfData.fetchStart;
                      console.log('📊 Page load time:', Math.round(loadTime), 'ms');
                    }
                  }
                });
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
