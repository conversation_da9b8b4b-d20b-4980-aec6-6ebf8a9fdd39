/**
 * Simple rate limiting middleware for Cloudflare Workers
 */

import { Context, Next } from 'hono';
import type { Env } from '../index';

// Simple in-memory rate limiting (for demonstration)
// In production, use Durable Objects or KV storage
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export const rateLimiter = async (c: Context<{ Bindings: Env }>, next: Next) => {
  const clientIP = c.req.header('CF-Connecting-IP') || c.req.header('x-forwarded-for') || 'unknown';
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxRequests = 100; // 100 requests per window
  
  // Clean up old entries
  const cutoff = now - windowMs;
  for (const [ip, data] of requestCounts.entries()) {
    if (data.resetTime < cutoff) {
      requestCounts.delete(ip);
    }
  }
  
  // Get or create rate limit data for this IP
  let rateLimitData = requestCounts.get(clientIP);
  if (!rateLimitData || rateLimitData.resetTime < cutoff) {
    rateLimitData = { count: 0, resetTime: now + windowMs };
    requestCounts.set(clientIP, rateLimitData);
  }
  
  // Check rate limit
  if (rateLimitData.count >= maxRequests) {
    const retryAfter = Math.ceil((rateLimitData.resetTime - now) / 1000);
    
    return c.json({
      error: 'Rate limit exceeded',
      limit: maxRequests,
      window: windowMs / 1000,
      retry_after: retryAfter
    }, 429);
  }
  
  // Increment counter
  rateLimitData.count++;
  
  // Set rate limit headers
  c.res.headers.set('X-RateLimit-Limit', maxRequests.toString());
  c.res.headers.set('X-RateLimit-Remaining', (maxRequests - rateLimitData.count).toString());
  c.res.headers.set('X-RateLimit-Reset', rateLimitData.resetTime.toString());
  
  await next();
};