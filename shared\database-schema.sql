-- Comprehensive Database Schema for Portfolio Management System

-- Users and Authentication
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  role VARCHAR(50) DEFAULT 'admin',
  avatar_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP,
  is_active BOOLEAN DEFAULT true
);

-- Projects Management
CREATE TABLE projects (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  long_description TEXT,
  github_url VARCHAR(500),
  demo_url VARCHAR(500),
  image_url TEXT,
  technologies JSONB DEFAULT '[]',
  category VARCHAR(100),
  status VARCHAR(50) DEFAULT 'active', -- active, archived, draft
  featured <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  github_data JSONB, -- stars, forks, language, etc.
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP
);

-- Blog Posts Management
CREATE TABLE blog_posts (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  excerpt TEXT,
  content TEXT NOT NULL,
  featured_image TEXT,
  status VARCHAR(50) DEFAULT 'draft', -- draft, published, archived
  author_id INTEGER REFERENCES users(id),
  category_id INTEGER,
  tags JSONB DEFAULT '[]',
  meta_title VARCHAR(255),
  meta_description TEXT,
  reading_time INTEGER, -- in minutes
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP
);

-- Blog Categories
CREATE TABLE blog_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  color VARCHAR(7), -- hex color
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- AI Playground Projects
CREATE TABLE playground_projects (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  project_type VARCHAR(100) NOT NULL, -- text-generator, image-analysis, etc.
  icon VARCHAR(100),
  gradient VARCHAR(100),
  tags JSONB DEFAULT '[]',
  configuration JSONB DEFAULT '{}', -- project-specific settings
  status VARCHAR(50) DEFAULT 'active',
  featured BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Analytics and Tracking
CREATE TABLE page_views (
  id SERIAL PRIMARY KEY,
  page_path VARCHAR(500) NOT NULL,
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  session_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Contact Form Submissions
CREATE TABLE contact_submissions (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  subject VARCHAR(255),
  message TEXT NOT NULL,
  status VARCHAR(50) DEFAULT 'new', -- new, read, replied, archived
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Media Library
CREATE TABLE media_files (
  id SERIAL PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type VARCHAR(100),
  alt_text TEXT,
  caption TEXT,
  uploaded_by INTEGER REFERENCES users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- System Settings
CREATE TABLE settings (
  id SERIAL PRIMARY KEY,
  key VARCHAR(255) UNIQUE NOT NULL,
  value TEXT,
  type VARCHAR(50) DEFAULT 'string', -- string, number, boolean, json
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_featured ON projects(featured);
CREATE INDEX idx_blog_posts_status ON blog_posts(status);
CREATE INDEX idx_blog_posts_published_at ON blog_posts(published_at);
CREATE INDEX idx_page_views_created_at ON page_views(created_at);
CREATE INDEX idx_page_views_page_path ON page_views(page_path);

-- Insert default admin user (password: admin123 - change in production!)
INSERT INTO users (email, password_hash, name, role) VALUES 
('<EMAIL>', '$2b$10$rQZ8qVZ8qVZ8qVZ8qVZ8qO', 'Admin User', 'admin');

-- Insert default settings
INSERT INTO settings (key, value, type, description) VALUES 
('site_title', 'Khiwniti Portfolio', 'string', 'Main site title'),
('site_description', 'AI Engineer & Full Stack Developer Portfolio', 'string', 'Site description for SEO'),
('github_username', 'khiwniti', 'string', 'GitHub username for API integration'),
('contact_email', '<EMAIL>', 'string', 'Contact email address'),
('analytics_enabled', 'true', 'boolean', 'Enable analytics tracking'),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode');

-- Insert default blog category
INSERT INTO blog_categories (name, slug, description, color) VALUES 
('Technology', 'technology', 'Posts about technology and development', '#3B82F6'),
('AI & Machine Learning', 'ai-ml', 'Posts about AI and ML topics', '#8B5CF6'),
('Tutorials', 'tutorials', 'Step-by-step tutorials and guides', '#10B981');
