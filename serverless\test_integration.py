#!/usr/bin/env python3
"""
Test script to verify frontend-backend integration for AI playground
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# Test server URL
BASE_URL = "http://localhost:8000"

async def test_playground_endpoints():
    """Test all AI playground endpoints"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("🧪 Testing AI Playground Backend Integration\n")
        
        # Test 1: Health check
        print("1. Testing health check...")
        try:
            response = await client.get(f"{BASE_URL}/")
            assert response.status_code == 200
            data = response.json()
            print(f"   ✅ Health check passed: {data['message']}")
        except Exception as e:
            print(f"   ❌ Health check failed: {e}")
            return
        
        # Test 2: Chat endpoint
        print("\n2. Testing chat endpoint...")
        try:
            chat_payload = {
                "content": "Hello, how are you?",
                "personality": "friendly"
            }
            response = await client.post(
                f"{BASE_URL}/api/playground/chat",
                json=chat_payload
            )
            assert response.status_code == 200
            data = response.json()
            print(f"   ✅ Chat response: {data['message'][:50]}...")
            print(f"   ⏱️ Response time: {data['response_time']}s")
        except Exception as e:
            print(f"   ❌ Chat test failed: {e}")
        
        # Test 3: Text generation endpoint
        print("\n3. Testing text generation...")
        try:
            text_payload = {
                "prompt": "artificial intelligence",
                "text_type": "blog",
                "model": "gpt4",
                "word_count": 100
            }
            response = await client.post(
                f"{BASE_URL}/api/playground/generate-text",
                json=text_payload
            )
            assert response.status_code == 200
            data = response.json()
            print(f"   ✅ Generated {data['word_count']} words")
            print(f"   📝 Text sample: {data['generated_text'][:100]}...")
            print(f"   ⏱️ Generation time: {data['generation_time']}s")
        except Exception as e:
            print(f"   ❌ Text generation test failed: {e}")
        
        # Test 4: Sentiment analysis endpoint
        print("\n4. Testing sentiment analysis...")
        try:
            sentiment_payload = {
                "text": "I absolutely love this new product! It's amazing and exceeded all my expectations."
            }
            response = await client.post(
                f"{BASE_URL}/api/playground/analyze-sentiment",
                json=sentiment_payload
            )
            assert response.status_code == 200
            data = response.json()
            print(f"   ✅ Sentiment: {data['overall']['sentiment']}")
            print(f"   📊 Confidence: {data['overall']['confidence']:.2%}")
            print(f"   🎭 Emotions detected: {len(data['emotions'])}")
            print(f"   🔑 Keywords found: {len(data['keywords'])}")
            print(f"   ⏱️ Analysis time: {data['analysis_time']}s")
        except Exception as e:
            print(f"   ❌ Sentiment analysis test failed: {e}")
        
        # Test 5: Code assistance endpoint
        print("\n5. Testing code assistance...")
        try:
            code_payload = {
                "code": "def hello_world():\n    print('Hello, World!')",
                "language": "python",
                "task": "explain"
            }
            response = await client.post(
                f"{BASE_URL}/api/playground/code-assist",
                json=code_payload
            )
            assert response.status_code == 200
            data = response.json()
            print(f"   ✅ Code analysis completed")
            print(f"   📝 Result: {data['result'][:100]}...")
            print(f"   💡 Suggestions: {len(data['suggestions'])}")
            print(f"   ⏱️ Analysis time: {data['analysis_time']}s")
        except Exception as e:
            print(f"   ❌ Code assistance test failed: {e}")
        
        print("\n🎉 Integration tests completed!")

async def test_error_handling():
    """Test error handling scenarios"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("\n🔍 Testing Error Handling\n")
        
        # Test invalid chat request
        print("1. Testing invalid chat request...")
        try:
            response = await client.post(
                f"{BASE_URL}/api/playground/chat",
                json={"content": "", "personality": "invalid"}  # Empty content and invalid personality
            )
            if response.status_code == 422:
                print("   ✅ Validation error handled correctly")
            else:
                print(f"   ⚠️ Unexpected status code: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error handling test failed: {e}")
        
        # Test text generation with invalid parameters
        print("\n2. Testing invalid text generation...")
        try:
            response = await client.post(
                f"{BASE_URL}/api/playground/generate-text",
                json={"prompt": "", "word_count": 1000}  # Empty prompt, excessive word count
            )
            if response.status_code == 422:
                print("   ✅ Validation error handled correctly")
            else:
                print(f"   ⚠️ Unexpected status code: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error handling test failed: {e}")
        
        print("\n✅ Error handling tests completed!")

async def test_performance():
    """Test basic performance metrics"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("\n⚡ Testing Performance\n")
        
        # Test multiple concurrent requests
        print("1. Testing concurrent chat requests...")
        try:
            tasks = []
            for i in range(5):
                task = client.post(
                    f"{BASE_URL}/api/playground/chat",
                    json={"content": f"Test message {i}", "personality": "helpful"}
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            success_count = sum(1 for r in responses if r.status_code == 200)
            print(f"   ✅ {success_count}/5 concurrent requests successful")
            
            # Calculate average response time
            response_times = []
            for response in responses:
                if response.status_code == 200:
                    data = response.json()
                    response_times.append(data['response_time'])
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                print(f"   ⏱️ Average response time: {avg_time:.3f}s")
        except Exception as e:
            print(f"   ❌ Concurrent requests test failed: {e}")
        
        print("\n🏁 Performance tests completed!")

def print_integration_summary():
    """Print integration setup summary"""
    print("=" * 60)
    print("🚀 Frontend-Backend Integration Summary")
    print("=" * 60)
    print("✅ Backend endpoints created:")
    print("   • /api/playground/chat - AI chatbot with personalities")
    print("   • /api/playground/generate-text - AI text generation")
    print("   • /api/playground/analyze-sentiment - Sentiment analysis")
    print("   • /api/playground/code-assist - Code assistance")
    print("   • /api/playground/analyze-image - Image analysis")
    print("")
    print("✅ Frontend components updated:")
    print("   • TextGeneratorProject - Now uses real API")
    print("   • ChatBotProject - Connected to backend")
    print("   • SentimentAnalysisProject - Real sentiment analysis")
    print("   • Fallback mechanisms for offline/error states")
    print("")
    print("✅ API integration features:")
    print("   • Type-safe API client with TypeScript")
    print("   • Error handling with graceful fallbacks")
    print("   • Loading states and user feedback")
    print("   • Environment-aware configuration")
    print("")
    print("🔧 To test locally:")
    print("   1. Start backend: cd serverless && ./deploy.sh local")
    print("   2. Start frontend: npm run dev")
    print("   3. Visit: http://localhost:3000/playground")
    print("=" * 60)

async def main():
    """Main test function"""
    print_integration_summary()
    
    print("\n⏳ Starting integration tests...")
    print("   (Make sure the backend is running on localhost:8000)")
    
    try:
        await test_playground_endpoints()
        await test_error_handling()
        await test_performance()
        
        print("\n" + "=" * 60)
        print("🎯 All tests completed! Frontend-backend integration is ready.")
        print("📱 You can now test the playground at: http://localhost:3000/playground")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())