'use client'

import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Upload, 
  Github,
  ExternalLink,
  Star,
  Zap,
  Code,
  Globe,
  Database,
  Smartphone,
  Cloud,
  Shield,
  Cpu,
  Layers,
  CheckCircle,
  AlertCircle,
  XCircle,
  Sparkles,
  Target,
  TrendingUp,
  Calendar,
  FileText,
  Image as ImageIcon,
  Link as LinkIcon,
  Plus,
  X
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import Link from 'next/link';
import Image from 'next/image';

const projectCategories = [
  { id: 'ai-ml', name: 'AI & Machine Learning', icon: Sparkles, color: 'from-purple-500 to-pink-500' },
  { id: 'web', name: 'Web Development', icon: Globe, color: 'from-blue-500 to-cyan-500' },
  { id: 'backend', name: 'Backend & APIs', icon: Database, color: 'from-green-500 to-emerald-500' },
  { id: 'mobile', name: 'Mobile Development', icon: Smartphone, color: 'from-orange-500 to-red-500' },
  { id: 'devops', name: 'DevOps & Cloud', icon: Cloud, color: 'from-indigo-500 to-purple-500' },
  { id: 'security', name: 'Security & Privacy', icon: Shield, color: 'from-gray-500 to-gray-700' }
];

const technologyStack = {
  'Frontend': ['React', 'Vue.js', 'Angular', 'Next.js', 'Nuxt.js', 'Svelte', 'TypeScript', 'JavaScript'],
  'Backend': ['Node.js', 'Python', 'Java', 'Go', 'Rust', 'PHP', 'C#', 'Ruby'],
  'Database': ['PostgreSQL', 'MongoDB', 'MySQL', 'Redis', 'DynamoDB', 'Firebase', 'Supabase'],
  'AI/ML': ['TensorFlow', 'PyTorch', 'Scikit-learn', 'OpenAI', 'Hugging Face', 'LangChain'],
  'Cloud': ['AWS', 'Google Cloud', 'Azure', 'Vercel', 'Netlify', 'Docker', 'Kubernetes'],
  'Mobile': ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Expo', 'Ionic']
};

const projectComplexity = [
  { level: 'Simple', description: 'Basic functionality, minimal dependencies', color: 'text-green-600', points: 25 },
  { level: 'Moderate', description: 'Multiple features, some integrations', color: 'text-yellow-600', points: 50 },
  { level: 'Complex', description: 'Advanced features, multiple systems', color: 'text-orange-600', points: 75 },
  { level: 'Enterprise', description: 'High-scale, mission-critical system', color: 'text-red-600', points: 100 }
];

interface ProjectHealth {
  score: number;
  title: { status: 'good' | 'warning' | 'error'; message: string };
  description: { status: 'good' | 'warning' | 'error'; message: string };
  technologies: { status: 'good' | 'warning' | 'error'; message: string };
  documentation: { status: 'good' | 'warning' | 'error'; message: string };
  repository: { status: 'good' | 'warning' | 'error'; message: string };
}

interface ProjectFormData {
  title: string;
  description: string;
  long_description: string;
  github_url: string;
  demo_url: string;
  image_url: string;
  technologies: string[];
  category: string;
  status: 'active' | 'draft' | 'archived';
  featured: boolean;
  complexity: string;
  primaryLanguage: string;
  framework: string;
  deploymentUrl: string;
  documentationUrl: string;
  licenseType: string;
  startDate: string;
  endDate: string;
  priority: number;
  tags: string[];
}

export default function NewProject() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<ProjectFormData>({
    title: '',
    description: '',
    long_description: '',
    github_url: '',
    demo_url: '',
    image_url: '',
    technologies: [],
    category: '',
    status: 'draft',
    featured: false,
    complexity: '',
    primaryLanguage: '',
    framework: '',
    deploymentUrl: '',
    documentationUrl: '',
    licenseType: 'MIT',
    startDate: '',
    endDate: '',
    priority: 0,
    tags: []
  });

  const [projectHealth, setProjectHealth] = useState<ProjectHealth>({
    score: 0,
    title: { status: 'error', message: 'Project title is required' },
    description: { status: 'error', message: 'Description is required' },
    technologies: { status: 'error', message: 'Select at least 2 technologies' },
    documentation: { status: 'warning', message: 'Add documentation URL for better visibility' },
    repository: { status: 'warning', message: 'GitHub repository recommended' }
  });

  const updateTitle = (title: string) => {
    setFormData(prev => ({ 
      ...prev, 
      title
    }));
    analyzeProjectHealth();
  };

  const addTechnology = (tech: string) => {
    if (tech && !formData.technologies.includes(tech)) {
      setFormData(prev => ({
        ...prev,
        technologies: [...prev.technologies, tech]
      }));
      analyzeProjectHealth();
    }
  };

  const removeTechnology = (tech: string) => {
    setFormData(prev => ({
      ...prev,
      technologies: prev.technologies.filter(t => t !== tech)
    }));
    analyzeProjectHealth();
  };

  const addTag = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const analyzeProjectHealth = () => {
    let score = 0;
    const newHealth = { ...projectHealth };

    // Title analysis
    if (formData.title.length === 0) {
      newHealth.title = { status: 'error', message: 'Project title is required' };
    } else if (formData.title.length < 10) {
      newHealth.title = { status: 'warning', message: 'Title could be more descriptive' };
      score += 10;
    } else {
      newHealth.title = { status: 'good', message: 'Good title length and clarity' };
      score += 20;
    }

    // Description analysis
    if (formData.description.length === 0) {
      newHealth.description = { status: 'error', message: 'Description is required' };
    } else if (formData.description.length < 50) {
      newHealth.description = { status: 'warning', message: 'Description could be more detailed' };
      score += 10;
    } else {
      newHealth.description = { status: 'good', message: 'Well-detailed description' };
      score += 20;
    }

    // Technologies analysis
    if (formData.technologies.length === 0) {
      newHealth.technologies = { status: 'error', message: 'Select at least 2 technologies' };
    } else if (formData.technologies.length < 2) {
      newHealth.technologies = { status: 'warning', message: 'Add more technologies for better visibility' };
      score += 10;
    } else {
      newHealth.technologies = { status: 'good', message: 'Good technology stack coverage' };
      score += 20;
    }

    // Documentation analysis
    if (!formData.documentationUrl && !formData.long_description) {
      newHealth.documentation = { status: 'warning', message: 'Add documentation for better project understanding' };
      score += 5;
    } else {
      newHealth.documentation = { status: 'good', message: 'Documentation available' };
      score += 20;
    }

    // Repository analysis
    if (!formData.github_url) {
      newHealth.repository = { status: 'warning', message: 'GitHub repository highly recommended' };
      score += 5;
    } else {
      newHealth.repository = { status: 'good', message: 'Repository linked' };
      score += 20;
    }

    newHealth.score = Math.min(score, 100);
    setProjectHealth(newHealth);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // In a real app, you'd upload to a cloud storage service
      const imageUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, image_url: imageUrl }));
    }
  };

  const fetchGitHubData = async () => {
    if (!formData.github_url) return;
    
    try {
      const urlParts = formData.github_url.split('/');
      const owner = urlParts[urlParts.length - 2];
      const repo = urlParts[urlParts.length - 1];
      
      const response = await fetch(`https://api.github.com/repos/${owner}/${repo}`);
      const data = await response.json();
      
      if (response.ok) {
        setFormData(prev => ({
          ...prev,
          description: prev.description || data.description,
          technologies: prev.technologies.length > 0 ? prev.technologies : [data.language].filter(Boolean)
        }));
        analyzeProjectHealth();
      }
    } catch (error) {
      console.error('Failed to fetch GitHub data:', error);
    }
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const newProject = await response.json();
      console.log('Project created:', newProject);
      
      router.push('/admin/projects');
    } catch (error) {
      console.error('Failed to create project:', error);
      alert(error instanceof Error ? error.message : 'Failed to create project');
    } finally {
      setIsLoading(false);
    }
  };

  const getHealthIcon = (status: 'good' | 'warning' | 'error') => {
    switch (status) {
      case 'good': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const steps = [
    { id: 1, name: 'Basic Info', description: 'Project details' },
    { id: 2, name: 'Technology', description: 'Tech stack' },
    { id: 3, name: 'Media & Links', description: 'URLs and images' },
    { id: 4, name: 'Settings', description: 'Configuration' },
    { id: 5, name: 'Review', description: 'Final check' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/admin/projects">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Projects
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold gradient-text">Create Project</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Showcase your work with detailed project information
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button variant="outline" size="sm">
              <Save className="w-4 h-4 mr-2" />
              Save Draft
            </Button>
          </div>
        </div>

        {/* Progress Steps */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                    currentStep >= step.id 
                      ? 'bg-blue-500 border-blue-500 text-white' 
                      : 'border-gray-300 text-gray-400'
                  }`}>
                    {currentStep > step.id ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      step.id
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`h-1 w-16 mx-2 transition-colors ${
                      currentStep > step.id ? 'bg-blue-500' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-4">
              {steps.map((step) => (
                <div key={step.id} className="flex flex-col items-center w-20">
                  <span className="text-sm font-medium">{step.name}</span>
                  <span className="text-xs text-gray-500">{step.description}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="title">Project Title *</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => updateTitle(e.target.value)}
                        placeholder="Enter your project name"
                        className="text-lg"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category">Category *</Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {projectCategories.map((category) => {
                          const Icon = category.icon;
                          return (
                            <motion.div
                              key={category.id}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                                formData.category === category.id
                                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                              onClick={() => setFormData(prev => ({ ...prev, category: category.id }))}
                            >
                              <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center mb-2`}>
                                <Icon className="w-4 h-4 text-white" />
                              </div>
                              <h4 className="font-medium text-sm">{category.name}</h4>
                            </motion.div>
                          );
                        })}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Short Description *</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Brief description of your project (will appear on project cards)"
                        rows={3}
                      />
                      <p className="text-sm text-gray-500">{formData.description.length}/200 characters</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="long_description">Detailed Description</Label>
                      <Textarea
                        id="long_description"
                        value={formData.long_description}
                        onChange={(e) => setFormData(prev => ({ ...prev, long_description: e.target.value }))}
                        placeholder="Detailed project description with features, challenges, and solutions"
                        rows={6}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Project Complexity</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {projectComplexity.map((complexity) => (
                          <motion.div
                            key={complexity.level}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                              formData.complexity === complexity.level
                                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => setFormData(prev => ({ ...prev, complexity: complexity.level }))}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <h4 className={`font-medium ${complexity.color}`}>{complexity.level}</h4>
                              <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div 
                                  className={`h-full ${complexity.color.replace('text-', 'bg-')}`}
                                  style={{ width: `${complexity.points}%` }}
                                />
                              </div>
                            </div>
                            <p className="text-sm text-gray-600">{complexity.description}</p>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Technology Stack</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <Label>Selected Technologies</Label>
                      <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border border-gray-200 rounded-lg">
                        {formData.technologies.length === 0 ? (
                          <span className="text-gray-400 text-sm">Select technologies from the categories below</span>
                        ) : (
                          formData.technologies.map(tech => (
                            <Badge 
                              key={tech} 
                              variant="secondary" 
                              className="cursor-pointer hover:bg-red-100 hover:text-red-800"
                              onClick={() => removeTechnology(tech)}
                            >
                              {tech} ×
                            </Badge>
                          ))
                        )}
                      </div>
                    </div>

                    {Object.entries(technologyStack).map(([category, techs]) => (
                      <div key={category} className="space-y-2">
                        <Label className="flex items-center gap-2">
                          <Code className="w-4 h-4" />
                          {category}
                        </Label>
                        <div className="flex flex-wrap gap-2">
                          {techs.map(tech => (
                            <Badge
                              key={tech}
                              variant={formData.technologies.includes(tech) ? "default" : "outline"}
                              className="cursor-pointer hover:shadow-md transition-all"
                              onClick={() => 
                                formData.technologies.includes(tech) 
                                  ? removeTechnology(tech) 
                                  : addTechnology(tech)
                              }
                            >
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Media & Links</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="image_url">Project Image</Label>
                      <div className="flex gap-4">
                        <Input
                          id="image_url"
                          value={formData.image_url}
                          onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                          placeholder="Enter image URL or upload below"
                        />
                        <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                          <Upload className="w-4 h-4 mr-2" />
                          Upload
                        </Button>
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      {formData.image_url && (
                        <div className="mt-4">
                          <img 
                            src={formData.image_url} 
                            alt="Project preview" 
                            className="w-full max-w-md h-48 object-cover rounded-lg border"
                          />
                        </div>
                      )}
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="github_url">GitHub Repository</Label>
                        <div className="flex gap-2">
                          <Input
                            id="github_url"
                            value={formData.github_url}
                            onChange={(e) => setFormData(prev => ({ ...prev, github_url: e.target.value }))}
                            placeholder="https://github.com/username/repo"
                          />
                          <Button variant="outline" onClick={fetchGitHubData}>
                            <Github className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="demo_url">Live Demo URL</Label>
                        <Input
                          id="demo_url"
                          value={formData.demo_url}
                          onChange={(e) => setFormData(prev => ({ ...prev, demo_url: e.target.value }))}
                          placeholder="https://your-project.com"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Tags</Label>
                      <div className="flex flex-wrap gap-2 mb-2">
                        {formData.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                            {tag} ×
                          </Badge>
                        ))}
                      </div>
                      <Input
                        placeholder="Add tags (press Enter)"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            addTag(e.currentTarget.value);
                            e.currentTarget.value = '';
                          }
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === 4 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Project Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label>Status</Label>
                        <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value as any }))}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="archived">Archived</SelectItem>
                            <SelectItem value="draft">Draft</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>License Type</Label>
                        <Select value={formData.licenseType} onValueChange={(value) => setFormData(prev => ({ ...prev, licenseType: value }))}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="MIT">MIT License</SelectItem>
                            <SelectItem value="Apache-2.0">Apache 2.0</SelectItem>
                            <SelectItem value="GPL-3.0">GPL 3.0</SelectItem>
                            <SelectItem value="BSD-3-Clause">BSD 3-Clause</SelectItem>
                            <SelectItem value="proprietary">Proprietary</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="featured">Featured Project</Label>
                        <Switch
                          id="featured"
                          checked={formData.featured}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === 5 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Review & Create</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Project Preview */}
                    <div className="p-6 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800">
                      <div className="flex gap-4">
                        {formData.image_url && (
                          <div className="w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
                            <img src={formData.image_url} alt={formData.title} className="w-full h-full object-cover" />
                          </div>
                        )}
                        <div className="flex-1">
                          <h3 className="text-xl font-bold mb-2">{formData.title || 'Project Title'}</h3>
                          <p className="text-gray-600 dark:text-gray-400 mb-3">
                            {formData.description || 'Project description...'}
                          </p>
                          <div className="flex flex-wrap gap-1 mb-3">
                            {formData.technologies.slice(0, 5).map(tech => (
                              <Badge key={tech} variant="outline" className="text-xs">{tech}</Badge>
                            ))}
                            {formData.technologies.length > 5 && (
                              <Badge variant="outline" className="text-xs">+{formData.technologies.length - 5}</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            {formData.github_url && (
                              <Button size="sm" variant="outline">
                                <Github className="w-3 h-3 mr-1" />
                                Code
                              </Button>
                            )}
                            {formData.demo_url && (
                              <Button size="sm" variant="outline">
                                <ExternalLink className="w-3 h-3 mr-1" />
                                Demo
                              </Button>
                            )}
                            {formData.featured && (
                              <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                                <Star className="w-3 h-3 mr-1" />
                                Featured
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-6 border-t">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-green-600">Ready to publish</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Target className="w-4 h-4 text-blue-500" />
                          <span className="text-sm text-blue-600">SEO optimized</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => setCurrentStep(currentStep - 1)}>
                          Back
                        </Button>
                        <Button 
                          onClick={() => handleSubmit()}
                          disabled={isLoading}
                          className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
                        >
                          {isLoading ? (
                            <>
                              <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                              Creating...
                            </>
                          ) : (
                            <>
                              <Zap className="w-4 h-4 mr-2" />
                              Create Project
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Health Score */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Project Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className={`text-3xl font-bold ${getHealthScoreColor(projectHealth.score)}`}>
                    {projectHealth.score}/100
                  </div>
                  <Progress value={projectHealth.score} className="mt-2" />
                </div>
                
                <div className="space-y-2">
                  {Object.entries(projectHealth).map(([key, health]) => {
                    if (key === 'score') return null;
                    return (
                      <div key={key} className="flex items-center gap-2 text-sm">
                        {getHealthIcon(health.status)}
                        <span className="flex-1">{health.message}</span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" size="sm" className="w-full justify-start" onClick={() => fileInputRef.current?.click()}>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Image
                </Button>
                
                <Button variant="outline" size="sm" className="w-full justify-start" onClick={fetchGitHubData}>
                  <Github className="w-4 h-4 mr-2" />
                  Import from GitHub
                </Button>
                
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <FileText className="w-4 h-4 mr-2" />
                  Generate README
                </Button>
              </CardContent>
            </Card>

            {/* Progress Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Completion</span>
                  <span className="font-medium">{Math.round((currentStep / steps.length) * 100)}%</span>
                </div>
                <Progress value={(currentStep / steps.length) * 100} />
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Health Score</span>
                  <span className={`font-medium ${getHealthScoreColor(projectHealth.score)}`}>
                    {projectHealth.score}/100
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Navigation */}
        {currentStep > 1 && currentStep < 5 && (
          <div className="flex items-center justify-between mt-8">
            <Button variant="outline" onClick={() => setCurrentStep(currentStep - 1)}>
              Previous
            </Button>
            <Button onClick={() => setCurrentStep(currentStep + 1)}>
              {currentStep === 4 ? 'Review' : 'Next'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}