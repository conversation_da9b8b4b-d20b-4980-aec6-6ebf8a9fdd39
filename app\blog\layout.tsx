import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Blog | Khiw Nitithachot',
  description: 'Insights, tutorials, and thoughts on data science, AI, machine learning, and software engineering by <PERSON><PERSON><PERSON>.',
  keywords: [
    'blog',
    'data science',
    'machine learning',
    'AI',
    'artificial intelligence',
    'tutorials',
    'programming',
    'python',
    'software engineering'
  ],
  openGraph: {
    title: 'Blog | Khiw Nitithachot',
    description: 'Insights, tutorials, and thoughts on data science, AI, machine learning, and software engineering.',
    type: 'website',
    url: 'https://getintheq.space/blog',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Blog | Khiw Nitithachot',
    description: 'Insights, tutorials, and thoughts on data science, AI, machine learning, and software engineering.',
  },
}

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}