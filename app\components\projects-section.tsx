'use client'

import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { ProjectCard3D } from "./project-card-3d";
import { ProjectCardSkeleton } from "./ui/loading-spinner";
import { GitHubStatsCard } from "./github-stats-card";
import { APIClient } from "@/lib/api-client";
import {
  fallbackProjects,
  type Project,
} from "@/lib/projects-data";
import { QUERY_KEYS, CACHE_TIMES, ANIMATION_VARIANTS } from "@/lib/constants";

// Transform GitHub repo to Project format
const transformRepoToProject = (repo: any): Project => ({
  id: repo.id.toString(),
  title: repo.name.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
  description: repo.description || 'No description available',
  technologies: repo.language ? [repo.language] : [],
  githubUrl: repo.html_url,
  stars: repo.stargazers_count,
  forks: repo.forks_count,
  language: repo.language,
  updated_at: repo.updated_at,
});

export function ProjectsSection() {
  const {
    data: repos,
    isLoading: projectsLoading,
    error: projectsError
  } = useQuery({
    queryKey: [QUERY_KEYS.GITHUB_REPOS],
    queryFn: APIClient.getGitHubRepos,
    staleTime: CACHE_TIMES.GITHUB_DATA,
    retry: 2,
  });

  // Transform GitHub repos to projects, or use fallback data
  const displayProjects = repos ? 
    repos.slice(0, 6).map(transformRepoToProject) : 
    fallbackProjects;

  const containerVariants = ANIMATION_VARIANTS.container;

  return (
    <section id="projects" className="py-20 bg-gray-50 dark:bg-gray-900 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">Featured Projects</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Showcasing innovative solutions that blend data science, AI, and software engineering
          </p>
        </motion.div>
        
        {projectsError && (
          <div className="text-center mb-8">
            <p className="text-yellow-600 dark:text-yellow-400">
              Unable to load live projects. Showing featured projects instead.
            </p>
          </div>
        )}
        
        {projectsLoading ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {[1, 2, 3].map((i) => (
              <ProjectCardSkeleton key={i} />
            ))}
          </div>
        ) : (
          <motion.div
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {displayProjects.map((project, index) => (
              <ProjectCard3D key={project.id} project={project} index={index} />
            ))}
          </motion.div>
        )}

        {/* GitHub Stats */}
        <GitHubStatsCard />
      </div>
    </section>
  );
}
