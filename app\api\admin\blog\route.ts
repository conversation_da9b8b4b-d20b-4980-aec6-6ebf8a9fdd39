import { NextRequest, NextResponse } from 'next/server';
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { blogPosts, insertBlogPostSchema } from '@shared/schema';
import { eq, desc } from 'drizzle-orm';

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const limit = searchParams.get('limit');

    let query = db.select().from(blogPosts);
    
    if (status && status !== 'all') {
      query = query.where(eq(blogPosts.status, status as any));
    }
    
    query = query.orderBy(desc(blogPosts.updatedAt));

    if (limit) {
      query = query.limit(parseInt(limit));
    }

    const posts = await query;

    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input using Zod schema
    const validatedData = insertBlogPostSchema.parse(body);
    
    // Calculate reading time
    const wordCount = validatedData.content.split(/\s+/).length;
    const readTime = Math.ceil(wordCount / 200);
    
    // Generate slug if not provided
    const slug = validatedData.slug || validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    
    // Insert blog post
    const newPost = await db.insert(blogPosts).values({
      ...validatedData,
      slug,
      readTime,
      publishedAt: validatedData.status === 'published' ? new Date() : null
    }).returning();
    
    return NextResponse.json(newPost[0]);
  } catch (error) {
    console.error('Error creating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;
    
    // Validate input using Zod schema (partial for updates)
    const validatedData = insertBlogPostSchema.partial().parse(updateData);
    
    // Recalculate reading time if content changed
    if (validatedData.content) {
      const wordCount = validatedData.content.split(/\s+/).length;
      validatedData.readTime = Math.ceil(wordCount / 200);
    }
    
    // Handle publishing
    if (validatedData.status === 'published' && !validatedData.publishedAt) {
      validatedData.publishedAt = new Date();
    }
    
    const updatedPost = await db
      .update(blogPosts)
      .set({ ...validatedData, updatedAt: new Date() })
      .where(eq(blogPosts.id, id))
      .returning();

    if (updatedPost.length === 0) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedPost[0]);
  } catch (error) {
    console.error('Error updating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Blog post ID is required' },
        { status: 400 }
      );
    }
    
    const deletedPost = await db
      .delete(blogPosts)
      .where(eq(blogPosts.id, id))
      .returning({ id: blogPosts.id });

    if (deletedPost.length === 0) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}
