import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { AuthService } from '@/lib/auth';

export const GET = AuthService.requireAuth(async (request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const id = params.id;
    
    const query = 'SELECT * FROM playground_projects WHERE id = $1';
    const result = await DatabaseService.query(query, [id]);
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Playground project not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching playground project:', error);
    return NextResponse.json(
      { error: 'Failed to fetch playground project' },
      { status: 500 }
    );
  }
});

export const PUT = AuthService.requireAuth(async (request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const id = parseInt(params.id);
    const body = await request.json();
    
    const project = await DatabaseService.updatePlaygroundProject(id, body);
    
    if (!project) {
      return NextResponse.json(
        { error: 'Playground project not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(project);
  } catch (error) {
    console.error('Error updating playground project:', error);
    return NextResponse.json(
      { error: 'Failed to update playground project' },
      { status: 500 }
    );
  }
});

export const DELETE = AuthService.requireAuth(async (request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const id = parseInt(params.id);
    
    const query = 'DELETE FROM playground_projects WHERE id = $1 RETURNING id';
    const result = await DatabaseService.query(query, [id]);
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Playground project not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting playground project:', error);
    return NextResponse.json(
      { error: 'Failed to delete playground project' },
      { status: 500 }
    );
  }
});
