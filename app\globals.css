@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(210 25% 7.8431%);
  --card: hsl(180 6.6667% 97.0588%);
  --card-foreground: hsl(210 25% 7.8431%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(210 25% 7.8431%);
  --primary: hsl(203.8863 88.2845% 53.1373%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(259.4737 76.8116% 55.8824%);
  --secondary-foreground: hsl(0 0% 100%);
  --accent: hsl(153.8462 100% 50.9804%);
  --accent-foreground: hsl(0 0% 0%);
  --muted: hsl(240 1.9608% 90%);
  --muted-foreground: hsl(210 25% 7.8431%);
  --destructive: hsl(356.3033 90.5579% 54.3137%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(201.4286 30.4348% 90.9804%);
  --input: hsl(200 23.0769% 97.4510%);
  --ring: hsl(202.8169 89.1213% 53.1373%);
  --chart-1: hsl(203.8863 88.2845% 53.1373%);
  --chart-2: hsl(159.7826 100% 36.0784%);
  --chart-3: hsl(42.0290 92.8251% 56.2745%);
  --chart-4: hsl(147.1429 78.5047% 41.9608%);
  --chart-5: hsl(341.4894 75.2000% 50.9804%);
  --sidebar: hsl(180 6.6667% 97.0588%);
  --sidebar-foreground: hsl(210 25% 7.8431%);
  --sidebar-primary: hsl(203.8863 88.2845% 53.1373%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(211.5789 51.3514% 92.7451%);
  --sidebar-accent-foreground: hsl(203.8863 88.2845% 53.1373%);
  --sidebar-border: hsl(205.0000 25.0000% 90.5882%);
  --sidebar-ring: hsl(202.8169 89.1213% 53.1373%);
  --font-sans: 'Inter', sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --dark-100: hsl(0 0% 16.4706%);
  --dark-200: hsl(0 0% 10.1961%);
  --dark-300: hsl(0 0% 3.9216%);
  
  /* Advanced animations */
  --animation-bounce-in: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* Glass morphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-dark: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
}

.dark {
  --background: hsl(0 0% 3.9216%);
  --foreground: hsl(200 6.6667% 91.1765%);
  
  /* Dark mode glass */
  --glass-bg: rgba(0, 0, 0, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
  --card: hsl(0 0% 10.1961%);
  --card-foreground: hsl(200 6.6667% 91.1765%);
  --popover: hsl(0 0% 3.9216%);
  --popover-foreground: hsl(200 6.6667% 91.1765%);
  --primary: hsl(203.8863 88.2845% 53.1373%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(259.4737 76.8116% 55.8824%);
  --secondary-foreground: hsl(0 0% 100%);
  --accent: hsl(153.8462 100% 50.9804%);
  --accent-foreground: hsl(0 0% 0%);
  --muted: hsl(0 0% 16.4706%);
  --muted-foreground: hsl(200 6.6667% 91.1765%);
  --destructive: hsl(356.3033 90.5579% 54.3137%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 16.4706%);
  --input: hsl(0 0% 16.4706%);
  --ring: hsl(202.8169 89.1213% 53.1373%);
  --chart-1: hsl(203.8863 88.2845% 53.1373%);
  --chart-2: hsl(159.7826 100% 36.0784%);
  --chart-3: hsl(42.0290 92.8251% 56.2745%);
  --chart-4: hsl(147.1429 78.5047% 41.9608%);
  --chart-5: hsl(341.4894 75.2000% 50.9804%);
  --sidebar: hsl(0 0% 10.1961%);
  --sidebar-foreground: hsl(200 6.6667% 91.1765%);
  --sidebar-primary: hsl(203.8863 88.2845% 53.1373%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(0 0% 16.4706%);
  --sidebar-accent-foreground: hsl(200 6.6667% 91.1765%);
  --sidebar-border: hsl(0 0% 16.4706%);
  --sidebar-ring: hsl(202.8169 89.1213% 53.1373%);
}

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)), hsl(var(--accent)));
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

/* Animations */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes morphing {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate3d(0, 0, 0) rotateZ(0deg);
  }
  25% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    transform: translate3d(20px, -50px, 0) rotateZ(-5deg);
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    transform: translate3d(-20px, 30px, 0) rotateZ(8deg);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotateX(0deg); }
  50% { transform: translateY(-20px) rotateX(10deg); }
}

@keyframes float-complex {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotateZ(0deg);
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotateZ(5deg);
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotateZ(-3deg);
  }
  75% {
    transform: translateY(-25px) translateX(10px) rotateZ(2deg);
  }
}

@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(20px) scale(0.9); }
  100% { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes slideUp {
  0% { 
    transform: translateY(50px) rotateX(20deg); 
    opacity: 0; 
  }
  100% { 
    transform: translateY(0) rotateX(0deg); 
    opacity: 1; 
  }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: hsl(var(--accent)); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }
  50% {
    box-shadow: 0 0 40px hsl(var(--primary) / 0.6), 0 0 60px hsl(var(--accent) / 0.3);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Floating elements */
.floating-elements {
  animation: float 6s ease-in-out infinite;
}

.floating-complex {
  animation: float-complex 8s ease-in-out infinite;
}

/* Morphing blob */
.morphing-blob {
  background: linear-gradient(-45deg, 
    rgba(59, 130, 246, 0.4), 
    rgba(147, 51, 234, 0.4), 
    rgba(236, 72, 153, 0.4), 
    rgba(34, 197, 94, 0.4)
  );
  background-size: 400% 400%;
  animation: morphing 15s ease-in-out infinite, gradient-shift 8s ease infinite;
  filter: blur(40px);
}

/* Particle effects */
.particle {
  position: absolute;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
  pointer-events: none;
}

/* Skill bar effects */
.skill-bar {
  position: relative;
  overflow: hidden;
}

.skill-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

/* Enhanced animations */
@keyframes text-glow {
  0%, 100% { text-shadow: 0 0 10px rgba(59, 130, 246, 0.5); }
  50% { text-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(147, 51, 234, 0.6); }
}

@keyframes card-hover {
  0% { transform: translateY(0) rotateX(0) rotateY(0); }
  100% { transform: translateY(-10px) rotateX(5deg) rotateY(5deg); }
}

@keyframes ripple {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(4); opacity: 0; }
}

@keyframes bounce-in {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

/* Enhanced hover effects */
.card-3d {
  transition: all 0.3s ease;
  transform-style: preserve-3d;
}

.card-3d:hover {
  animation: card-hover 0.3s ease forwards;
}

/* Glowing text effect */
.glow-text {
  animation: text-glow 3s ease-in-out infinite;
}

/* Ripple effect */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:hover::before {
  width: 300px;
  height: 300px;
  animation: ripple 0.6s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--secondary)));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, hsl(var(--secondary)), hsl(var(--accent)));
}

/* Advanced Responsive Design Optimizations */

/* Mobile-first breakpoints */
@media (max-width: 480px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
  }
  
  .mobile-padding {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  .mobile-text-center {
    text-align: center !important;
  }
  
  .mobile-stack {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }
}

/* Tablet optimizations */
@media (min-width: 481px) and (max-width: 768px) {
  .tablet-hidden {
    display: none !important;
  }
  
  .tablet-cols-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  .desktop-cols-3 {
    grid-template-columns: repeat(3, 1fr) !important;
  }
  
  .desktop-cols-4 {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

/* Touch-friendly button sizing */
@media (pointer: coarse) {
  .touch-target {
    min-height: 44px !important;
    min-width: 44px !important;
  }
  
  .touch-button {
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
  }
}

/* High DPI optimizations */
@media (-webkit-min-device-pixel-ratio: 2),
       (min-resolution: 192dpi) {
  .high-dpi-border {
    border-width: 0.5px !important;
  }
  
  .high-dpi-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .floating-elements,
  .floating-complex,
  .morphing-blob {
    animation: none !important;
  }
}

/* Dark mode image optimizations */
@media (prefers-color-scheme: dark) {
  .dark-mode-image {
    filter: brightness(0.8) contrast(1.2);
  }
  
  .dark-mode-logo {
    filter: invert(1) brightness(0.8);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .glass-effect {
    background: white !important;
    border: 1px solid #ccc !important;
  }
}

/* Container queries for component-level responsiveness */
@container (max-width: 400px) {
  .container-responsive {
    padding: 0.5rem !important;
  }
  
  .container-responsive .grid {
    grid-template-columns: 1fr !important;
  }
}

/* Focus management for accessibility */
.focus-ring:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.focus-ring:focus:not(:focus-visible) {
  outline: none;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Improved text rendering */
.optimized-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

/* Safe area insets for mobile devices */
.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
}

/* Advanced grid layouts */
.auto-fit-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: 1.5rem;
}

.auto-fill-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

/* Fluid typography */
.fluid-text-sm {
  font-size: clamp(0.875rem, 0.8rem + 0.2vw, 1rem);
}

.fluid-text-base {
  font-size: clamp(1rem, 0.9rem + 0.3vw, 1.125rem);
}

.fluid-text-lg {
  font-size: clamp(1.125rem, 1rem + 0.4vw, 1.25rem);
}

.fluid-text-xl {
  font-size: clamp(1.25rem, 1.1rem + 0.5vw, 1.5rem);
}

.fluid-text-2xl {
  font-size: clamp(1.5rem, 1.3rem + 0.8vw, 2rem);
}

.fluid-text-3xl {
  font-size: clamp(1.875rem, 1.5rem + 1.2vw, 2.5rem);
}

.fluid-text-4xl {
  font-size: clamp(2.25rem, 1.8rem + 1.5vw, 3rem);
}

/* Responsive spacing */
.fluid-space-xs {
  gap: clamp(0.25rem, 0.2rem + 0.15vw, 0.5rem);
}

.fluid-space-sm {
  gap: clamp(0.5rem, 0.4rem + 0.3vw, 1rem);
}

.fluid-space-md {
  gap: clamp(1rem, 0.8rem + 0.6vw, 2rem);
}

.fluid-space-lg {
  gap: clamp(1.5rem, 1.2rem + 1vw, 3rem);
}

.fluid-space-xl {
  gap: clamp(2rem, 1.5rem + 1.5vw, 4rem);
}

/* Enhanced hover states for touch devices */
@media (hover: hover) {
  .hover-scale:hover {
    transform: scale(1.05);
  }
  
  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

/* Touch device optimizations */
@media (hover: none) {
  .touch-feedback:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}

/* Improved accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced focus indicators */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: 2px;
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-contents {
  will-change: contents;
}

/* Reset will-change after animation */
.animation-finished {
  will-change: auto;
}
