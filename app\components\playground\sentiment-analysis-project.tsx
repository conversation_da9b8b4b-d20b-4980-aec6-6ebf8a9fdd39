'use client'

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, BarChart3, Heart, Frown, Smile, <PERSON>h, TrendingUp, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface SentimentAnalysisProjectProps {
  onBack: () => void;
  projectData: any;
}

interface SentimentResult {
  overall: {
    sentiment: 'positive' | 'negative' | 'neutral';
    confidence: number;
    score: number; // -1 to 1
  };
  emotions: Array<{
    emotion: string;
    intensity: number;
    color: string;
    icon: any;
  }>;
  keywords: Array<{
    word: string;
    sentiment: 'positive' | 'negative' | 'neutral';
    weight: number;
  }>;
  insights: string[];
}

const sampleTexts = [
  "I absolutely love this new product! It's amazing and exceeded all my expectations. The customer service was fantastic too!",
  "This is the worst experience I've ever had. The product broke immediately and customer support was unhelpful.",
  "The product is okay, nothing special but it works as expected. Average quality for the price.",
  "I'm so excited about this opportunity! This could be life-changing and I can't wait to get started!",
  "I'm feeling quite disappointed with the recent changes. It's not what I was hoping for."
];

export function SentimentAnalysisProject({ onBack, projectData }: SentimentAnalysisProjectProps) {
  const [inputText, setInputText] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<SentimentResult | null>(null);
  const [selectedSample, setSelectedSample] = useState<number | null>(null);

  const analyzeSentiment = async () => {
    if (!inputText.trim()) return;
    
    setIsAnalyzing(true);
    
    try {
      // Import the API client
      const { PlaygroundAPI, handleApiError } = await import('@/lib/playground-api');
      
      // Make API call to analyze sentiment
      const response = await PlaygroundAPI.analyzeSentiment({
        text: inputText.trim()
      });
      
      // Convert API response to component format
      const convertedResult: SentimentResult = {
        overall: response.overall,
        emotions: response.emotions.map(emotion => ({
          ...emotion,
          icon: emotion.emotion === 'Joy' ? Smile :
                emotion.emotion === 'Sadness' ? Frown :
                emotion.emotion === 'Anger' ? Frown :
                emotion.emotion === 'Love' ? Heart : Meh
        })),
        keywords: response.keywords?.map((keyword: any) => ({
          word: keyword.word,
          sentiment: (keyword.sentiment === 'positive' || keyword.sentiment === 'negative' || keyword.sentiment === 'neutral') 
            ? keyword.sentiment 
            : 'neutral' as const,
          weight: keyword.weight
        })) || [],
        insights: response.insights
      };
      
      setResult(convertedResult);
      
    } catch (error) {
      console.error('Sentiment analysis error:', error);
      // Fallback to mock analysis if API fails
      const mockResult = generateMockSentiment(inputText);
      setResult(mockResult);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generateMockSentiment = (text: string): SentimentResult => {
    // Mock sentiment analysis based on keywords (fallback)
    const positiveWords = ['love', 'amazing', 'fantastic', 'excellent', 'great', 'wonderful', 'excited', 'happy'];
    const negativeWords = ['hate', 'terrible', 'awful', 'worst', 'disappointed', 'broken', 'unhelpful', 'bad'];
    
    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;
    
    const keywords: SentimentResult['keywords'] = [];
    
    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) {
        positiveCount++;
        keywords.push({ word, sentiment: 'positive', weight: Math.random() * 0.8 + 0.2 });
      } else if (negativeWords.some(nw => word.includes(nw))) {
        negativeCount++;
        keywords.push({ word, sentiment: 'negative', weight: Math.random() * 0.8 + 0.2 });
      }
    });
    
    const totalSentimentWords = positiveCount + negativeCount;
    const score = totalSentimentWords > 0 ? (positiveCount - negativeCount) / totalSentimentWords : 0;
    
    let sentiment: 'positive' | 'negative' | 'neutral';
    let confidence: number;
    
    if (score > 0.2) {
      sentiment = 'positive';
      confidence = Math.min(0.95, 0.6 + Math.abs(score) * 0.4);
    } else if (score < -0.2) {
      sentiment = 'negative';
      confidence = Math.min(0.95, 0.6 + Math.abs(score) * 0.4);
    } else {
      sentiment = 'neutral';
      confidence = 0.7 + Math.random() * 0.2;
    }
    
    return {
      overall: {
        sentiment,
        confidence,
        score
      },
      emotions: [
        { emotion: 'Joy', intensity: sentiment === 'positive' ? 85 : 20, color: 'bg-yellow-500', icon: Smile },
        { emotion: 'Sadness', intensity: sentiment === 'negative' ? 75 : 15, color: 'bg-blue-500', icon: Frown },
        { emotion: 'Anger', intensity: sentiment === 'negative' ? 60 : 10, color: 'bg-red-500', icon: Frown },
        { emotion: 'Love', intensity: sentiment === 'positive' ? 70 : 25, color: 'bg-pink-500', icon: Heart },
        { emotion: 'Neutral', intensity: sentiment === 'neutral' ? 80 : 30, color: 'bg-gray-500', icon: Meh }
      ],
      keywords,
      insights: [
        sentiment === 'positive' 
          ? 'The text expresses strong positive sentiment with enthusiastic language'
          : sentiment === 'negative'
          ? 'The text contains negative sentiment with critical language'
          : 'The text maintains a neutral tone with balanced language',
        `Confidence level is ${Math.round(confidence * 100)}% based on sentiment indicators`,
        `${keywords.length} sentiment-bearing keywords identified`,
        'Analysis considers context, intensity, and linguistic patterns'
      ]
    };
  };

  const loadSample = (index: number) => {
    setInputText(sampleTexts[index]);
    setSelectedSample(index);
    setResult(null);
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-500';
      case 'negative': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return Smile;
      case 'negative': return Frown;
      default: return Meh;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 dark:from-orange-900 dark:via-red-900 dark:to-pink-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Playground
          </Button>
          
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold gradient-text">AI Sentiment Analysis</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Analyze emotions and sentiment in text with advanced NLP
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Text Input */}
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  Text Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="Enter text to analyze sentiment... (reviews, social media posts, feedback, etc.)"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className="min-h-[150px]"
                />
                
                <Button
                  onClick={analyzeSentiment}
                  disabled={!inputText.trim() || isAnalyzing}
                  className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                >
                  {isAnalyzing ? (
                    <>
                      <TrendingUp className="w-4 h-4 mr-2 animate-pulse" />
                      Analyzing Sentiment...
                    </>
                  ) : (
                    <>
                      <BarChart3 className="w-4 h-4 mr-2" />
                      Analyze Sentiment
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Sample Texts */}
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle>Try Sample Texts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {sampleTexts.map((sample, index) => (
                    <button
                      key={index}
                      onClick={() => loadSample(index)}
                      className={`w-full p-3 text-left text-sm border rounded-lg transition-all hover:border-orange-300 ${
                        selectedSample === index 
                          ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' 
                          : 'border-gray-200 dark:border-gray-700'
                      }`}
                    >
                      {sample.substring(0, 80)}...
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Results Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-6"
          >
            {result ? (
              <>
                {/* Overall Sentiment */}
                <Card className="glass-effect">
                  <CardHeader>
                    <CardTitle>Overall Sentiment</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        {(() => {
                          const Icon = getSentimentIcon(result.overall.sentiment);
                          return <Icon className={`w-8 h-8 ${getSentimentColor(result.overall.sentiment)}`} />;
                        })()}
                        <div>
                          <div className={`text-2xl font-bold capitalize ${getSentimentColor(result.overall.sentiment)}`}>
                            {result.overall.sentiment}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {Math.round(result.overall.confidence * 100)}% confidence
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">
                          {result.overall.score > 0 ? '+' : ''}{result.overall.score.toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">Sentiment Score</div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Confidence Level</span>
                        <span>{Math.round(result.overall.confidence * 100)}%</span>
                      </div>
                      <Progress value={result.overall.confidence * 100} />
                    </div>
                  </CardContent>
                </Card>

                {/* Emotion Breakdown */}
                <Card className="glass-effect">
                  <CardHeader>
                    <CardTitle>Emotion Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {result.emotions.map((emotion, index) => {
                        const Icon = emotion.icon;
                        return (
                          <div key={index} className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-full ${emotion.color}`}>
                                <Icon className="w-4 h-4 text-white" />
                              </div>
                              <span className="font-medium">{emotion.emotion}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-24">
                                <Progress value={emotion.intensity} />
                              </div>
                              <span className="text-sm font-medium w-8">{emotion.intensity}%</span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>

                {/* Keywords */}
                {result.keywords.length > 0 && (
                  <Card className="glass-effect">
                    <CardHeader>
                      <CardTitle>Sentiment Keywords</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {result.keywords.map((keyword, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className={`${
                              keyword.sentiment === 'positive' 
                                ? 'border-green-500 text-green-700 bg-green-50 dark:bg-green-900/20' 
                                : 'border-red-500 text-red-700 bg-red-50 dark:bg-red-900/20'
                            }`}
                          >
                            {keyword.word}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Insights */}
                <Card className="glass-effect">
                  <CardHeader>
                    <CardTitle>AI Insights</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {result.insights.map((insight, index) => (
                        <li key={index} className="text-sm flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                          {insight}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card className="glass-effect h-96">
                <CardContent className="flex items-center justify-center h-full">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Enter text to see sentiment analysis results</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
