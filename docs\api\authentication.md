# Authentication Guide

Comprehensive guide to authentication and authorization mechanisms in the getintheq.space API. This document covers authentication flows, security considerations, token management, and implementation examples.

## Table of Contents

- [Overview](#overview)
- [Authentication Methods](#authentication-methods)
- [JWT Token Structure](#jwt-token-structure)
- [Authentication Flows](#authentication-flows)
- [Session Management](#session-management)
- [Security Considerations](#security-considerations)
- [Implementation Examples](#implementation-examples)
- [Troubleshooting](#troubleshooting)

## Overview

The authentication system is built on industry-standard practices with the following key features:

- **JWT-based Authentication**: Stateless token-based authentication
- **HTTP-Only Cookies**: Secure cookie storage for web applications
- **Role-Based Access Control**: Admin and user role differentiation
- **Rate Limiting**: Protection against brute force attacks
- **Password Security**: bcrypt hashing with salt rounds
- **Session Security**: Secure cookie settings and token expiration

### Security Features

```mermaid
graph TB
    A[User Request] --> B{Authentication Required?}
    B -->|No| C[Public Access]
    B -->|Yes| D[Token Validation]
    D --> E{Valid Token?}
    E -->|No| F[401 Unauthorized]
    E -->|Yes| G{Role Check}
    G -->|Insufficient| H[403 Forbidden]
    G -->|Authorized| I[Access Granted]
    
    J[Rate Limiter] --> D
    K[Password Policy] --> L[User Creation/Login]
    M[CSRF Protection] --> N[Cookie Settings]
```

## Authentication Methods

### 1. Bearer Token Authentication

**Usage**: API clients, mobile applications, server-to-server communication

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.signature
```

**Advantages:**
- Stateless and scalable
- Works across different domains
- Suitable for API integrations
- No CSRF vulnerabilities

**Disadvantages:**
- Vulnerable to XSS if stored in localStorage
- Requires manual token refresh logic
- Larger request headers

### 2. HTTP-Only Cookie Authentication

**Usage**: Web applications, same-origin requests

```http
Cookie: auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict
```

**Advantages:**
- Automatic CSRF protection with SameSite
- Not accessible via JavaScript (XSS protection)
- Automatic inclusion in requests
- Browser handles token lifecycle

**Disadvantages:**
- CSRF vulnerabilities without proper protection
- Limited cross-domain support
- Requires server-side session management for logout

## JWT Token Structure

### Token Composition

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin",
    "iat": 1723663400,
    "exp": 1724268200
  },
  "signature": "base64UrlEncodedSignature"
}
```

### Token Claims

| Claim | Type | Description | Example |
|-------|------|-------------|---------|
| `id` | string | User unique identifier | `550e8400-e29b-41d4-a716-446655440000` |
| `username` | string | User's username | `admin` |
| `email` | string | User's email address | `<EMAIL>` |
| `role` | string | User's role | `admin`, `user` |
| `iat` | number | Issued at timestamp | `1723663400` |
| `exp` | number | Expiration timestamp | `1724268200` |

### Token Lifecycle

- **Expiration**: 7 days (configurable via `JWT_EXPIRES_IN`)
- **Algorithm**: HMAC SHA256 (HS256)
- **Secret**: Configurable via `JWT_SECRET` environment variable
- **Refresh**: Manual re-authentication required

## Authentication Flows

### Initial Setup Flow

```mermaid
sequenceDiagram
    participant Admin
    participant API
    participant Database

    Admin->>API: GET /admin (first access)
    API->>Database: Check user count
    Database-->>API: users.length === 0
    API-->>Admin: Redirect to /admin/setup
    
    Admin->>API: POST /auth/setup
    API->>API: Validate input
    API->>API: Hash password
    API->>Database: Create first admin user
    API->>API: Generate JWT token
    API-->>Admin: JWT + Set-Cookie + User data
```

### Standard Login Flow

```mermaid
sequenceDiagram
    participant User
    participant API
    participant Database
    participant RateLimiter

    User->>API: POST /auth/login (credentials)
    API->>RateLimiter: Check rate limit
    RateLimiter-->>API: Rate limit status
    
    alt Rate limit exceeded
        API-->>User: 429 Too Many Requests
    else Rate limit OK
        API->>API: Validate input
        API->>Database: Query user by username/email
        Database-->>API: User record
        
        alt User not found
            API->>API: Simulate password check (timing attack prevention)
            API-->>User: 401 Invalid credentials
        else User found
            API->>API: Verify password with bcrypt
            
            alt Password invalid
                API->>RateLimiter: Record failed attempt
                API-->>User: 401 Invalid credentials
            else Password valid
                API->>RateLimiter: Clear failed attempts
                API->>API: Generate JWT token
                API-->>User: JWT + Set-Cookie + User data
            end
        end
    end
```

### Protected Resource Access

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Middleware

    Client->>API: Request with Authorization header
    API->>Middleware: Extract token
    
    alt No token provided
        API-->>Client: 401 Authentication required
    else Token provided
        Middleware->>Middleware: Verify JWT signature
        
        alt Invalid signature
            API-->>Client: 401 Invalid token
        else Valid signature
            Middleware->>Middleware: Check expiration
            
            alt Token expired
                API-->>Client: 401 Token expired
            else Token valid
                Middleware->>Middleware: Extract user data
                API->>API: Process request with user context
                API-->>Client: Protected resource
            end
        end
    end
```

### Logout Flow

```mermaid
sequenceDiagram
    participant User
    participant API
    participant Browser

    User->>API: POST /auth/logout
    API->>API: Validate token (optional)
    API->>Browser: Set-Cookie with Max-Age=0
    Browser->>Browser: Clear auth-token cookie
    API-->>User: Logout success response
    
    Note over API: Token remains valid until expiration
    Note over API: Server-side blacklisting not implemented
```

## Session Management

### Cookie Configuration

The authentication cookie is configured with maximum security:

```typescript
const cookieConfig = {
  name: 'auth-token',
  value: jwtToken,
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60, // 30 days or 1 day
  path: '/'
};
```

### Security Headers

All authentication responses include security headers:

```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Cache-Control: no-store, no-cache, must-revalidate, private
```

### Rate Limiting Configuration

Authentication endpoints have strict rate limiting:

```typescript
const authRateLimit = {
  maxRequests: 5,
  windowMs: 15 * 60 * 1000, // 15 minutes
  keyGenerator: (request) => `auth:${email}:${ipAddress}`
};
```

## Security Considerations

### Password Security

#### Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter  
- At least one number
- At least one special character
- No common weak patterns

#### Implementation
```typescript
const passwordValidation = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  forbiddenPatterns: [/123456/, /password/i, /qwerty/i]
};
```

### Token Security

#### Best Practices
- Use environment-specific secrets
- Implement short expiration times
- Use secure random secret generation
- Monitor for suspicious token usage

#### Secret Management
```bash
# Generate secure JWT secret
openssl rand -base64 32

# Environment configuration
JWT_SECRET="your-32-character-or-longer-secret"
```

### Input Validation

All authentication inputs are validated and sanitized:

```typescript
const loginSchema = z.object({
  email: z.string()
    .email('Invalid email address')
    .max(100, 'Email too long')
    .transform(email => email.toLowerCase().trim()),
  password: z.string()
    .min(6, 'Password too short')
    .max(128, 'Password too long'),
  rememberMe: z.boolean().optional().default(false)
});
```

### CSRF Protection

Cookie-based authentication includes CSRF protection:

- `SameSite=Strict` attribute
- HTTPS-only in production
- HttpOnly flag prevents JavaScript access

### Timing Attack Prevention

Authentication includes timing attack prevention:

```typescript
// Always perform password check to prevent timing attacks
const isValidPassword = user 
  ? await bcrypt.compare(password, user.password)
  : await bcrypt.compare(password, '$2b$12$dummy.hash.to.prevent.timing');
```

## Implementation Examples

### Frontend Authentication (React/Next.js)

#### Login Implementation
```typescript
// lib/auth-client.ts
import { useState } from 'react';

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);

  const login = async (credentials: LoginCredentials) => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      const data = await response.json();
      setUser(data.user);
      return data;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/login', {
        credentials: 'include',
      });
      const data = await response.json();
      
      if (data.authenticated) {
        setUser(data.user);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    }
  };

  return { user, login, logout, checkAuth, loading };
};
```

#### Protected Route Component
```typescript
// components/ProtectedRoute.tsx
import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth-client';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireRole?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireRole = 'user'
}) => {
  const { user, checkAuth } = useAuth();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth().finally(() => setLoading(false));
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <LoginForm />;
  }

  if (requireRole === 'admin' && user.role !== 'admin') {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return <>{children}</>;
};
```

### API Client Authentication

#### Node.js/Server Implementation
```typescript
// lib/api-client.ts
class APIClient {
  private baseURL: string;
  private token?: string;

  constructor(baseURL: string, token?: string) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async authenticate(email: string, password: string): Promise<void> {
    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      throw new Error('Authentication failed');
    }

    const data = await response.json();
    this.token = this.extractTokenFromResponse(response, data);
  }

  private extractTokenFromResponse(response: Response, data: any): string {
    // Extract from Set-Cookie header if using cookie auth
    const setCookie = response.headers.get('set-cookie');
    if (setCookie) {
      const tokenMatch = setCookie.match(/auth-token=([^;]+)/);
      if (tokenMatch) {
        return tokenMatch[1];
      }
    }
    
    // Or use token from response body if included
    return data.token;
  }

  async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (response.status === 401) {
      throw new Error('Authentication required');
    }

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Request failed');
    }

    return response.json();
  }
}

// Usage
const client = new APIClient('https://getintheq.space/api');
await client.authenticate('<EMAIL>', 'password');
const dashboard = await client.request('/admin/dashboard');
```

### Mobile App Authentication (React Native)

```typescript
// lib/auth-mobile.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

const TOKEN_KEY = 'auth_token';

export class MobileAuth {
  private static async storeToken(token: string): Promise<void> {
    await AsyncStorage.setItem(TOKEN_KEY, token);
  }

  private static async getToken(): Promise<string | null> {
    return AsyncStorage.getItem(TOKEN_KEY);
  }

  private static async removeToken(): Promise<void> {
    await AsyncStorage.removeItem(TOKEN_KEY);
  }

  static async login(email: string, password: string): Promise<any> {
    const response = await fetch('https://getintheq.space/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    const data = await response.json();
    
    // Extract token from response (assuming it's included in response body)
    if (data.token) {
      await this.storeToken(data.token);
    }

    return data;
  }

  static async logout(): Promise<void> {
    const token = await this.getToken();
    
    if (token) {
      try {
        await fetch('https://getintheq.space/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      } catch (error) {
        console.error('Logout request failed:', error);
      }
    }

    await this.removeToken();
  }

  static async authenticatedRequest(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<any> {
    const token = await this.getToken();
    
    if (!token) {
      throw new Error('No authentication token');
    }

    const response = await fetch(`https://getintheq.space/api${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    });

    if (response.status === 401) {
      await this.removeToken();
      throw new Error('Authentication expired');
    }

    if (!response.ok) {
      throw new Error('Request failed');
    }

    return response.json();
  }
}
```

## Troubleshooting

### Common Issues

#### 1. "Authentication required" Error

**Symptoms:**
- API returns 401 status
- User cannot access protected routes

**Possible Causes:**
- Missing or invalid JWT token
- Token has expired
- Invalid JWT secret configuration

**Solutions:**
```bash
# Check token expiration
jwt decode <your-token>

# Verify JWT secret
echo $JWT_SECRET | wc -c  # Should be 32+ characters

# Test authentication endpoint
curl -X POST https://getintheq.space/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"yourpassword"}'
```

#### 2. "Rate limit exceeded" Error

**Symptoms:**
- 429 status code
- Unable to authenticate after multiple attempts

**Solutions:**
```bash
# Wait for rate limit window to reset (15 minutes for auth endpoints)
# Or implement exponential backoff in client code

# Check current rate limit status
curl -I https://getintheq.space/api/auth/login
# Look for X-RateLimit-* headers
```

#### 3. Cookie Not Being Set

**Symptoms:**
- Login succeeds but cookie not stored
- Subsequent requests not authenticated

**Possible Causes:**
- Incorrect cookie settings
- HTTPS/Secure flag mismatch
- SameSite policy issues

**Solutions:**
```javascript
// Check cookie configuration in browser
document.cookie; // Should show auth-token

// Verify HTTPS in production
location.protocol; // Should be 'https:'

// Check SameSite compatibility
// Ensure requests are same-origin or configure CORS properly
```

#### 4. JWT Verification Failures

**Symptoms:**
- "Invalid token" errors
- Inconsistent authentication state

**Debugging:**
```typescript
// Server-side debugging
try {
  const decoded = jwt.verify(token, process.env.JWT_SECRET);
  console.log('Token valid:', decoded);
} catch (error) {
  console.error('Token verification failed:', error.message);
  // Common errors:
  // - "jwt malformed"
  // - "invalid signature"
  // - "jwt expired"
}
```

### Performance Optimization

#### Token Validation Caching

```typescript
// Implement token validation caching for high-traffic applications
const tokenCache = new Map();

const validateTokenCached = (token: string) => {
  if (tokenCache.has(token)) {
    const cached = tokenCache.get(token);
    if (Date.now() < cached.expires) {
      return cached.user;
    }
    tokenCache.delete(token);
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const expires = Date.now() + (5 * 60 * 1000); // 5 minute cache
    tokenCache.set(token, { user: decoded, expires });
    return decoded;
  } catch (error) {
    return null;
  }
};
```

#### Rate Limiting Optimization

```typescript
// Use Redis for distributed rate limiting in production
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

const checkRateLimit = async (key: string, maxRequests: number, windowMs: number) => {
  const current = await redis.incr(key);
  
  if (current === 1) {
    await redis.expire(key, Math.ceil(windowMs / 1000));
  }
  
  return {
    count: current,
    remaining: Math.max(0, maxRequests - current),
    limited: current > maxRequests
  };
};
```

---

**Last Updated**: 2024-08-14  
**Security Review**: 2024-08-14  
**Next Review**: 2024-11-14