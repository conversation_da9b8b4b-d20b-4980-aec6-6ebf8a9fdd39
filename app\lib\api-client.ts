/**
 * Main API client for backend services
 */

import { API_ENDPOINTS, API_BASE_URL } from './constants';

// Base URL for API calls
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // Client-side: use API_BASE_URL or environment variable
    return process.env.NEXT_PUBLIC_API_URL || API_BASE_URL;
  }
  // Server-side: use environment variable or API_BASE_URL
  return process.env.API_URL || API_BASE_URL;
};

// Types for API responses
export interface GitHubStats {
  public_repos: number;
  followers: number;
  following: number;
  total_stars: number;
  total_forks: number;
  contributions_last_year: number;
  most_used_languages: Array<{
    language: string;
    count: number;
    percentage: number;
  }>;
  popular_repositories: Array<{
    name: string;
    description: string;
    language: string;
    stars: number;
    forks: number;
    url: string;
  }>;
}

export interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  description: string;
  html_url: string;
  language: string;
  stargazers_count: number;
  forks_count: number;
  created_at: string;
  updated_at: string;
  topics: string[];
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  published_date: string;
  tags: string[];
  reading_time: number;
  featured_image?: string;
}

export interface ContactRequest {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactResponse {
  success: boolean;
  message: string;
  id?: string;
}

// Main API client class
export class APIClient {
  private static async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const baseUrl = getBaseUrl();
    const url = `${baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const response = await fetch(url, { ...defaultOptions, ...options });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.detail || 
        errorData.message || 
        `API request failed: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Get GitHub statistics
   */
  static async getGitHubStats(): Promise<GitHubStats> {
    return this.makeRequest<GitHubStats>(API_ENDPOINTS.GITHUB_STATS);
  }

  /**
   * Get GitHub repositories
   */
  static async getGitHubRepos(): Promise<GitHubRepo[]> {
    return this.makeRequest<GitHubRepo[]>(API_ENDPOINTS.GITHUB_REPOS);
  }

  /**
   * Get blog posts
   */
  static async getBlogPosts(): Promise<BlogPost[]> {
    return this.makeRequest<BlogPost[]>(API_ENDPOINTS.BLOG);
  }

  /**
   * Submit contact form
   */
  static async submitContact(request: ContactRequest): Promise<ContactResponse> {
    return this.makeRequest<ContactResponse>(API_ENDPOINTS.CONTACT, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Health check
   */
  static async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.makeRequest<{ status: string; timestamp: string }>('/');
  }
}

// Error handling
export class APIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export const handleApiError = (error: unknown): APIError => {
  if (error instanceof APIError) {
    return error;
  }

  if (error instanceof Error) {
    // Check if it's a network error
    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      return new APIError(
        'Network error. Please check your connection and try again.',
        0,
        'NETWORK_ERROR'
      );
    }

    // Check if it's a timeout error
    if (error.message.includes('timeout')) {
      return new APIError(
        'Request timed out. Please try again.',
        408,
        'TIMEOUT_ERROR'
      );
    }

    return new APIError(error.message, 500, 'API_ERROR');
  }

  return new APIError('An unexpected error occurred', 500, 'UNKNOWN_ERROR');
};

// Environment detection
export const isProduction = () => process.env.NODE_ENV === 'production';
export const isDevelopment = () => process.env.NODE_ENV === 'development';

// Configuration for different environments
export const getApiConfig = () => ({
  baseUrl: getBaseUrl(),
  timeout: isDevelopment() ? 30000 : 10000, // 30s in dev, 10s in prod
  retries: isDevelopment() ? 1 : 3,
  retryDelay: 1000,
});

export default APIClient;