# Agent Guidelines for getintheq.space

## Build/Test/Lint Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint with auto-fix
- `npm run lint:check` - Run ESLint without auto-fix
- `npm run type-check` or `npm run check` - TypeScript type check
- `npm run test` - Run Jest tests
- `npm run test:watch` - Run Jest in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm test -- --testNamePattern="test name"` - Run single test by name
- `npm test -- path/to/test.test.ts` - Run single test file
- `npm run validate` - Run type-check, lint:check, and format:check together
- `npm run db:push` - Push database schema changes to Neon PostgreSQL

## Code Style Guidelines

- **Language**: TypeScript with strict mode, ESNext modules, no emitted JS
- **Framework**: Next.js 14 with App Router architecture
- **Imports**: Use path aliases `@/*` (app/), `@/components/*`, `@/lib/*`, `@/hooks/*`, `@shared/*` (shared/)
- **Import order**: React first, Next.js, externals, internals with `@/`, then `@shared/` - enforced by ESLint
- **Components**: Use React.forwardRef for UI components, export component + variants with CVA
- **Styling**: Tailwind CSS with CSS variables, use `cn()` from `@/lib/utils` for className merging
- **Types**: Export types alongside schemas, use Zod + drizzle-zod for validation, infer types from schemas
- **Database**: Drizzle ORM with PostgreSQL, all schemas in `shared/schema.ts`, use createInsertSchema
- **State**: @tanstack/react-query for server state, React hooks for local state
- **File naming**: kebab-case for files, PascalCase for React components, preserve existing conventions
- **Error handling**: Try/catch in API routes, return NextResponse.json with appropriate HTTP status codes
- **Async/await**: Prefer over .then(), handle all promise rejections, log errors before returning responses

## Architecture & Patterns

- App Router: `app/` contains pages/layouts/components/API routes, exclude server/workers/serverless
- Shared schemas: `shared/` for database types, exclude from server builds
- UI components: Follow shadcn/ui patterns with forwardRef, variants, displayName
- API validation: Validate inputs, handle client headers (x-forwarded-for, user-agent), graceful email failures
