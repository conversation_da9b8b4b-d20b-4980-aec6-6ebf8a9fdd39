# Portfolio API Deployment Guide

This guide walks you through deploying the portfolio API to `api.getintheq.space` using Cloudflare Workers and D1 database.

## Prerequisites

1. **Cloudflare Account**: You need a Cloudflare account with your domain (`getintheq.space`) added
2. **API Token**: Create a Cloudflare API token with the following permissions:
   - Zone: Zone Read, Zone Edit
   - Account: Cloudflare Workers Edit
   - Account: D1 Edit

## Step 1: Authentication

1. Get your API token from [Cloudflare Dashboard](https://developers.cloudflare.com/fundamentals/api/get-started/create-token/)
2. Set the environment variable:
   ```bash
   export CLOUDFLARE_API_TOKEN=your_token_here
   ```

## Step 2: Install Dependencies

```bash
cd workers
npm install
```

## Step 3: Create D1 Databases

Run the following commands to create databases for each environment:

```bash
# Development database
npx wrangler d1 create portfolio-db

# Staging database  
npx wrangler d1 create portfolio-db-staging

# Production database
npx wrangler d1 create portfolio-db-prod
```

Each command will output a database ID. Copy these IDs.

## Step 4: Update Configuration

Edit `wrangler.toml` and replace the placeholder database IDs:
- Replace `YOUR_DATABASE_ID_HERE` with your development database ID
- Replace `YOUR_STAGING_DATABASE_ID_HERE` with your staging database ID  
- Replace `YOUR_PROD_DATABASE_ID_HERE` with your production database ID

## Step 5: Run Database Migrations

Apply the database schema to all environments:

```bash
# Local development
npm run db:migrate:local

# Staging
npm run db:migrate:staging

# Production
npm run db:migrate:production
```

## Step 6: Set Environment Variables

Set the required secrets for production:

```bash
npx wrangler secret put GITHUB_TOKEN --env production
npx wrangler secret put GITHUB_USERNAME --env production  
npx wrangler secret put RESEND_API_KEY --env production
npx wrangler secret put CONTACT_EMAIL --env production
```

## Step 7: Deploy

Deploy to production:

```bash
npm run deploy:production
```

## Step 8: Configure DNS (if not done)

If you haven't set up the custom domain yet:

1. Go to Cloudflare Dashboard
2. Navigate to Workers & Pages
3. Select your `portfolio-api-production` worker
4. Go to Settings > Triggers
5. Add custom domain: `api.getintheq.space`

## Automated Deployment

For convenience, you can use the deployment script:

```bash
./deploy.sh
```

This script will:
- Check prerequisites
- Create D1 databases
- Guide you through setting up secrets
- Deploy to production

## API Endpoints

Once deployed, your API will be available at:

- Health check: `https://api.getintheq.space/`
- GitHub stats: `https://api.getintheq.space/api/github/stats`
- Blog posts: `https://api.getintheq.space/api/blog`
- Contact form: `https://api.getintheq.space/api/contact`
- Playground: `https://api.getintheq.space/api/playground`

## Development

For local development:

```bash
npm run dev
```

This will start the worker locally with D1 database emulation.

## Troubleshooting

### Authentication Issues
- Ensure your `CLOUDFLARE_API_TOKEN` is set correctly
- Check that your API token has the required permissions

### Database Issues
- Verify database IDs in `wrangler.toml` are correct
- Ensure migrations have been applied to all environments

### Domain Issues
- Check that your domain is properly configured in Cloudflare
- Verify DNS settings for the subdomain

## Monitoring

After deployment, monitor your API using:
- Cloudflare Workers dashboard for performance metrics
- Real User Monitoring (RUM) for user experience
- Error tracking through Workers logs