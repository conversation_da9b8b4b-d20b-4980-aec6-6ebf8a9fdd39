@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(210 25% 7.8431%);
  --card: hsl(180 6.6667% 97.0588%);
  --card-foreground: hsl(210 25% 7.8431%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(210 25% 7.8431%);
  --primary: hsl(203.8863 88.2845% 53.1373%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(259.4737 76.8116% 55.8824%);
  --secondary-foreground: hsl(0 0% 100%);
  --accent: hsl(153.8462 100% 50.9804%);
  --accent-foreground: hsl(0 0% 0%);
  --muted: hsl(240 1.9608% 90%);
  --muted-foreground: hsl(210 25% 7.8431%);
  --destructive: hsl(356.3033 90.5579% 54.3137%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(201.4286 30.4348% 90.9804%);
  --input: hsl(200 23.0769% 97.4510%);
  --ring: hsl(202.8169 89.1213% 53.1373%);
  --chart-1: hsl(203.8863 88.2845% 53.1373%);
  --chart-2: hsl(159.7826 100% 36.0784%);
  --chart-3: hsl(42.0290 92.8251% 56.2745%);
  --chart-4: hsl(147.1429 78.5047% 41.9608%);
  --chart-5: hsl(341.4894 75.2000% 50.9804%);
  --sidebar: hsl(180 6.6667% 97.0588%);
  --sidebar-foreground: hsl(210 25% 7.8431%);
  --sidebar-primary: hsl(203.8863 88.2845% 53.1373%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(211.5789 51.3514% 92.7451%);
  --sidebar-accent-foreground: hsl(203.8863 88.2845% 53.1373%);
  --sidebar-border: hsl(205.0000 25.0000% 90.5882%);
  --sidebar-ring: hsl(202.8169 89.1213% 53.1373%);
  --font-sans: 'Inter', sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --dark-100: hsl(0 0% 16.4706%);
  --dark-200: hsl(0 0% 10.1961%);
  --dark-300: hsl(0 0% 3.9216%);
}

.dark {
  --background: hsl(0 0% 3.9216%);
  --foreground: hsl(200 6.6667% 91.1765%);
  --card: hsl(228 9.8039% 10%);
  --card-foreground: hsl(0 0% 85.0980%);
  --popover: hsl(0 0% 3.9216%);
  --popover-foreground: hsl(200 6.6667% 91.1765%);
  --primary: hsl(203.7736 87.6033% 52.5490%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(259.4737 76.8116% 55.8824%);
  --secondary-foreground: hsl(0 0% 100%);
  --accent: hsl(153.8462 100% 50.9804%);
  --accent-foreground: hsl(0 0% 0%);
  --muted: hsl(0 0% 9.4118%);
  --muted-foreground: hsl(210 3.3898% 46.2745%);
  --destructive: hsl(356.3033 90.5579% 54.3137%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(210 5.2632% 14.9020%);
  --input: hsl(207.6923 27.6596% 18.4314%);
  --ring: hsl(202.8169 89.1213% 53.1373%);
  --chart-1: hsl(203.8863 88.2845% 53.1373%);
  --chart-2: hsl(159.7826 100% 36.0784%);
  --chart-3: hsl(42.0290 92.8251% 56.2745%);
  --chart-4: hsl(147.1429 78.5047% 41.9608%);
  --chart-5: hsl(341.4894 75.2000% 50.9804%);
  --sidebar: hsl(228 9.8039% 10%);
  --sidebar-foreground: hsl(0 0% 85.0980%);
  --sidebar-primary: hsl(202.8169 89.1213% 53.1373%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(205.7143 70% 7.8431%);
  --sidebar-accent-foreground: hsl(203.7736 87.6033% 52.5490%);
  --sidebar-border: hsl(205.7143 15.7895% 26.0784%);
  --sidebar-ring: hsl(202.8169 89.1213% 53.1373%);
  --dark-100: hsl(0 0% 16.4706%);
  --dark-200: hsl(0 0% 10.1961%);
  --dark-300: hsl(0 0% 3.9216%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground transition-colors duration-300;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)), hsl(var(--accent)));
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient 8s ease infinite;
    filter: drop-shadow(0 2px 10px rgba(0, 0, 0, 0.1));
  }

  .morphing-blob {
    background: linear-gradient(45deg, hsl(var(--primary) / 0.3), hsl(var(--secondary) / 0.3), hsl(var(--accent) / 0.3));
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: morph 15s ease-in-out infinite;
    filter: blur(40px);
  }

  .floating-elements {
    animation: float-complex 20s ease-in-out infinite;
  }

  .glass-effect {
    @apply bg-white/10 dark:bg-black/30 backdrop-blur-lg border border-white/20 dark:border-white/10;
  }

  .typing-animation {
    overflow: hidden;
    border-right: 3px solid hsl(var(--accent));
    white-space: nowrap;
    margin: 0 auto;
    animation: typing 3.5s steps(30, end), blink-caret 0.75s step-end infinite;
  }

  .particle {
    @apply absolute rounded-full pointer-events-none;
    background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--secondary)));
    animation: float 6s ease-in-out infinite;
  }

  .skill-bar {
    @apply transition-all duration-700 ease-in-out;
  }

  .project-card {
    @apply transition-all duration-300 ease-in-out;
  }

  .project-card:hover {
    @apply -translate-y-2 scale-105;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-gradient {
    animation: gradient 3s ease infinite;
    background-size: 300% 300%;
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-morphing {
    animation: morphing 20s ease-in-out infinite;
  }

  .bg-300\% {
    background-size: 300% 300%;
  }
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}

@keyframes morph {
  0%, 100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: translate3d(0, 0, 0) rotateZ(0deg);
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
    transform: translate3d(-50px, -20px, 0) rotateZ(10deg);
  }
  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    transform: translate3d(20px, -50px, 0) rotateZ(-5deg);
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    transform: translate3d(-20px, 30px, 0) rotateZ(8deg);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotateX(0deg); }
  50% { transform: translateY(-20px) rotateX(10deg); }
}

@keyframes float-complex {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotateZ(0deg);
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotateZ(5deg);
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotateZ(-3deg);
  }
  75% {
    transform: translateY(-25px) translateX(10px) rotateZ(2deg);
  }
}

@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(20px) scale(0.9); }
  100% { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes slideUp {
  0% { 
    transform: translateY(50px) rotateX(20deg); 
    opacity: 0; 
  }
  100% { 
    transform: translateY(0) rotateX(0deg); 
    opacity: 1; 
  }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: hsl(var(--accent)); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }
  50% {
    box-shadow: 0 0 40px hsl(var(--primary) / 0.6), 0 0 60px hsl(var(--accent) / 0.3);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes morphing {
  0%, 100% { 
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: rotate(0deg) scale(1);
  }
  25% { 
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    transform: rotate(90deg) scale(1.1);
  }
  50% { 
    border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%;
    transform: rotate(180deg) scale(0.9);
  }
  75% { 
    border-radius: 60% 40% 60% 30% / 70% 30% 50% 60%;
    transform: rotate(270deg) scale(1.05);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--primary) / 0.8);
}
