import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { DatabaseService } from '@/lib/database-service';

export async function POST(request: NextRequest) {
  try {
    // Check if admin user already exists
    const existingAdminQuery = 'SELECT COUNT(*) as count FROM users WHERE role = $1';
    const result = await DatabaseService.query(existingAdminQuery, ['admin']);
    const adminCount = parseInt(result.rows[0].count);

    if (adminCount > 0) {
      return NextResponse.json(
        { error: 'Admin user already exists' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { email, password, name } = body;

    // Validate input
    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'Email, password, and name are required' },
        { status: 400 }
      );
    }

    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Create admin user
    const user = await AuthService.createUser({
      email,
      password,
      name,
      role: 'admin'
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Failed to create admin user' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Admin user created successfully',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Setup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check if setup is needed
    const existingAdminQuery = 'SELECT COUNT(*) as count FROM users WHERE role = $1';
    const result = await DatabaseService.query(existingAdminQuery, ['admin']);
    const adminCount = parseInt(result.rows[0].count);

    return NextResponse.json({
      setupRequired: adminCount === 0
    });
  } catch (error) {
    console.error('Setup check error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
