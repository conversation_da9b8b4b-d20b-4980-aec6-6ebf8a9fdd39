'use client'

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Brain, 
  FileText, 
  MessageSquare, 
  Eye, 
  Users, 
  TrendingUp,
  Calendar,
  Plus,
  ExternalLink,
  Activity,
  Clock,
  BarChart3,
  Zap,
  Globe,
  Shield,
  Database,
  Server,
  ChevronUp,
  ChevronDown,
  Star,
  GitBranch,
  Cpu,
  HardDrive
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Link from 'next/link';

interface DashboardStats {
  projects: { total: number; active: number; featured: number };
  blog: { total: number; published: number; drafts: number };
  playground: { total: number; active: number; usage: number };
  contact: { total: number; unread: number };
  analytics: { views: number; visitors: number; growth: number };
  performance: { score: number; loadTime: number; uptime: number };
  system: { cpu: number; memory: number; storage: number };
}

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  trend: 'up' | 'down' | 'neutral';
  icon: any;
  color: string;
}

interface RecentActivity {
  id: string;
  type: 'project' | 'blog' | 'contact' | 'playground' | 'system';
  title: string;
  description: string;
  timestamp: Date;
  status?: string;
  priority?: 'low' | 'medium' | 'high';
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    projects: { total: 12, active: 10, featured: 3 },
    blog: { total: 8, published: 6, drafts: 2 },
    playground: { total: 5, active: 5, usage: 1247 },
    contact: { total: 23, unread: 3 },
    analytics: { views: 15420, visitors: 8934, growth: 12.5 },
    performance: { score: 94, loadTime: 1.2, uptime: 99.8 },
    system: { cpu: 23, memory: 45, storage: 67 }
  });

  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('7d');

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([
    {
      id: '1',
      type: 'contact',
      title: 'New Contact Message',
      description: 'High-priority inquiry from potential client about AI consulting services',
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      status: 'unread',
      priority: 'high'
    },
    {
      id: '2',
      type: 'blog',
      title: 'Blog Post Published',
      description: 'AI in Modern Web Development - Advanced Techniques for 2024',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
      status: 'published',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'playground',
      title: 'AI Tool High Usage',
      description: 'Text Generator reached 500 uses today - consider upgrading infrastructure',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
      status: 'active',
      priority: 'medium'
    },
    {
      id: '4',
      type: 'project',
      title: 'Project Performance Alert',
      description: 'AI Analytics Platform showing excellent user engagement metrics',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),
      status: 'active',
      priority: 'low'
    },
    {
      id: '5',
      type: 'system',
      title: 'Security Scan Complete',
      description: 'Weekly security audit completed - all systems secure',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12),
      status: 'completed',
      priority: 'low'
    }
  ]);

  const metricCards: MetricCard[] = [
    {
      title: 'Total Views',
      value: stats.analytics.views.toLocaleString(),
      change: stats.analytics.growth,
      trend: 'up',
      icon: Eye,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Active Projects',
      value: stats.projects.active,
      change: 8.2,
      trend: 'up',
      icon: FolderOpen,
      color: 'from-green-500 to-emerald-500'
    },
    {
      title: 'AI Tools Usage',
      value: stats.playground.usage,
      change: 15.7,
      trend: 'up',
      icon: Brain,
      color: 'from-purple-500 to-pink-500'
    },
    {
      title: 'Performance Score',
      value: `${stats.performance.score}%`,
      change: 2.1,
      trend: 'up',
      icon: Zap,
      color: 'from-orange-500 to-red-500'
    }
  ];

  const systemMetrics = [
    { name: 'CPU Usage', value: stats.system.cpu, max: 100, color: 'bg-blue-500' },
    { name: 'Memory', value: stats.system.memory, max: 100, color: 'bg-green-500' },
    { name: 'Storage', value: stats.system.storage, max: 100, color: 'bg-purple-500' },
    { name: 'Uptime', value: stats.performance.uptime, max: 100, color: 'bg-orange-500' }
  ];

  const quickActions = [
    { title: 'New AI Project', href: '/admin/playground/new', icon: Brain, color: 'bg-purple-500' },
    { title: 'Create Blog Post', href: '/admin/blog/new', icon: FileText, color: 'bg-green-500' },
    { title: 'Add Project', href: '/admin/projects/new', icon: FolderOpen, color: 'bg-blue-500' },
    { title: 'View Analytics', href: '/admin/analytics', icon: BarChart3, color: 'bg-orange-500' },
    { title: 'System Health', href: '/admin/settings', icon: Server, color: 'bg-red-500' },
    { title: 'Security Audit', href: '/admin/security', icon: Shield, color: 'bg-gray-500' }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'project': return FolderOpen;
      case 'blog': return FileText;
      case 'contact': return MessageSquare;
      case 'playground': return Brain;
      case 'system': return Server;
      default: return Activity;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'project': return 'text-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'blog': return 'text-green-500 bg-green-50 dark:bg-green-900/20';
      case 'contact': return 'text-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'playground': return 'text-purple-500 bg-purple-50 dark:bg-purple-900/20';
      case 'system': return 'text-gray-500 bg-gray-50 dark:bg-gray-900/20';
      default: return 'text-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  return (
    <div className="space-y-8">
      {/* Enhanced Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 rounded-3xl p-8 text-white"
      >
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold mb-2 flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-white/20 rounded-2xl backdrop-blur-sm"
                >
                  <Activity className="w-8 h-8" />
                </motion.div>
                Welcome back, Khiwniti! 🚀
              </h1>
              <p className="text-purple-100 text-lg">
                Your AI-powered portfolio is performing exceptionally well today.
              </p>
            </div>
            
            <div className="text-right space-y-2">
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold">{stats.analytics.views.toLocaleString()}</div>
                  <div className="text-sm text-purple-100">Total Views</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{stats.analytics.visitors.toLocaleString()}</div>
                  <div className="text-sm text-purple-100">Visitors</div>
                </div>
              </div>
              <div className="flex items-center justify-center gap-2 bg-white/20 rounded-lg px-3 py-1 backdrop-blur-sm">
                <TrendingUp className="w-4 h-4" />
                <span className="text-sm font-medium">+{stats.analytics.growth}% growth</span>
              </div>
            </div>
          </div>

          {/* Performance Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-purple-100">Performance Score</span>
                <Zap className="w-4 h-4 text-yellow-300" />
              </div>
              <div className="text-2xl font-bold">{stats.performance.score}%</div>
              <Progress value={stats.performance.score} className="mt-2" />
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-purple-100">Load Time</span>
                <Clock className="w-4 h-4 text-green-300" />
              </div>
              <div className="text-2xl font-bold">{stats.performance.loadTime}s</div>
              <div className="text-xs text-purple-100">Excellent performance</div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-purple-100">Uptime</span>
                <Globe className="w-4 h-4 text-blue-300" />
              </div>
              <div className="text-2xl font-bold">{stats.performance.uptime}%</div>
              <div className="text-xs text-purple-100">Rock solid reliability</div>
            </div>
          </div>
        </div>
        
        {/* Floating Elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <motion.div
            animate={{ rotate: 360, scale: [1, 1.1, 1] }}
            transition={{ duration: 8, repeat: Infinity }}
            className="w-24 h-24 border border-white/30 rounded-full"
          />
        </div>
        <div className="absolute bottom-4 left-4 opacity-20">
          <motion.div
            animate={{ rotate: -360, scale: [1, 0.9, 1] }}
            transition={{ duration: 12, repeat: Infinity }}
            className="w-16 h-16 border border-white/30 rounded-full"
          />
        </div>
      </motion.div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metricCards.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="group"
            >
              <Card className="hover:shadow-xl transition-all duration-300 border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {metric.title}
                  </CardTitle>
                  <motion.div 
                    className={`p-3 rounded-xl bg-gradient-to-r ${metric.color}`}
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                  >
                    <Icon className="w-5 h-5 text-white" />
                  </motion.div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-3xl font-bold text-gray-900 dark:text-white">
                      {metric.value}
                    </div>
                    <div className={`flex items-center text-sm font-medium ${
                      metric.trend === 'up' ? 'text-green-600' : 
                      metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {metric.trend === 'up' ? <ChevronUp className="w-4 h-4" /> : 
                       metric.trend === 'down' ? <ChevronDown className="w-4 h-4" /> : null}
                      {metric.change > 0 ? '+' : ''}{metric.change}%
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                    vs. last {timeRange === '7d' ? 'week' : timeRange === '30d' ? 'month' : 'day'}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
          </TabsList>
          
          <div className="flex items-center gap-2">
            <Button
              variant={timeRange === '1d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange('1d')}
            >
              1D
            </Button>
            <Button
              variant={timeRange === '7d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange('7d')}
            >
              7D
            </Button>
            <Button
              variant={timeRange === '30d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange('30d')}
            >
              30D
            </Button>
          </div>
        </div>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-1"
            >
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Plus className="w-5 h-5" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <motion.div
                        key={action.title}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <Button
                          variant="outline"
                          className="w-full justify-start hover:shadow-md transition-all"
                          asChild
                        >
                          <Link href={action.href}>
                            <div className={`p-1.5 rounded-lg ${action.color} mr-3`}>
                              <Icon className="w-4 h-4 text-white" />
                            </div>
                            {action.title}
                          </Link>
                        </Button>
                      </motion.div>
                    );
                  })}
                </CardContent>
              </Card>
            </motion.div>

            {/* Recent Activity */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="lg:col-span-2"
            >
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <AnimatePresence>
                      {recentActivity.map((activity, index) => {
                        const Icon = getActivityIcon(activity.type);
                        return (
                          <motion.div
                            key={activity.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ delay: index * 0.05 }}
                            className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          >
                            <div className={`p-2 rounded-lg ${getActivityColor(activity.type)}`}>
                              <Icon className="w-4 h-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                  {activity.title}
                                </p>
                                <span className="text-xs text-gray-500">
                                  {formatTimeAgo(activity.timestamp)}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                {activity.description}
                              </p>
                              <div className="flex items-center gap-2">
                                {activity.status && (
                                  <Badge variant="secondary" className="text-xs">
                                    {activity.status}
                                  </Badge>
                                )}
                                {activity.priority && (
                                  <Badge className={`text-xs ${getPriorityColor(activity.priority)}`}>
                                    {activity.priority}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        );
                      })}
                    </AnimatePresence>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* System Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="w-5 h-5" />
                  System Health
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {systemMetrics.map((metric, index) => (
                  <motion.div
                    key={metric.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{metric.name}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {metric.value}%
                      </span>
                    </div>
                    <Progress value={metric.value} className="h-2" />
                  </motion.div>
                ))}
              </CardContent>
            </Card>

            {/* Resource Usage */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Resource Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Cpu className="w-5 h-5 text-blue-500" />
                      <span className="font-medium">CPU</span>
                    </div>
                    <span className="text-lg font-bold">{stats.system.cpu}%</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <HardDrive className="w-5 h-5 text-green-500" />
                      <span className="font-medium">Memory</span>
                    </div>
                    <span className="text-lg font-bold">{stats.system.memory}%</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Database className="w-5 h-5 text-purple-500" />
                      <span className="font-medium">Storage</span>
                    </div>
                    <span className="text-lg font-bold">{stats.system.storage}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}