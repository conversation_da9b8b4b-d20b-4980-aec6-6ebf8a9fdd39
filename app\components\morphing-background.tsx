'use client'

'use client'

import { motion, useAnimation } from "framer-motion";
import { useEffect, useState } from "react";

export function MorphingBackground() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const controls = useAnimation();

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight,
      });
    };

    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);

    // Start the continuous animation
    controls.start({
      rotate: 360,
      transition: {
        duration: 20,
        repeat: Infinity,
        ease: "linear"
      }
    });

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [controls]);

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {/* Primary morphing blob with enhanced effects */}
      <motion.div
        className="morphing-blob absolute w-96 h-96 opacity-30"
        style={{
          left: '20%',
          top: '30%',
        }}
        animate={{
          x: mousePosition.x * 50 + Math.sin(scrollY * 0.01) * 20,
          y: mousePosition.y * 50 + Math.cos(scrollY * 0.01) * 15,
          scale: [1, 1.1, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          type: "spring",
          damping: 50,
          stiffness: 50,
          scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
          rotate: { duration: 15, repeat: Infinity, ease: "linear" }
        }}
      />

      {/* Secondary morphing blob with parallax */}
      <motion.div
        className="morphing-blob absolute w-80 h-80 opacity-20"
        style={{
          right: '15%',
          bottom: '25%',
          animationDelay: '5s',
        }}
        animate={{
          x: -mousePosition.x * 30 + scrollY * 0.1,
          y: -mousePosition.y * 30 - scrollY * 0.05,
          scale: [1, 0.8, 1.2, 1],
        }}
        transition={{
          type: "spring",
          damping: 30,
          stiffness: 30,
          scale: { duration: 12, repeat: Infinity, ease: "easeInOut" }
        }}
      />

      {/* Tertiary morphing blob with complex motion */}
      <motion.div
        className="morphing-blob absolute w-64 h-64 opacity-25"
        style={{
          left: '60%',
          top: '10%',
          animationDelay: '10s',
        }}
        animate={{
          x: mousePosition.x * 20 + Math.cos(scrollY * 0.02) * 30,
          y: mousePosition.y * 40 + Math.sin(scrollY * 0.015) * 25,
          rotate: [0, -90, 90, 0],
        }}
        transition={{
          type: "spring",
          damping: 40,
          stiffness: 40,
          rotate: { duration: 10, repeat: Infinity, ease: "easeInOut" }
        }}
      />

      {/* Floating geometric elements */}
      {Array.from({ length: 12 }).map((_, i) => (
        <motion.div
          key={i}
          className="floating-elements absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${i * 2}s`,
          }}
          animate={{
            x: Math.sin(Date.now() * 0.001 + i) * 50,
            y: Math.cos(Date.now() * 0.001 + i) * 30,
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
        >
          <div
            className={`
              ${i % 4 === 0 ? 'w-2 h-2 rounded-full bg-primary/20' : ''}
              ${i % 4 === 1 ? 'w-3 h-3 rotate-45 bg-secondary/20' : ''}
              ${i % 4 === 2 ? 'w-1 h-1 rounded-full bg-accent/30' : ''}
              ${i % 4 === 3 ? 'w-2 h-2 rotate-12 bg-gradient-to-br from-primary/20 to-accent/20' : ''}
            `}
          />
        </motion.div>
      ))}

      {/* Sparkle effects */}
      {Array.from({ length: 8 }).map((_, i) => (
        <motion.div
          key={`sparkle-${i}`}
          className="absolute w-1 h-1 bg-accent rounded-full"
          style={{
            left: `${20 + Math.random() * 60}%`,
            top: `${20 + Math.random() * 60}%`,
            animation: `sparkle ${3 + Math.random() * 4}s infinite`,
            animationDelay: `${Math.random() * 2}s`,
          }}
        />
      ))}
    </div>
  );
}