# CI/CD Pipeline Guide

Comprehensive guide for setting up continuous integration and deployment pipelines for the getintheq.space platform, covering automated testing, security scanning, deployment strategies, and monitoring across multiple environments.

## Table of Contents

- [CI/CD Overview](#cicd-overview)
- [GitHub Actions Setup](#github-actions-setup)
- [Quality Gates](#quality-gates)
- [Security Scanning](#security-scanning)
- [Deployment Strategies](#deployment-strategies)
- [Environment Management](#environment-management)
- [Database Migrations](#database-migrations)
- [Monitoring & Alerts](#monitoring--alerts)
- [Rollback Procedures](#rollback-procedures)
- [Branch Protection](#branch-protection)
- [Secrets Management](#secrets-management)
- [Performance Testing](#performance-testing)
- [Alternative CI Platforms](#alternative-ci-platforms)
- [Troubleshooting](#troubleshooting)

## CI/CD Overview

The getintheq.space platform uses a comprehensive CI/CD pipeline to ensure code quality, security, and reliable deployments across multiple environments.

### Pipeline Architecture

```mermaid
graph TB
    subgraph "Source Control"
        Dev[Developer Push]
        PR[Pull Request]
        Main[Main Branch]
    end

    subgraph "CI Pipeline"
        Lint[Lint & Format]
        Type[Type Check]
        Test[Unit Tests]
        E2E[E2E Tests]
        Security[Security Scan]
        Build[Build Application]
    end

    subgraph "Quality Gates"
        Coverage[Coverage Check]
        Sonar[SonarQube]
        Audit[Dependency Audit]
        Performance[Performance Budget]
    end

    subgraph "CD Pipeline"
        Preview[Preview Deploy]
        Staging[Staging Deploy]
        Production[Production Deploy]
    end

    subgraph "Post-Deploy"
        Smoke[Smoke Tests]
        Monitor[Monitoring]
        Alert[Alerting]
    end

    Dev --> PR
    PR --> Lint
    Lint --> Type
    Type --> Test
    Test --> E2E
    E2E --> Security
    Security --> Build
    Build --> Coverage
    Coverage --> Sonar
    Sonar --> Audit
    Audit --> Performance
    Performance --> Preview
    PR --> Main
    Main --> Staging
    Staging --> Production
    Production --> Smoke
    Smoke --> Monitor
    Monitor --> Alert
```

### Pipeline Stages

1. **Code Quality**: Linting, formatting, type checking
2. **Testing**: Unit tests, integration tests, E2E tests
3. **Security**: Dependency scanning, SAST, container scanning
4. **Build**: Application compilation and optimization
5. **Quality Gates**: Coverage, performance budgets, code analysis
6. **Deployment**: Automated deployment to environments
7. **Verification**: Smoke tests, health checks
8. **Monitoring**: Performance monitoring, alerting

## GitHub Actions Setup

### Main Workflow Configuration

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality & Testing
  quality:
    name: Code Quality & Testing
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # For SonarQube

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: |
          npm run lint:check
          npm run format:check

      - name: Run type checking
        run: npm run type-check

      - name: Run unit tests
        run: npm run test:coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Run E2E tests
        run: npm run test:e2e
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          NEXTAUTH_SECRET: test-secret
          NEXTAUTH_URL: http://localhost:3000

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/lcov.info
          fail_ci_if_error: true

      - name: SonarQube Scan
        uses: sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: quality

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level high

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: Run CodeQL analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript

      - name: Perform CodeQL analysis
        uses: github/codeql-action/analyze@v2

      - name: OWASP ZAP security scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:3000'

  # Build Application
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [quality, security]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production

      - name: Run build analysis
        run: |
          npm run analyze
          npm run bundle-analyzer

      - name: Performance budget check
        run: npm run performance:budget

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push container image
        id: build
        if: github.event_name != 'pull_request'
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            .next/
            public/
            package.json
          retention-days: 7

  # Deploy to Preview (PR only)
  deploy-preview:
    name: Deploy to Preview
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request'
    environment:
      name: preview
      url: ${{ steps.deploy.outputs.url }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Vercel Preview
        id: deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          github-comment: true

      - name: Run smoke tests
        run: |
          npm ci
          npm run test:smoke
        env:
          SMOKE_TEST_URL: ${{ steps.deploy.outputs.url }}

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.getintheq.space

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run database migrations
        run: |
          npm ci
          npm run db:migrate:staging
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}

      - name: Deploy to staging
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod --env staging'

      - name: Run integration tests
        run: npm run test:integration
        env:
          TEST_URL: https://staging.getintheq.space
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}

      - name: Performance testing
        run: npm run test:performance
        env:
          TARGET_URL: https://staging.getintheq.space

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://getintheq.space

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create backup
        run: |
          npm ci
          npm run db:backup:production
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          BACKUP_ENCRYPTION_KEY: ${{ secrets.BACKUP_ENCRYPTION_KEY }}

      - name: Run database migrations
        run: npm run db:migrate:production
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}

      - name: Deploy to production
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

      - name: Run smoke tests
        run: npm run test:smoke
        env:
          SMOKE_TEST_URL: https://getintheq.space

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          text: |
            Production deployment ${{ job.status }}!
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}

  # Security container scan
  container-security:
    name: Container Security Scan
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'

    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'
```

### Deployment Workflow

```yaml
# .github/workflows/deploy.yml
name: Manual Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      run_migrations:
        description: 'Run database migrations'
        required: false
        default: true
        type: boolean
      run_tests:
        description: 'Run tests before deployment'
        required: false
        default: true
        type: boolean

jobs:
  deploy:
    name: Manual Deploy to ${{ github.event.inputs.environment }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        if: github.event.inputs.run_tests == 'true'
        run: |
          npm run test
          npm run test:e2e

      - name: Create backup
        if: github.event.inputs.environment == 'production'
        run: npm run db:backup:production
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}

      - name: Run migrations
        if: github.event.inputs.run_migrations == 'true'
        run: npm run db:migrate:${{ github.event.inputs.environment }}
        env:
          DATABASE_URL: ${{ secrets[format('{0}_DATABASE_URL', upper(github.event.inputs.environment))] }}

      - name: Deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: ${{ github.event.inputs.environment == 'production' && '--prod' || '--env staging' }}

      - name: Post-deployment verification
        run: npm run test:smoke
        env:
          SMOKE_TEST_URL: ${{ github.event.inputs.environment == 'production' && 'https://getintheq.space' || 'https://staging.getintheq.space' }}
```

## Quality Gates

### Code Coverage Requirements

```yaml
# .github/workflows/coverage.yml
name: Code Coverage Check

on:
  pull_request:
    branches: [main, develop]

jobs:
  coverage:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests with coverage
        run: npm run test:coverage
      
      - name: Check coverage thresholds
        run: |
          # Extract coverage percentages
          LINES=$(grep -o 'Lines.*%' coverage/lcov-report/index.html | grep -o '[0-9.]*')
          FUNCTIONS=$(grep -o 'Functions.*%' coverage/lcov-report/index.html | grep -o '[0-9.]*')
          BRANCHES=$(grep -o 'Branches.*%' coverage/lcov-report/index.html | grep -o '[0-9.]*')
          
          # Set minimum thresholds
          MIN_LINES=80
          MIN_FUNCTIONS=80
          MIN_BRANCHES=70
          
          # Check thresholds
          if (( $(echo "$LINES < $MIN_LINES" | bc -l) )); then
            echo "❌ Line coverage $LINES% is below minimum $MIN_LINES%"
            exit 1
          fi
          
          if (( $(echo "$FUNCTIONS < $MIN_FUNCTIONS" | bc -l) )); then
            echo "❌ Function coverage $FUNCTIONS% is below minimum $MIN_FUNCTIONS%"
            exit 1
          fi
          
          if (( $(echo "$BRANCHES < $MIN_BRANCHES" | bc -l) )); then
            echo "❌ Branch coverage $BRANCHES% is below minimum $MIN_BRANCHES%"
            exit 1
          fi
          
          echo "✅ All coverage thresholds met"
          echo "Lines: $LINES% (min: $MIN_LINES%)"
          echo "Functions: $FUNCTIONS% (min: $MIN_FUNCTIONS%)"
          echo "Branches: $BRANCHES% (min: $MIN_BRANCHES%)"
      
      - name: Comment coverage on PR
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: coverage
          message: |
            ## 📊 Code Coverage Report
            
            | Metric | Coverage | Status |
            |--------|----------|--------|
            | Lines | ${{ env.LINES_COVERAGE }}% | ${{ env.LINES_STATUS }} |
            | Functions | ${{ env.FUNCTIONS_COVERAGE }}% | ${{ env.FUNCTIONS_STATUS }} |
            | Branches | ${{ env.BRANCHES_COVERAGE }}% | ${{ env.BRANCHES_STATUS }} |
            
            [View detailed report](https://codecov.io/gh/${{ github.repository }}/pull/${{ github.event.number }})
```

### Performance Budget

```javascript
// scripts/performance-budget.js
const { execSync } = require('child_process');
const path = require('path');

const PERFORMANCE_BUDGETS = {
  // Bundle size limits (in KB)
  bundleSize: {
    main: 250,        // Main bundle
    vendor: 500,      // Vendor bundle
    total: 1000,      // Total bundle size
  },
  
  // Runtime performance
  runtime: {
    fcp: 1500,        // First Contentful Paint (ms)
    lcp: 2500,        // Largest Contentful Paint (ms)
    fid: 100,         // First Input Delay (ms)
    cls: 0.1,         // Cumulative Layout Shift
    ttfb: 600,        // Time to First Byte (ms)
  },
  
  // Lighthouse scores (0-100)
  lighthouse: {
    performance: 90,
    accessibility: 95,
    bestPractices: 90,
    seo: 95,
    pwa: 80,
  }
};

async function checkBundleSize() {
  console.log('📦 Checking bundle size...');
  
  try {
    // Build the application
    execSync('npm run build', { stdio: 'inherit' });
    
    // Analyze bundle sizes
    const bundleAnalyzer = require('@next/bundle-analyzer')({
      enabled: true,
    });
    
    // Get bundle information
    const buildDir = path.join(process.cwd(), '.next');
    const stats = require(path.join(buildDir, 'build-manifest.json'));
    
    let totalSize = 0;
    const bundles = {};
    
    // Calculate bundle sizes
    for (const [name, files] of Object.entries(stats.pages)) {
      let bundleSize = 0;
      for (const file of files) {
        const filePath = path.join(buildDir, 'static', file);
        if (require('fs').existsSync(filePath)) {
          bundleSize += require('fs').statSync(filePath).size;
        }
      }
      bundles[name] = Math.round(bundleSize / 1024); // Convert to KB
      totalSize += bundleSize;
    }
    
    totalSize = Math.round(totalSize / 1024); // Convert to KB
    
    // Check against budgets
    const violations = [];
    
    if (totalSize > PERFORMANCE_BUDGETS.bundleSize.total) {
      violations.push(`Total bundle size ${totalSize}KB exceeds budget ${PERFORMANCE_BUDGETS.bundleSize.total}KB`);
    }
    
    if (bundles['/_app'] > PERFORMANCE_BUDGETS.bundleSize.main) {
      violations.push(`Main bundle size ${bundles['/_app']}KB exceeds budget ${PERFORMANCE_BUDGETS.bundleSize.main}KB`);
    }
    
    if (violations.length > 0) {
      console.error('❌ Bundle size budget violations:');
      violations.forEach(violation => console.error(`  - ${violation}`));
      process.exit(1);
    }
    
    console.log('✅ Bundle size within budget');
    console.log(`Total: ${totalSize}KB (budget: ${PERFORMANCE_BUDGETS.bundleSize.total}KB)`);
    
  } catch (error) {
    console.error('❌ Bundle size check failed:', error.message);
    process.exit(1);
  }
}

async function checkLighthouseScores() {
  console.log('🔍 Running Lighthouse performance check...');
  
  try {
    const lighthouse = require('lighthouse');
    const chromeLauncher = require('chrome-launcher');
    
    const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
    const options = {
      logLevel: 'info',
      output: 'json',
      onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
      port: chrome.port,
    };
    
    const url = process.env.LIGHTHOUSE_URL || 'http://localhost:3000';
    const runnerResult = await lighthouse(url, options);
    
    await chrome.kill();
    
    const scores = {
      performance: Math.round(runnerResult.lhr.categories.performance.score * 100),
      accessibility: Math.round(runnerResult.lhr.categories.accessibility.score * 100),
      bestPractices: Math.round(runnerResult.lhr.categories['best-practices'].score * 100),
      seo: Math.round(runnerResult.lhr.categories.seo.score * 100),
      pwa: Math.round(runnerResult.lhr.categories.pwa.score * 100),
    };
    
    const violations = [];
    
    for (const [category, score] of Object.entries(scores)) {
      const budget = PERFORMANCE_BUDGETS.lighthouse[category];
      if (score < budget) {
        violations.push(`${category}: ${score} (budget: ${budget})`);
      }
    }
    
    if (violations.length > 0) {
      console.error('❌ Lighthouse score budget violations:');
      violations.forEach(violation => console.error(`  - ${violation}`));
      process.exit(1);
    }
    
    console.log('✅ All Lighthouse scores meet budget requirements');
    Object.entries(scores).forEach(([category, score]) => {
      console.log(`  ${category}: ${score}`);
    });
    
  } catch (error) {
    console.error('❌ Lighthouse check failed:', error.message);
    process.exit(1);
  }
}

async function main() {
  const checkType = process.argv[2] || 'all';
  
  try {
    switch (checkType) {
      case 'bundle':
        await checkBundleSize();
        break;
      case 'lighthouse':
        await checkLighthouseScores();
        break;
      case 'all':
      default:
        await checkBundleSize();
        await checkLighthouseScores();
        break;
    }
  } catch (error) {
    console.error('Performance budget check failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  checkBundleSize,
  checkLighthouseScores,
  PERFORMANCE_BUDGETS
};
```

## Security Scanning

### Security Workflow

```yaml
# .github/workflows/security.yml
name: Security Scanning

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    - cron: '0 2 * * 1'  # Weekly security scan

jobs:
  dependency-check:
    name: Dependency Security Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run npm audit
        run: |
          npm audit --audit-level high --json > audit-results.json || true
          
          # Check if there are high/critical vulnerabilities
          HIGH_VULNS=$(jq '.metadata.vulnerabilities.high // 0' audit-results.json)
          CRITICAL_VULNS=$(jq '.metadata.vulnerabilities.critical // 0' audit-results.json)
          
          echo "High vulnerabilities: $HIGH_VULNS"
          echo "Critical vulnerabilities: $CRITICAL_VULNS"
          
          if [ "$HIGH_VULNS" -gt 0 ] || [ "$CRITICAL_VULNS" -gt 0 ]; then
            echo "❌ High or critical vulnerabilities found"
            npm audit --audit-level high
            exit 1
          fi
          
          echo "✅ No high or critical vulnerabilities found"
      
      - name: Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --json > snyk-results.json
      
      - name: Upload Snyk results
        if: always()
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: snyk-results.sarif

  sast-scan:
    name: Static Application Security Testing
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: javascript
          queries: security-extended
      
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
      
      - name: ESLint Security Scan
        run: |
          npm ci
          npx eslint . --ext .js,.jsx,.ts,.tsx --format json --output-file eslint-security.json || true
          
          # Check for security issues
          SECURITY_ISSUES=$(jq '[.[] | select(.messages[] | .ruleId | test("security"))] | length' eslint-security.json)
          
          if [ "$SECURITY_ISSUES" -gt 0 ]; then
            echo "❌ Security issues found in ESLint scan"
            cat eslint-security.json
            exit 1
          fi
          
          echo "✅ No security issues found in ESLint scan"

  secrets-scan:
    name: Secrets Detection
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: TruffleHog secrets scan
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    needs: [dependency-check]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Build Docker image
        run: |
          docker build -t security-scan:latest .
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'security-scan:latest'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
      
      - name: Hadolint Dockerfile scan
        uses: hadolint/hadolint-action@v3.1.0
        with:
          dockerfile: Dockerfile
          failure-threshold: warning

  dast-scan:
    name: Dynamic Application Security Testing
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies and build
        run: |
          npm ci
          npm run build
      
      - name: Start application
        run: |
          npm start &
          sleep 30  # Wait for app to start
        env:
          PORT: 3000
      
      - name: OWASP ZAP security scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:3000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
```

## Environment Management

### Environment Configuration

```typescript
// scripts/ci/environment-setup.ts
interface EnvironmentConfig {
  name: string;
  url: string;
  branch: string;
  databaseUrl: string;
  secrets: Record<string, string>;
  features: string[];
  monitoring: boolean;
  backups: boolean;
}

export const ENVIRONMENTS: Record<string, EnvironmentConfig> = {
  development: {
    name: 'development',
    url: 'http://localhost:3000',
    branch: 'feature/*',
    databaseUrl: 'postgresql://localhost:5432/getintheq_dev',
    secrets: {
      NEXTAUTH_SECRET: 'dev-secret',
      JWT_SECRET: 'dev-jwt-secret',
    },
    features: ['debug', 'dev-tools'],
    monitoring: false,
    backups: false,
  },
  
  preview: {
    name: 'preview',
    url: 'https://preview-*.vercel.app',
    branch: 'feature/*',
    databaseUrl: 'postgresql://preview-db:5432/getintheq_preview',
    secrets: {
      NEXTAUTH_SECRET: 'preview-secret',
      JWT_SECRET: 'preview-jwt-secret',
    },
    features: ['preview-mode'],
    monitoring: true,
    backups: false,
  },
  
  staging: {
    name: 'staging',
    url: 'https://staging.getintheq.space',
    branch: 'develop',
    databaseUrl: 'postgresql://staging-db:5432/getintheq_staging',
    secrets: {
      NEXTAUTH_SECRET: 'staging-secret',
      JWT_SECRET: 'staging-jwt-secret',
    },
    features: ['staging-mode', 'performance-monitoring'],
    monitoring: true,
    backups: true,
  },
  
  production: {
    name: 'production',
    url: 'https://getintheq.space',
    branch: 'main',
    databaseUrl: 'postgresql://prod-db:5432/getintheq_production',
    secrets: {
      NEXTAUTH_SECRET: 'production-secret',
      JWT_SECRET: 'production-jwt-secret',
    },
    features: ['production-mode', 'analytics', 'monitoring'],
    monitoring: true,
    backups: true,
  },
};

export class EnvironmentManager {
  static async validateEnvironment(envName: string): Promise<boolean> {
    const config = ENVIRONMENTS[envName];
    if (!config) {
      throw new Error(`Unknown environment: ${envName}`);
    }

    console.log(`🔍 Validating ${envName} environment...`);

    // Check database connectivity
    try {
      const db = new DatabaseConnection(config.databaseUrl);
      await db.ping();
      console.log('✅ Database connection successful');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      return false;
    }

    // Validate required secrets
    for (const [key, value] of Object.entries(config.secrets)) {
      if (!value) {
        console.error(`❌ Missing required secret: ${key}`);
        return false;
      }
    }

    console.log('✅ Environment validation successful');
    return true;
  }

  static async deployToEnvironment(
    envName: string,
    options: {
      runMigrations?: boolean;
      runTests?: boolean;
      createBackup?: boolean;
    } = {}
  ): Promise<void> {
    const config = ENVIRONMENTS[envName];
    
    console.log(`🚀 Deploying to ${envName} environment...`);

    // Validate environment
    const isValid = await this.validateEnvironment(envName);
    if (!isValid) {
      throw new Error(`Environment validation failed for ${envName}`);
    }

    // Create backup if required
    if (options.createBackup && config.backups) {
      console.log('💾 Creating backup...');
      await this.createBackup(envName);
    }

    // Run migrations if required
    if (options.runMigrations) {
      console.log('🗄️ Running database migrations...');
      await this.runMigrations(envName);
    }

    // Run tests if required
    if (options.runTests) {
      console.log('🧪 Running tests...');
      await this.runTests(envName);
    }

    // Deploy application
    console.log('📦 Deploying application...');
    await this.deployApplication(envName);

    // Run post-deployment verification
    console.log('✅ Running post-deployment verification...');
    await this.verifyDeployment(envName);

    console.log(`🎉 Successfully deployed to ${envName}`);
  }

  private static async createBackup(envName: string): Promise<void> {
    const config = ENVIRONMENTS[envName];
    // Implementation for backup creation
  }

  private static async runMigrations(envName: string): Promise<void> {
    const config = ENVIRONMENTS[envName];
    // Implementation for migration execution
  }

  private static async runTests(envName: string): Promise<void> {
    const config = ENVIRONMENTS[envName];
    // Implementation for test execution
  }

  private static async deployApplication(envName: string): Promise<void> {
    const config = ENVIRONMENTS[envName];
    // Implementation for application deployment
  }

  private static async verifyDeployment(envName: string): Promise<void> {
    const config = ENVIRONMENTS[envName];
    // Implementation for deployment verification
  }
}
```

## Branch Protection

### Branch Protection Rules

```yaml
# .github/branch-protection.yml
# This file defines branch protection rules
# Apply using GitHub CLI: gh api repos/:owner/:repo/branches/main/protection

protection_rules:
  main:
    required_status_checks:
      strict: true
      contexts:
        - "CI/CD Pipeline / Code Quality & Testing"
        - "CI/CD Pipeline / Security Scanning"
        - "CI/CD Pipeline / Build Application"
        - "Security Scanning / Dependency Security Check"
        - "Security Scanning / Static Application Security Testing"
    
    enforce_admins: true
    
    required_pull_request_reviews:
      required_approving_review_count: 2
      dismiss_stale_reviews: true
      require_code_owner_reviews: true
      require_last_push_approval: true
    
    restrictions:
      users: []
      teams: ["maintainers", "senior-developers"]
    
    allow_force_pushes: false
    allow_deletions: false
    block_creations: false

  develop:
    required_status_checks:
      strict: true
      contexts:
        - "CI/CD Pipeline / Code Quality & Testing"
        - "CI/CD Pipeline / Security Scanning"
    
    enforce_admins: false
    
    required_pull_request_reviews:
      required_approving_review_count: 1
      dismiss_stale_reviews: true
      require_code_owner_reviews: false
    
    restrictions: null
    
    allow_force_pushes: false
    allow_deletions: false
```

### Setup Script

```bash
#!/bin/bash
# scripts/ci/setup-branch-protection.sh

set -e

REPO_OWNER="${GITHUB_REPOSITORY_OWNER}"
REPO_NAME="${GITHUB_REPOSITORY#*/}"
TOKEN="${GITHUB_TOKEN}"

echo "🔒 Setting up branch protection rules for ${REPO_OWNER}/${REPO_NAME}"

# Function to set branch protection
set_branch_protection() {
  local branch=$1
  local config_file=$2
  
  echo "Setting protection for branch: $branch"
  
  curl -X PUT \
    -H "Authorization: token $TOKEN" \
    -H "Accept: application/vnd.github.v3+json" \
    "https://api.github.com/repos/${REPO_OWNER}/${REPO_NAME}/branches/${branch}/protection" \
    -d @"$config_file"
}

# Main branch protection
cat > main-protection.json << EOF
{
  "required_status_checks": {
    "strict": true,
    "contexts": [
      "CI/CD Pipeline / Code Quality & Testing",
      "CI/CD Pipeline / Security Scanning",
      "CI/CD Pipeline / Build Application"
    ]
  },
  "enforce_admins": true,
  "required_pull_request_reviews": {
    "required_approving_review_count": 2,
    "dismiss_stale_reviews": true,
    "require_code_owner_reviews": true,
    "require_last_push_approval": true
  },
  "restrictions": {
    "users": [],
    "teams": ["maintainers"]
  },
  "allow_force_pushes": false,
  "allow_deletions": false
}
EOF

# Develop branch protection
cat > develop-protection.json << EOF
{
  "required_status_checks": {
    "strict": true,
    "contexts": [
      "CI/CD Pipeline / Code Quality & Testing",
      "CI/CD Pipeline / Security Scanning"
    ]
  },
  "enforce_admins": false,
  "required_pull_request_reviews": {
    "required_approving_review_count": 1,
    "dismiss_stale_reviews": true,
    "require_code_owner_reviews": false
  },
  "restrictions": null,
  "allow_force_pushes": false,
  "allow_deletions": false
}
EOF

# Apply protection rules
set_branch_protection "main" "main-protection.json"
set_branch_protection "develop" "develop-protection.json"

# Cleanup
rm main-protection.json develop-protection.json

echo "✅ Branch protection rules applied successfully"
```

## Secrets Management

### GitHub Secrets Configuration

```yaml
# Required GitHub Secrets for CI/CD

# Deployment
VERCEL_TOKEN: "vercel_token_here"
VERCEL_ORG_ID: "team_id_here"
VERCEL_PROJECT_ID: "project_id_here"

# Database
PRODUCTION_DATABASE_URL: "********************************/db"
STAGING_DATABASE_URL: "****************************************/db"

# Security
NEXTAUTH_SECRET: "nextauth_secret_here"
JWT_SECRET: "jwt_secret_here"
BACKUP_ENCRYPTION_KEY: "backup_encryption_key_here"

# Third-party services
CODECOV_TOKEN: "codecov_token_here"
SONAR_TOKEN: "sonar_token_here"
SNYK_TOKEN: "snyk_token_here"
SLACK_WEBHOOK: "slack_webhook_url_here"

# Container registry
GHCR_TOKEN: "github_token_here"
DOCKER_USERNAME: "docker_username_here"
DOCKER_PASSWORD: "docker_password_here"
```

### Secrets Validation Script

```typescript
// scripts/ci/validate-secrets.ts
interface RequiredSecret {
  name: string;
  environment: string[];
  pattern?: RegExp;
  description: string;
}

const REQUIRED_SECRETS: RequiredSecret[] = [
  {
    name: 'VERCEL_TOKEN',
    environment: ['staging', 'production'],
    pattern: /^[A-Za-z0-9]{24}$/,
    description: 'Vercel deployment token'
  },
  {
    name: 'DATABASE_URL',
    environment: ['staging', 'production'],
    pattern: /^postgresql:\/\//,
    description: 'PostgreSQL database connection string'
  },
  {
    name: 'NEXTAUTH_SECRET',
    environment: ['staging', 'production'],
    pattern: /^.{32,}$/,
    description: 'NextAuth.js secret key (minimum 32 characters)'
  },
  {
    name: 'JWT_SECRET',
    environment: ['staging', 'production'],
    pattern: /^.{32,}$/,
    description: 'JWT signing secret (minimum 32 characters)'
  }
];

export async function validateSecrets(environment: string): Promise<boolean> {
  console.log(`🔍 Validating secrets for ${environment} environment...`);
  
  let allValid = true;
  
  for (const secret of REQUIRED_SECRETS) {
    if (!secret.environment.includes(environment)) {
      continue;
    }
    
    const value = process.env[secret.name];
    
    if (!value) {
      console.error(`❌ Missing required secret: ${secret.name}`);
      console.error(`   Description: ${secret.description}`);
      allValid = false;
      continue;
    }
    
    if (secret.pattern && !secret.pattern.test(value)) {
      console.error(`❌ Invalid format for secret: ${secret.name}`);
      console.error(`   Description: ${secret.description}`);
      allValid = false;
      continue;
    }
    
    console.log(`✅ ${secret.name}: Valid`);
  }
  
  if (allValid) {
    console.log('✅ All required secrets are valid');
  } else {
    console.error('❌ Secret validation failed');
  }
  
  return allValid;
}

// Usage in CI
if (require.main === module) {
  const environment = process.argv[2] || 'staging';
  
  validateSecrets(environment)
    .then(isValid => {
      if (!isValid) {
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Secret validation error:', error);
      process.exit(1);
    });
}
```

## Rollback Procedures

### Automated Rollback Workflow

```yaml
# .github/workflows/rollback.yml
name: Emergency Rollback

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
          - staging
          - production
      commit_sha:
        description: 'Commit SHA to rollback to (optional - will use previous successful deployment)'
        required: false
        type: string
      reason:
        description: 'Reason for rollback'
        required: true
        type: string

jobs:
  rollback:
    name: Rollback ${{ github.event.inputs.environment }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Determine rollback target
        id: target
        run: |
          if [ -n "${{ github.event.inputs.commit_sha }}" ]; then
            TARGET_SHA="${{ github.event.inputs.commit_sha }}"
          else
            # Get the last successful deployment
            TARGET_SHA=$(gh api repos/${{ github.repository }}/deployments \
              --jq '.[] | select(.environment == "${{ github.event.inputs.environment }}" and .statuses_url) | .sha' \
              | head -1)
          fi
          
          echo "target_sha=$TARGET_SHA" >> $GITHUB_OUTPUT
          echo "Rolling back to: $TARGET_SHA"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Checkout target commit
        run: git checkout ${{ steps.target.outputs.target_sha }}
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Create backup before rollback
        if: github.event.inputs.environment == 'production'
        run: npm run db:backup:production
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      
      - name: Deploy rollback
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: ${{ github.event.inputs.environment == 'production' && '--prod' || '--env staging' }}
      
      - name: Verify rollback
        run: |
          URL="${{ github.event.inputs.environment == 'production' && 'https://getintheq.space' || 'https://staging.getintheq.space' }}"
          
          # Wait for deployment
          sleep 30
          
          # Check if site is responding
          if curl -f -s "$URL" > /dev/null; then
            echo "✅ Rollback successful - site is responding"
          else
            echo "❌ Rollback failed - site is not responding"
            exit 1
          fi
      
      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          text: |
            🚨 EMERGENCY ROLLBACK ${{ job.status }}
            
            Environment: ${{ github.event.inputs.environment }}
            Target: ${{ steps.target.outputs.target_sha }}
            Reason: ${{ github.event.inputs.reason }}
            Triggered by: ${{ github.actor }}
            
            Status: ${{ job.status }}
      
      - name: Create rollback issue
        if: always()
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `Emergency Rollback - ${context.payload.inputs.environment}`,
              body: `
              ## Emergency Rollback Report
              
              **Environment:** ${context.payload.inputs.environment}
              **Target Commit:** ${context.payload.steps.target.outputs.target_sha}
              **Reason:** ${context.payload.inputs.reason}
              **Triggered by:** ${context.actor}
              **Status:** ${context.job.status}
              **Timestamp:** ${new Date().toISOString()}
              
              ### Next Steps
              - [ ] Investigate root cause
              - [ ] Fix underlying issue
              - [ ] Test fix in staging
              - [ ] Plan re-deployment
              - [ ] Update monitoring/alerts if needed
              
              ### Rollback Details
              - Backup created: ${context.payload.inputs.environment === 'production' ? 'Yes' : 'No'}
              - Verification: ${context.job.status === 'success' ? 'Passed' : 'Failed'}
              `,
              labels: ['rollback', 'incident', context.payload.inputs.environment]
            });
```

## Alternative CI Platforms

### GitLab CI Configuration

```yaml
# .gitlab-ci.yml
stages:
  - quality
  - security
  - build
  - deploy

variables:
  NODE_VERSION: "18"
  POSTGRES_DB: test_db
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres

# Code Quality Stage
lint_and_test:
  stage: quality
  image: node:${NODE_VERSION}
  services:
    - postgres:16
  variables:
    DATABASE_URL: ********************************************/test_db
  before_script:
    - npm ci
  script:
    - npm run lint:check
    - npm run type-check
    - npm run test:coverage
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week

# Security Stage
security_scan:
  stage: security
  image: node:${NODE_VERSION}
  before_script:
    - npm ci
  script:
    - npm audit --audit-level high
    - npx snyk test --severity-threshold=high
  allow_failure: false

# Build Stage
build_application:
  stage: build
  image: node:${NODE_VERSION}
  before_script:
    - npm ci
  script:
    - npm run build
    - npm run performance:budget
  artifacts:
    paths:
      - .next/
      - public/
    expire_in: 1 hour

# Deploy Staging
deploy_staging:
  stage: deploy
  image: node:${NODE_VERSION}
  before_script:
    - npm ci
  script:
    - npm run deploy:staging
  environment:
    name: staging
    url: https://staging.getintheq.space
  only:
    - develop

# Deploy Production
deploy_production:
  stage: deploy
  image: node:${NODE_VERSION}
  before_script:
    - npm ci
  script:
    - npm run db:backup:production
    - npm run deploy:production
  environment:
    name: production
    url: https://getintheq.space
  when: manual
  only:
    - main
```

### Azure DevOps Pipeline

```yaml
# azure-pipelines.yml
trigger:
  branches:
    include:
      - main
      - develop

pr:
  branches:
    include:
      - main
      - develop

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '18.x'
  buildConfiguration: 'production'

stages:
- stage: Quality
  displayName: 'Code Quality & Testing'
  jobs:
  - job: QualityCheck
    displayName: 'Quality Check'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - script: npm ci
      displayName: 'Install dependencies'
    
    - script: |
        npm run lint:check
        npm run type-check
      displayName: 'Run linting and type check'
    
    - script: npm run test:coverage
      displayName: 'Run tests with coverage'
    
    - task: PublishCodeCoverageResults@1
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: 'coverage/cobertura-coverage.xml'
        reportDirectory: 'coverage'
      displayName: 'Publish coverage results'

- stage: Security
  displayName: 'Security Scanning'
  dependsOn: Quality
  jobs:
  - job: SecurityScan
    displayName: 'Security Scan'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - script: npm ci
      displayName: 'Install dependencies'
    
    - script: npm audit --audit-level high
      displayName: 'Run npm audit'
    
    - task: SnykSecurityScan@1
      inputs:
        serviceConnectionEndpoint: 'Snyk'
        testType: 'app'
        monitorWhen: 'always'
        failOnIssues: true
      displayName: 'Run Snyk security scan'

- stage: Build
  displayName: 'Build Application'
  dependsOn: [Quality, Security]
  jobs:
  - job: BuildApp
    displayName: 'Build Application'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - script: npm ci
      displayName: 'Install dependencies'
    
    - script: npm run build
      displayName: 'Build application'
      env:
        NODE_ENV: $(buildConfiguration)
    
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '.next'
        artifactName: 'build-output'
      displayName: 'Publish build artifacts'

- stage: Deploy
  displayName: 'Deploy'
  dependsOn: Build
  condition: and(succeeded(), in(variables['Build.SourceBranch'], 'refs/heads/main', 'refs/heads/develop'))
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy Application'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: NodeTool@0
            inputs:
              versionSpec: $(nodeVersion)
            displayName: 'Install Node.js'
          
          - script: |
              npm ci
              npm run deploy
            displayName: 'Deploy application'
            env:
              VERCEL_TOKEN: $(VERCEL_TOKEN)
```

---

**CI/CD Pipeline Guide Version**: 1.0.0  
**Last Updated**: 2024-08-14  
**Pipeline Platform**: GitHub Actions (Primary), GitLab CI, Azure DevOps  
**Next Review**: 2024-11-14  
**DevOps Contact**: <EMAIL>