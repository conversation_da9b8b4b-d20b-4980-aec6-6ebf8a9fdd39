/**
 * Error handling middleware for Cloudflare Workers
 */

import { Context } from 'hono';
import type { Env } from '../index';

export const errorHandler = (err: Error, c: Context<{ Bindings: Env }>) => {
  console.error('API Error:', err);
  
  // Don't expose internal errors in production
  const isDevelopment = c.env.ENVIRONMENT === 'development';
  
  const errorResponse = {
    error: isDevelopment ? err.message : 'Internal server error',
    status_code: 500,
    timestamp: new Date().toISOString(),
    ...(isDevelopment && { stack: err.stack })
  };
  
  return c.json(errorResponse, 500);
};