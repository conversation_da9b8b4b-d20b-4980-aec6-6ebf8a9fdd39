'use client'

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import analytics from '@/lib/analytics-tracker';

export default function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  useEffect(() => {
    // Track page view on route change
    analytics.trackPageView(pathname);
    
    // Track performance metrics
    analytics.trackPerformance();
    
    // Track time on page
    const cleanup = analytics.trackTimeOnPage();
    
    return cleanup;
  }, [pathname]);

  useEffect(() => {
    // Track external link clicks
    const handleLinkClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a');
      
      if (link && link.href) {
        const url = new URL(link.href);
        const currentDomain = window.location.hostname;
        
        // Check if it's an external link
        if (url.hostname !== currentDomain) {
          analytics.trackExternalLinkClick(link.href, link.textContent || undefined);
        }
      }
    };

    // Track file downloads
    const handleDownload = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a');
      
      if (link && link.href) {
        const url = new URL(link.href);
        const pathname = url.pathname;
        
        // Check if it's a file download
        const fileExtensions = ['.pdf', '.doc', '.docx', '.zip', '.rar', '.exe', '.dmg', '.pkg'];
        const isDownload = fileExtensions.some(ext => pathname.toLowerCase().endsWith(ext));
        
        if (isDownload) {
          const fileName = pathname.split('/').pop() || 'unknown';
          const fileType = fileName.split('.').pop() || 'unknown';
          analytics.trackDownload(fileName, fileType);
        }
      }
    };

    document.addEventListener('click', handleLinkClick);
    document.addEventListener('click', handleDownload);

    return () => {
      document.removeEventListener('click', handleLinkClick);
      document.removeEventListener('click', handleDownload);
    };
  }, []);

  return <>{children}</>;
}
