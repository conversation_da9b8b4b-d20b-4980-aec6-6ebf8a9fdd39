'use client'

import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Play, ExternalLink, Brain, BarChart3, ImageIcon, MessageSquare } from 'lucide-react';
import { Github } from 'lucide-react';

interface AIProject {
  id: string;
  title: string;
  description: string;
  category: 'computer-vision' | 'nlp' | 'ml' | 'data-viz';
  technologies: string[];
  demoComponent: React.ComponentType<any>;
  githubUrl: string;
  liveUrl?: string;
  complexity: 'Beginner' | 'Intermediate' | 'Advanced';
  featured: boolean;
}

// Demo Components
const ImageClassifierDemo = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [prediction, setPrediction] = useState<{ label: string; confidence: number } | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const sampleImages = [
    { url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=300&h=300&fit=crop', label: 'Dog' },
    { url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=300&h=300&fit=crop', label: 'Cat' },
    { url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop', label: 'Mountain' },
  ];

  const simulateAnalysis = (imageLabel: string) => {
    setIsAnalyzing(true);
    setTimeout(() => {
      setPrediction({
        label: imageLabel,
        confidence: Math.random() * 0.3 + 0.7 // 70-100% confidence
      });
      setIsAnalyzing(false);
    }, 2000);
  };

  return (
    <div className="space-y-4">
      <h4 className="font-semibold">AI Image Classification Demo</h4>
      <div className="grid grid-cols-3 gap-2">
        {sampleImages.map((img, idx) => (
          <div
            key={idx}
            className={`cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
              selectedImage === img.url ? 'border-primary' : 'border-gray-200'
            }`}
            onClick={() => {
              setSelectedImage(img.url);
              setPrediction(null);
            }}
          >
            <img src={img.url} alt={`Sample ${idx + 1}`} className="w-full h-20 object-cover" />
          </div>
        ))}
      </div>
      
      {selectedImage && (
        <div className="text-center space-y-4">
          <Button 
            onClick={() => {
              const img = sampleImages.find(i => i.url === selectedImage);
              if (img) simulateAnalysis(img.label);
            }}
            disabled={isAnalyzing}
            className="w-full"
          >
            {isAnalyzing ? 'Analyzing...' : 'Classify Image'}
          </Button>
          
          {prediction && (
            <div className="p-4 bg-muted rounded-lg">
              <p className="font-medium">Prediction: {prediction.label}</p>
              <p className="text-sm text-muted-foreground">
                Confidence: {(prediction.confidence * 100).toFixed(1)}%
              </p>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${prediction.confidence * 100}%` }}
                />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const SentimentAnalysisDemo = () => {
  const [text, setText] = useState('');
  const [sentiment, setSentiment] = useState<{ label: string; score: number; confidence: number } | null>(null);

  const analyzeSentiment = (inputText: string) => {
    if (!inputText.trim()) return;
    
    // Simple sentiment analysis simulation
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'fantastic', 'awesome'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'horrible', 'disappointing'];
    
    const words = inputText.toLowerCase().split(/\s+/);
    const positiveCount = words.filter(word => positiveWords.some(pos => word.includes(pos))).length;
    const negativeCount = words.filter(word => negativeWords.some(neg => word.includes(neg))).length;
    
    let label: string;
    let score: number;
    
    if (positiveCount > negativeCount) {
      label = 'Positive';
      score = 0.7 + Math.random() * 0.3;
    } else if (negativeCount > positiveCount) {
      label = 'Negative';
      score = -(0.7 + Math.random() * 0.3);
    } else {
      label = 'Neutral';
      score = (Math.random() - 0.5) * 0.4;
    }
    
    setSentiment({
      label,
      score,
      confidence: 0.8 + Math.random() * 0.2
    });
  };

  return (
    <div className="space-y-4">
      <h4 className="font-semibold">Sentiment Analysis Demo</h4>
      <textarea
        className="w-full p-3 border rounded-lg resize-none"
        rows={3}
        placeholder="Enter text to analyze sentiment..."
        value={text}
        onChange={(e) => setText(e.target.value)}
      />
      <Button 
        onClick={() => analyzeSentiment(text)}
        disabled={!text.trim()}
        className="w-full"
      >
        Analyze Sentiment
      </Button>
      
      {sentiment && (
        <div className="p-4 bg-muted rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium">Sentiment: {sentiment.label}</span>
            <Badge variant={sentiment.label === 'Positive' ? 'default' : sentiment.label === 'Negative' ? 'destructive' : 'secondary'}>
              {(sentiment.confidence * 100).toFixed(1)}% confident
            </Badge>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-1000 ${
                sentiment.score > 0 ? 'bg-green-500' : sentiment.score < 0 ? 'bg-red-500' : 'bg-yellow-500'
              }`}
              style={{ 
                width: `${Math.abs(sentiment.score) * 100}%`,
                marginLeft: sentiment.score < 0 ? `${(1 + sentiment.score) * 100}%` : '0'
              }}
            />
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Score: {sentiment.score > 0 ? '+' : ''}{sentiment.score.toFixed(2)}
          </p>
        </div>
      )}
    </div>
  );
};

const DataVisualizationDemo = () => {
  const [dataSize, setDataSize] = useState([100]);
  const [chartType, setChartType] = useState('bar');

  // Generate sample data
  const generateData = (size: number) => {
    const data = Array.from({ length: size }, (_, i) => ({
      x: i,
      y: Math.floor(Math.random() * 100) + 1,
      category: ['A', 'B', 'C'][Math.floor(Math.random() * 3)]
    }));
    console.log('Generated data:', data); // Use the data variable
    return data;
  };

  return (
    <div className="space-y-4">
      <h4 className="font-semibold">Interactive Data Visualization</h4>
      <div className="space-y-2">
        <label className="text-sm font-medium">Data Points: {dataSize[0]}</label>
        <Slider
          value={dataSize}
          onValueChange={setDataSize}
          max={200}
          min={10}
          step={10}
          className="w-full"
        />
      </div>
      
      <div className="flex gap-2">
        <Button
          variant={chartType === 'bar' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setChartType('bar')}
        >
          Bar Chart
        </Button>
        <Button
          variant={chartType === 'line' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setChartType('line')}
        >
          Line Chart
        </Button>
      </div>
      
      <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">
            {chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart with {dataSize[0]} data points
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Interactive visualization would be rendered here
          </p>
        </div>
      </div>
    </div>
  );
};

const MLModelDemo = () => {
  const [features, setFeatures] = useState({
    feature1: [50],
    feature2: [30],
    feature3: [70]
  });
  const [prediction, setPrediction] = useState<number | null>(null);

  const predict = () => {
    // Simple linear combination for demo
    const result = (features.feature1[0] * 0.3 + features.feature2[0] * 0.4 + features.feature3[0] * 0.3) / 100;
    setPrediction(result);
  };

  return (
    <div className="space-y-4">
      <h4 className="font-semibold">ML Model Prediction Demo</h4>
      
      {Object.entries(features).map(([key, value]) => (
        <div key={key} className="space-y-2">
          <label className="text-sm font-medium">
            {key.replace('feature', 'Feature ')}: {value[0]}
          </label>
          <Slider
            value={value}
            onValueChange={(newValue) => setFeatures(prev => ({ ...prev, [key]: newValue }))}
            max={100}
            min={0}
            step={1}
            className="w-full"
          />
        </div>
      ))}
      
      <Button onClick={predict} className="w-full">
        Generate Prediction
      </Button>
      
      {prediction !== null && (
        <div className="p-4 bg-muted rounded-lg text-center">
          <p className="font-medium">Prediction: {(prediction * 100).toFixed(1)}%</p>
          <div className="w-full bg-gray-200 rounded-full h-3 mt-2">
            <div 
              className="bg-primary h-3 rounded-full transition-all duration-1000"
              style={{ width: `${prediction * 100}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

const aiProjects: AIProject[] = [
  {
    id: '1',
    title: 'AI Image Classification System',
    description: 'Deep learning model for real-time image classification using CNNs with 95% accuracy on ImageNet dataset.',
    category: 'computer-vision',
    technologies: ['PyTorch', 'OpenCV', 'FastAPI', 'React'],
    demoComponent: ImageClassifierDemo,
    githubUrl: 'https://github.com/khiwniti/ai-image-classifier',
    liveUrl: 'https://ai-classifier-demo.vercel.app',
    complexity: 'Advanced',
    featured: true
  },
  {
    id: '2',
    title: 'Natural Language Sentiment Analyzer',
    description: 'BERT-based sentiment analysis model for real-time text classification with emotional context understanding.',
    category: 'nlp',
    technologies: ['Transformers', 'BERT', 'Python', 'Streamlit'],
    demoComponent: SentimentAnalysisDemo,
    githubUrl: 'https://github.com/khiwniti/sentiment-analyzer',
    complexity: 'Intermediate',
    featured: true
  },
  {
    id: '3',
    title: 'Interactive Data Visualization Platform',
    description: 'Dynamic data visualization tool with real-time updates and interactive charts for exploratory data analysis.',
    category: 'data-viz',
    technologies: ['D3.js', 'React', 'Node.js', 'WebSocket'],
    demoComponent: DataVisualizationDemo,
    githubUrl: 'https://github.com/khiwniti/data-viz-platform',
    liveUrl: 'https://data-viz-demo.vercel.app',
    complexity: 'Intermediate',
    featured: false
  },
  {
    id: '4',
    title: 'Predictive Analytics ML Pipeline',
    description: 'End-to-end machine learning pipeline for predictive analytics with automated feature engineering and model selection.',
    category: 'ml',
    technologies: ['Scikit-learn', 'Pandas', 'MLflow', 'Docker'],
    demoComponent: MLModelDemo,
    githubUrl: 'https://github.com/khiwniti/predictive-analytics',
    complexity: 'Advanced',
    featured: false
  }
];

export default function AIProjectsShowcase() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', label: 'All Projects', icon: Brain },
    { id: 'computer-vision', label: 'Computer Vision', icon: ImageIcon },
    { id: 'nlp', label: 'Natural Language', icon: MessageSquare },
    { id: 'ml', label: 'Machine Learning', icon: Brain },
    { id: 'data-viz', label: 'Data Visualization', icon: BarChart3 }
  ];

  const filteredProjects = selectedCategory === 'all' 
    ? aiProjects 
    : aiProjects.filter(project => project.category === selectedCategory);

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'Advanced': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6 gradient-text">
              AI/ML Project Showcase
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Interactive demonstrations of cutting-edge artificial intelligence and machine learning projects
            </p>
          </motion.div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map(category => {
              const IconComponent = category.icon;
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center gap-2"
                >
                  <IconComponent className="h-4 w-4" />
                  {category.label}
                </Button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-8">
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-xl mb-2">{project.title}</CardTitle>
                        <p className="text-muted-foreground text-sm mb-4">{project.description}</p>
                      </div>
                      {project.featured && (
                        <Badge className="bg-accent text-accent-foreground">Featured</Badge>
                      )}
                    </div>
                    
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.map(tech => (
                        <Badge key={tech} variant="outline">{tech}</Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Badge className={getComplexityColor(project.complexity)}>
                        {project.complexity}
                      </Badge>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(project.githubUrl, '_blank')}
                        >
                          <Github className="h-3 w-3 mr-1" />
                          Code
                        </Button>
                        {project.liveUrl && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(project.liveUrl, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            Live
                          </Button>
                        )}
                        <Button
                          size="sm"
                          onClick={() => {
                            // Demo functionality would go here
                            console.log('Demo for project:', project.title);
                          }}
                        >
                          <Play className="h-3 w-3 mr-1" />
                          Demo
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <Tabs defaultValue="demo" className="w-full">
                      <TabsList className="grid w-full grid-cols-1">
                        <TabsTrigger value="demo">Interactive Demo</TabsTrigger>
                      </TabsList>
                      <TabsContent value="demo" className="mt-4">
                        <project.demoComponent />
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}