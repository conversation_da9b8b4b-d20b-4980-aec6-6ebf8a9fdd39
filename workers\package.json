{"name": "portfolio-api-workers", "version": "1.0.0", "description": "Portfolio API for Cloudflare Workers", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "db:generate": "drizzle-kit generate", "db:migrate:local": "wrangler d1 migrations apply portfolio-db --local", "db:migrate:staging": "wrangler d1 migrations apply portfolio-db-staging --env staging", "db:migrate:production": "wrangler d1 migrations apply portfolio-db-prod --env production"}, "keywords": ["cloudflare", "workers", "api", "portfolio"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@cloudflare/workers-types": "^4.20241231.0", "@types/node": "^20.10.0", "drizzle-kit": "^0.30.4", "typescript": "^5.3.3", "wrangler": "^4.29.0"}, "dependencies": {"drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "hono": "^4.9.1", "zod": "^3.22.4"}}