import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const githubToken = process.env.GITHUB_TOKEN;
    const username = process.env.GITHUB_USERNAME || 'khiwniti';
    
    if (!githubToken) {
      return NextResponse.json({ error: "GitHub token not configured" }, { status: 500 });
    }

    const headers = {
      'Authorization': `token ${githubToken}`,
      'Accept': 'application/vnd.github.v3+json'
    };

    const response = await fetch(`https://api.github.com/users/${username}/repos?sort=updated&per_page=20`, { headers });
    
    if (!response.ok) {
      throw new Error(`GitHub API returned ${response.status}: ${response.statusText}`);
    }
    
    const repos = await response.json();
    
    // Check if repos is an array
    if (!Array.isArray(repos)) {
      throw new Error('GitHub API did not return an array of repositories');
    }

    // Filter and transform repos for portfolio display
    const portfolioRepos = repos
      .filter((repo: any) => !repo.fork && repo.stargazers_count >= 0) // Include all non-fork repos
      .slice(0, 6) // Limit to 6 most recent
      .map((repo: any) => ({
        id: repo.id.toString(),
        title: repo.name.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
        description: repo.description || 'No description available',
        technologies: repo.language ? [repo.language] : ['JavaScript'], // Basic tech stack
        githubUrl: repo.html_url,
        demoUrl: repo.homepage || null,
        imageUrl: `https://opengraph.githubassets.com/1/${username}/${repo.name}`,
        stars: repo.stargazers_count,
        forks: repo.forks_count,
        language: repo.language,
        updated_at: repo.updated_at
      }));

    return NextResponse.json(portfolioRepos);
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch GitHub repositories" }, { status: 500 });
  }
}
