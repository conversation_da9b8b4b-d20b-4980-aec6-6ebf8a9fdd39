'use client'

import { motion } from 'framer-motion';
import { Star, Quote, ExternalLink } from 'lucide-react';
import { Linked<PERSON> } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar: string;
  linkedinUrl?: string;
  relationship: string;
  date: string;
}

const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'VP of Engineering',
    company: 'TechCorp Inc.',
    content: '<PERSON><PERSON><PERSON> is an exceptional engineer with deep expertise in machine learning and data engineering. During our time working together, he consistently delivered high-quality solutions that had significant business impact. His ability to translate complex technical concepts into actionable business outcomes is remarkable.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    linkedinUrl: 'https://linkedin.com/in/sarah-johnson',
    relationship: '<PERSON> was <PERSON><PERSON><PERSON>\'s direct manager',
    date: '2023'
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Principal Data Scientist',
    company: 'DataTech Solutions',
    content: 'Working with Khiw on our ML platform was incredible. His technical depth in both infrastructure and algorithms is rare. He not only builds robust systems but also mentors team members effectively. The recommendation system he architected serves millions of users daily with 99.9% uptime.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    linkedinUrl: 'https://linkedin.com/in/michael-chen',
    relationship: 'Michael worked with Khiw on the same team',
    date: '2022'
  },
  {
    id: '3',
    name: 'Dr. Emily Rodriguez',
    role: 'Research Director',
    company: 'AI Research Institute',
    content: 'Khiw\'s research contributions during his graduate studies were outstanding. His work on deep learning optimization has been cited extensively and demonstrates both theoretical understanding and practical application. He\'s one of the most promising engineers I\'ve had the pleasure to mentor.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    linkedinUrl: 'https://linkedin.com/in/emily-rodriguez-phd',
    relationship: 'Emily was Khiw\'s academic advisor',
    date: '2019'
  },
  {
    id: '4',
    name: 'James Wilson',
    role: 'Product Manager',
    company: 'TechCorp Inc.',
    content: 'Khiw bridges the gap between complex AI/ML technology and practical product needs beautifully. He helped us launch three major ML-powered features that increased user engagement by 40%. His collaborative approach and clear communication make him invaluable to any product team.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
    linkedinUrl: 'https://linkedin.com/in/james-wilson-pm',
    relationship: 'James worked with Khiw on different teams',
    date: '2023'
  },
  {
    id: '5',
    name: 'Lisa Park',
    role: 'Senior Software Engineer',
    company: 'StartupXYZ',
    content: 'As a junior developer, I learned tremendously from Khiw\'s mentorship. He has a gift for explaining complex concepts clearly and always makes time to help others grow. The code review feedback and architectural guidance he provided helped me advance my career significantly.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    linkedinUrl: 'https://linkedin.com/in/lisa-park-dev',
    relationship: 'Khiw was Lisa\'s mentor',
    date: '2020'
  },
  {
    id: '6',
    name: 'Alex Thompson',
    role: 'CTO',
    company: 'DataTech Solutions',
    content: 'Khiw joined our team during a critical scaling phase and immediately made an impact. His expertise in MLOps and system architecture helped us handle 10x growth in data volume without missing a beat. He\'s the kind of engineer every startup needs during rapid growth phases.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    linkedinUrl: 'https://linkedin.com/in/alex-thompson-cto',
    relationship: 'Alex was Khiw\'s senior leader',
    date: '2022'
  }
];

const stats = [
  { label: 'Recommendations', value: '25+' },
  { label: 'Average Rating', value: '4.9/5' },
  { label: 'Years of Experience', value: '5+' },
  { label: 'Projects Delivered', value: '50+' }
];

export default function TestimonialsSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section id="testimonials" className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">
            What People Say
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Recommendations and testimonials from colleagues, managers, and collaborators
          </p>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-2xl md:text-3xl font-bold text-primary mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {testimonials.map((testimonial) => (
            <motion.div key={testimonial.id} variants={itemVariants}>
              <Card className="h-full hover:shadow-lg transition-all duration-300 group">
                <CardContent className="p-6">
                  {/* Quote Icon */}
                  <Quote className="h-8 w-8 text-primary/20 mb-4" />
                  
                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-4">
                    {renderStars(testimonial.rating)}
                  </div>
                  
                  {/* Content */}
                  <blockquote className="text-muted-foreground leading-relaxed mb-6 line-clamp-6">
                    "{testimonial.content}"
                  </blockquote>
                  
                  {/* Author */}
                  <div className="flex items-start gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                      <AvatarFallback>
                        {testimonial.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-semibold truncate">{testimonial.name}</h4>
                        {testimonial.linkedinUrl && (
                          <a
                            href={testimonial.linkedinUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:text-primary/80 transition-colors"
                          >
                            <Linkedin className="h-3 w-3" />
                          </a>
                        )}
                      </div>
                      <p className="text-sm text-primary font-medium mb-1">
                        {testimonial.role}
                      </p>
                      <p className="text-sm text-muted-foreground mb-2">
                        {testimonial.company}
                      </p>
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {testimonial.date}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2 italic">
                        {testimonial.relationship}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">
              Want to work together?
            </h3>
            <p className="text-muted-foreground mb-6">
              I'm always interested in new opportunities and collaborations. 
              Let's discuss how we can create something amazing together.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="#contact"
                className="px-8 py-3 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors inline-flex items-center justify-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Get In Touch
              </motion.a>
              <motion.a
                href="https://linkedin.com/in/khiwniti"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-3 border border-primary text-primary rounded-full hover:bg-primary hover:text-primary-foreground transition-colors inline-flex items-center justify-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Linkedin className="h-4 w-4" />
                Connect on LinkedIn
                <ExternalLink className="h-3 w-3" />
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}