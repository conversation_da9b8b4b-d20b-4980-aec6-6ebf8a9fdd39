import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { AuthService, SessionService, RateLimiter } from '@/lib/auth';
import { RequestTimer, PerformanceMonitor } from '@/lib/performance';
import { SecurityHeaders, IPUtils, InputSanitizer, PasswordValidator } from '@/lib/security';

const loginRequestSchema = z.object({
  email: z.string().email('Invalid email address').max(100, 'Email too long'),
  password: z.string().min(6, 'Password must be at least 6 characters').max(128, 'Password too long'),
  rememberMe: z.boolean().optional().default(false),
});

export async function POST(request: NextRequest): Promise<NextResponse> {
  const timer = new RequestTimer();
  const clientIP = IPUtils.getRealIP(request.headers);
  const userAgent = request.headers.get('user-agent') || '';

  try {
    // Parse and validate JSON body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.warn(`Invalid JSON in login request from IP: ${clientIP}`);
      return NextResponse.json(
        { error: 'Invalid request format' },
        {
          status: 400,
          headers: SecurityHeaders.getApiHeaders()
        }
      );
    }

    // Validate input using Zod schema
    const validationResult = loginRequestSchema.safeParse(body);
    if (!validationResult.success) {
      console.warn(`Login validation failed from IP: ${clientIP}:`, validationResult.error.issues);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.issues.map(issue => ({
            field: issue.path.join('.'),
            message: issue.message
          }))
        },
        {
          status: 400,
          headers: SecurityHeaders.getApiHeaders()
        }
      );
    }

    const { email, password, rememberMe } = validationResult.data;

    // Sanitize email input
    const sanitizedEmail = InputSanitizer.sanitizeEmail(email);

    // Additional password validation for security
    const passwordValidation = PasswordValidator.validate(password);
    if (!passwordValidation.isValid && process.env.NODE_ENV === 'production') {
      // In production, enforce strong passwords for new accounts
      // For existing accounts, this would be handled during password reset/change
      console.warn(`Weak password attempt for ${sanitizedEmail} from IP: ${clientIP}`);
    }

    // Rate limiting with more sophisticated key
    const rateLimitKey = `login:${sanitizedEmail}:${clientIP}`;
    if (RateLimiter.isRateLimited(rateLimitKey)) {
      console.warn(`Rate limit exceeded for login attempts: ${sanitizedEmail} from IP: ${clientIP}`);
      
      // Record failed attempt metrics
      PerformanceMonitor.recordMetrics({
        requestDuration: timer.end(),
        url: '/api/auth/login',
        userAgent,
      });

      return NextResponse.json(
        { error: 'Too many login attempts. Please try again later.' },
        {
          status: 429,
          headers: {
            ...SecurityHeaders.getApiHeaders(),
            'Retry-After': '900' // 15 minutes
          }
        }
      );
    }

    // Attempt login with performance tracking
    const loginStartTime = performance.now();
    const result = await AuthService.login(sanitizedEmail, password);
    const loginDuration = performance.now() - loginStartTime;

    if (!result) {
      console.warn(`Failed login attempt for ${sanitizedEmail} from IP: ${clientIP}`);
      
      // Record failed login metrics
      PerformanceMonitor.recordMetrics({
        requestDuration: timer.end(),
        url: '/api/auth/login:failed',
        userAgent,
      });

      // Add artificial delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 500));

      return NextResponse.json(
        { error: 'Invalid email or password' },
        {
          status: 401,
          headers: SecurityHeaders.getApiHeaders()
        }
      );
    }

    // Clear rate limit on successful login
    RateLimiter.clearAttempts(rateLimitKey);

    // Log successful login
    console.log(`Successful login for ${sanitizedEmail} from IP: ${clientIP}`);

    // Record performance metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: timer.end(),
      url: '/api/auth/login:success',
      userAgent,
    });

    // Create response with user data (excluding sensitive info)
    const responseData = {
      success: true,
      user: {
        id: result.user.id,
        email: result.user.email,
        role: result.user.role,
        // Don't include sensitive fields
      },
      // Don't include token in response body for security
    };

    const response = NextResponse.json(responseData, {
      headers: SecurityHeaders.getApiHeaders()
    });

    // Set secure HTTP-only cookie with enhanced security
    const cookieMaxAge = rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60; // 30 days or 1 day
    const secureCookieValue = `${result.token}; HttpOnly; Secure=${process.env.NODE_ENV === 'production'}; SameSite=Strict; Max-Age=${cookieMaxAge}; Path=/`;
    
    response.headers.set('Set-Cookie', SessionService.setAuthCookie(result.token));
    // Override with our enhanced cookie settings
    response.headers.set('Set-Cookie', `auth-token=${secureCookieValue}`);

    // Add additional security headers for auth endpoint
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');

    return response;
  } catch (error) {
    const duration = timer.end();
    
    // Record error metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: duration,
      url: '/api/auth/login:error',
      userAgent,
    });

    console.error(`Login error from IP: ${clientIP}:`, error);
    
    // Don't leak internal error details
    const errorMessage = process.env.NODE_ENV === 'development'
      ? `Internal error: ${(error as Error).message}`
      : 'Authentication service temporarily unavailable';

    return NextResponse.json(
      { error: errorMessage },
      {
        status: 500,
        headers: SecurityHeaders.getApiHeaders()
      }
    );
  }
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  const timer = new RequestTimer();
  const clientIP = IPUtils.getRealIP(request.headers);
  const userAgent = request.headers.get('user-agent') || '';

  try {
    // Check if user is already authenticated
    const user = await AuthService.getUserFromRequest(request);
    
    // Record performance metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: timer.end(),
      url: '/api/auth/login:check',
      userAgent,
    });
    
    if (user) {
      // Log auth check
      console.log(`Auth check successful for user ${user.id} from IP: ${clientIP}`);
      
      return NextResponse.json(
        {
          authenticated: true,
          user: {
            id: user.id,
            email: user.email,
            role: user.role,
          }
        },
        {
          headers: SecurityHeaders.getApiHeaders()
        }
      );
    }

    return NextResponse.json(
      { authenticated: false },
      {
        headers: SecurityHeaders.getApiHeaders()
      }
    );
  } catch (error) {
    const duration = timer.end();
    
    // Record error metrics
    PerformanceMonitor.recordMetrics({
      requestDuration: duration,
      url: '/api/auth/login:check:error',
      userAgent,
    });

    console.error(`Auth check error from IP: ${clientIP}:`, error);
    
    return NextResponse.json(
      { error: 'Authentication check failed' },
      {
        status: 500,
        headers: SecurityHeaders.getApiHeaders()
      }
    );
  }
}
