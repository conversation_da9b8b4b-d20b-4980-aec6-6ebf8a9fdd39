'use client'

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Save, 
  Globe, 
  Mail, 
  Github, 
  Linkedin, 
  Twitter,
  Shield,
  Database,
  Palette
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

interface SettingsData {
  [key: string]: {
    value: any;
    type: string;
    description?: string;
  };
}

export default function SettingsPage() {
  const { toast } = useToast();
  const [settings, setSettings] = useState<SettingsData>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      const data = await response.json();
      setSettings(data);
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load settings',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateSetting = (key: string, value: any, type: string = 'string') => {
    setSettings(prev => ({
      ...prev,
      [key]: { value, type }
    }));
  };

  const saveSettings = async () => {
    setIsSaving(true);
    try {
      const settingsArray = Object.entries(settings).map(([key, setting]) => ({
        key,
        value: setting.value,
        type: setting.type
      }));

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: settingsArray }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Settings saved successfully',
        });
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Settings</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[1, 2, 3, 4].map(i => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Configure your portfolio and admin preferences
          </p>
        </div>
        <Button 
          onClick={saveSettings}
          disabled={isSaving}
          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
        >
          {isSaving ? (
            <>
              <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save Settings
            </>
          )}
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="social">Social Links</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Site Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="site_title">Site Title</Label>
                  <Input
                    id="site_title"
                    value={settings.site_title?.value || ''}
                    onChange={(e) => updateSetting('site_title', e.target.value)}
                    placeholder="Your Portfolio"
                  />
                </div>

                <div>
                  <Label htmlFor="site_description">Site Description</Label>
                  <Textarea
                    id="site_description"
                    value={settings.site_description?.value || ''}
                    onChange={(e) => updateSetting('site_description', e.target.value)}
                    placeholder="A brief description of your portfolio"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="site_url">Site URL</Label>
                  <Input
                    id="site_url"
                    value={settings.site_url?.value || ''}
                    onChange={(e) => updateSetting('site_url', e.target.value)}
                    placeholder="https://yourportfolio.com"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="contact_email">Contact Email</Label>
                  <Input
                    id="contact_email"
                    type="email"
                    value={settings.contact_email?.value || ''}
                    onChange={(e) => updateSetting('contact_email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="admin_email">Admin Email</Label>
                  <Input
                    id="admin_email"
                    type="email"
                    value={settings.admin_email?.value || ''}
                    onChange={(e) => updateSetting('admin_email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="contact_form_enabled">Enable Contact Form</Label>
                  <Switch
                    id="contact_form_enabled"
                    checked={settings.contact_form_enabled?.value || false}
                    onCheckedChange={(checked) => updateSetting('contact_form_enabled', checked, 'boolean')}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="social">
          <Card>
            <CardHeader>
              <CardTitle>Social Media Links</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="github_url" className="flex items-center gap-2">
                    <Github className="w-4 h-4" />
                    GitHub URL
                  </Label>
                  <Input
                    id="github_url"
                    value={settings.github_url?.value || ''}
                    onChange={(e) => updateSetting('github_url', e.target.value)}
                    placeholder="https://github.com/username"
                  />
                </div>

                <div>
                  <Label htmlFor="linkedin_url" className="flex items-center gap-2">
                    <Linkedin className="w-4 h-4" />
                    LinkedIn URL
                  </Label>
                  <Input
                    id="linkedin_url"
                    value={settings.linkedin_url?.value || ''}
                    onChange={(e) => updateSetting('linkedin_url', e.target.value)}
                    placeholder="https://linkedin.com/in/username"
                  />
                </div>

                <div>
                  <Label htmlFor="twitter_url" className="flex items-center gap-2">
                    <Twitter className="w-4 h-4" />
                    Twitter URL
                  </Label>
                  <Input
                    id="twitter_url"
                    value={settings.twitter_url?.value || ''}
                    onChange={(e) => updateSetting('twitter_url', e.target.value)}
                    placeholder="https://twitter.com/username"
                  />
                </div>

                <div>
                  <Label htmlFor="portfolio_url">Portfolio URL</Label>
                  <Input
                    id="portfolio_url"
                    value={settings.portfolio_url?.value || ''}
                    onChange={(e) => updateSetting('portfolio_url', e.target.value)}
                    placeholder="https://yourportfolio.com"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Analytics Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable Analytics Tracking</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Track page views and user interactions
                  </p>
                </div>
                <Switch
                  checked={settings.analytics_enabled?.value || false}
                  onCheckedChange={(checked) => updateSetting('analytics_enabled', checked, 'boolean')}
                />
              </div>

              <div>
                <Label htmlFor="google_analytics_id">Google Analytics ID</Label>
                <Input
                  id="google_analytics_id"
                  value={settings.google_analytics_id?.value || ''}
                  onChange={(e) => updateSetting('google_analytics_id', e.target.value)}
                  placeholder="GA-XXXXXXXXX-X"
                />
              </div>

              <div>
                <Label htmlFor="analytics_retention_days">Data Retention (Days)</Label>
                <Input
                  id="analytics_retention_days"
                  type="number"
                  value={settings.analytics_retention_days?.value || 365}
                  onChange={(e) => updateSetting('analytics_retention_days', parseInt(e.target.value), 'number')}
                  placeholder="365"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Require HTTPS</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Force HTTPS redirects for all requests
                  </p>
                </div>
                <Switch
                  checked={settings.force_https?.value || false}
                  onCheckedChange={(checked) => updateSetting('force_https', checked, 'boolean')}
                />
              </div>

              <div>
                <Label htmlFor="session_timeout">Session Timeout (Hours)</Label>
                <Input
                  id="session_timeout"
                  type="number"
                  value={settings.session_timeout?.value || 24}
                  onChange={(e) => updateSetting('session_timeout', parseInt(e.target.value), 'number')}
                  placeholder="24"
                />
              </div>

              <div>
                <Label htmlFor="max_login_attempts">Max Login Attempts</Label>
                <Input
                  id="max_login_attempts"
                  type="number"
                  value={settings.max_login_attempts?.value || 5}
                  onChange={(e) => updateSetting('max_login_attempts', parseInt(e.target.value), 'number')}
                  placeholder="5"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable Rate Limiting</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Protect against brute force attacks
                  </p>
                </div>
                <Switch
                  checked={settings.rate_limiting_enabled?.value || true}
                  onCheckedChange={(checked) => updateSetting('rate_limiting_enabled', checked, 'boolean')}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
